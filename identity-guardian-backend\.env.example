# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/identity_guardian
DATABASE_URL_TEST=postgresql://username:password@localhost:5432/identity_guardian_test

# Flask Configuration
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here
FLASK_ENV=development

# External API Keys
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>

HIBP_API_KEY=your-haveibeenpwned-api-key
NUMVERIFY_API_KEY=your-numverify-api-key
HUNTER_API_KEY=your-hunter-io-api-key

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# Security Settings
BCRYPT_LOG_ROUNDS=12
JWT_ACCESS_TOKEN_EXPIRES=3600
JWT_REFRESH_TOKEN_EXPIRES=2592000

# Application Settings
APP_NAME=Identity Guardian
APP_VERSION=1.0.0
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

