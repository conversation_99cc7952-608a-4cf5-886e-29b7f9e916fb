import React, { useState } from 'react';
import Layout from '../components/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { But<PERSON> } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../components/ui/tabs';
import { Alert, AlertDescription } from '../components/ui/alert';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Eye, 
  Bell,
  Shield,
  TrendingUp,
  Download,
  Filter,
  Calendar,
  Globe,
  Smartphone,
  Mail
} from 'lucide-react';

const MonitoringPage = () => {
  const [alerts] = useState([
    {
      id: 1,
      type: 'breach',
      severity: 'critical',
      title: 'New Data Breach Detected',
      description: '<NAME_EMAIL> was found in the recent LinkedIn breach affecting 700M users',
      identity: '<EMAIL>',
      identityType: 'email',
      timestamp: '2024-01-15T10:30:00Z',
      source: 'LinkedIn',
      status: 'unread',
      actionRequired: true,
      recommendations: [
        'Change your LinkedIn password immediately',
        'Enable two-factor authentication',
        'Monitor your account for suspicious activity'
      ]
    },
    {
      id: 2,
      type: 'suspicious_activity',
      severity: 'high',
      title: 'Suspicious Login Attempt',
      description: 'Multiple failed login attempts detected for your monitored email address',
      identity: '<EMAIL>',
      identityType: 'email',
      timestamp: '2024-01-14T15:45:00Z',
      source: 'Security Monitoring',
      status: 'acknowledged',
      actionRequired: true,
      recommendations: [
        'Review recent login activity',
        'Change password if you suspect compromise',
        'Enable account alerts'
      ]
    },
    {
      id: 3,
      type: 'identity_exposure',
      severity: 'medium',
      title: 'Phone Number Found on Dark Web',
      description: 'Your phone number +**************** was found in underground forums',
      identity: '+****************',
      identityType: 'phone',
      timestamp: '2024-01-13T09:20:00Z',
      source: 'Dark Web Monitoring',
      status: 'read',
      actionRequired: false,
      recommendations: [
        'Be cautious of unexpected calls or texts',
        'Consider changing your phone number if harassment occurs',
        'Report suspicious communications'
      ]
    },
    {
      id: 4,
      type: 'password_compromise',
      severity: 'high',
      title: 'Password Found in Breach Database',
      description: 'A password associated with your email was found in a known breach database',
      identity: '<EMAIL>',
      identityType: 'email',
      timestamp: '2024-01-12T14:10:00Z',
      source: 'HaveIBeenPwned',
      status: 'resolved',
      actionRequired: false,
      recommendations: [
        'Password has been changed',
        'Continue monitoring for suspicious activity',
        'Use unique passwords for all accounts'
      ]
    },
    {
      id: 5,
      type: 'security_tip',
      severity: 'low',
      title: 'Weekly Security Reminder',
      description: 'Time for your weekly security check-up and password review',
      identity: null,
      identityType: null,
      timestamp: '2024-01-11T08:00:00Z',
      source: 'Identity Guardian',
      status: 'read',
      actionRequired: false,
      recommendations: [
        'Review your security dashboard',
        'Check for software updates',
        'Review recent account activity'
      ]
    }
  ]);

  const [monitoringStats] = useState({
    totalScans: 1247,
    identitiesMonitored: 5,
    breachesDetected: 3,
    alertsThisWeek: 7,
    lastScanTime: '2 hours ago',
    uptime: '99.9%',
    avgResponseTime: '< 1 minute'
  });

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'bg-red-500/20 text-red-300 border-red-500/50';
      case 'high': return 'bg-orange-500/20 text-orange-300 border-orange-500/50';
      case 'medium': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/50';
      case 'low': return 'bg-blue-500/20 text-blue-300 border-blue-500/50';
      default: return 'bg-slate-500/20 text-slate-300 border-slate-500/50';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'unread': return 'bg-red-500/20 text-red-300 border-red-500/50';
      case 'acknowledged': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/50';
      case 'read': return 'bg-blue-500/20 text-blue-300 border-blue-500/50';
      case 'resolved': return 'bg-green-500/20 text-green-300 border-green-500/50';
      default: return 'bg-slate-500/20 text-slate-300 border-slate-500/50';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'breach': return <AlertTriangle className="h-5 w-5 text-red-400" />;
      case 'suspicious_activity': return <Eye className="h-5 w-5 text-orange-400" />;
      case 'identity_exposure': return <Globe className="h-5 w-5 text-yellow-400" />;
      case 'password_compromise': return <Shield className="h-5 w-5 text-red-400" />;
      case 'security_tip': return <Bell className="h-5 w-5 text-blue-400" />;
      default: return <Activity className="h-5 w-5 text-slate-400" />;
    }
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    if (diffInHours < 48) return 'Yesterday';
    return date.toLocaleDateString();
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Security Monitoring</h1>
            <p className="text-slate-400">
              Real-time monitoring and alerts for your digital identities
            </p>
          </div>
          <div className="flex items-center space-x-2 mt-4 md:mt-0">
            <Badge variant="outline" className="border-green-500/50 text-green-300">
              <Activity className="w-3 h-3 mr-1" />
              Active Monitoring
            </Badge>
          </div>
        </div>

        {/* Monitoring Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Total Scans</CardTitle>
              <Activity className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{monitoringStats.totalScans.toLocaleString()}</div>
              <p className="text-xs text-slate-400">Completed successfully</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Identities Monitored</CardTitle>
              <Eye className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{monitoringStats.identitiesMonitored}</div>
              <p className="text-xs text-slate-400">Active protection</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Breaches Detected</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{monitoringStats.breachesDetected}</div>
              <p className="text-xs text-slate-400">Requiring attention</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">System Uptime</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{monitoringStats.uptime}</div>
              <p className="text-xs text-slate-400">Last 30 days</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="alerts" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-slate-800/50">
            <TabsTrigger value="alerts" className="data-[state=active]:bg-purple-600">
              Security Alerts
            </TabsTrigger>
            <TabsTrigger value="activity" className="data-[state=active]:bg-purple-600">
              Monitoring Activity
            </TabsTrigger>
            <TabsTrigger value="settings" className="data-[state=active]:bg-purple-600">
              Alert Settings
            </TabsTrigger>
          </TabsList>

          {/* Alerts Tab */}
          <TabsContent value="alerts" className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button variant="outline" size="sm" className="border-slate-600 text-slate-300">
                  <Filter className="w-4 h-4 mr-2" />
                  Filter
                </Button>
                <Button variant="outline" size="sm" className="border-slate-600 text-slate-300">
                  <Calendar className="w-4 h-4 mr-2" />
                  Date Range
                </Button>
              </div>
              <Button variant="outline" size="sm" className="border-slate-600 text-slate-300">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>

            <div className="space-y-4">
              {alerts.map((alert) => (
                <Card key={alert.id} className="bg-slate-800/50 border-slate-700">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4 flex-1">
                        <div className="w-12 h-12 bg-slate-700/50 rounded-lg flex items-center justify-center">
                          {getTypeIcon(alert.type)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <Badge className={getSeverityColor(alert.severity)}>
                              {alert.severity}
                            </Badge>
                            <Badge className={getStatusColor(alert.status)}>
                              {alert.status}
                            </Badge>
                            {alert.actionRequired && (
                              <Badge variant="outline" className="border-orange-500/50 text-orange-300">
                                Action Required
                              </Badge>
                            )}
                          </div>
                          <h3 className="text-white font-medium text-lg mb-1">{alert.title}</h3>
                          <p className="text-slate-300 mb-2">{alert.description}</p>
                          <div className="flex items-center space-x-4 text-sm text-slate-400">
                            {alert.identity && (
                              <div className="flex items-center space-x-1">
                                {alert.identityType === 'email' ? (
                                  <Mail className="h-4 w-4" />
                                ) : (
                                  <Smartphone className="h-4 w-4" />
                                )}
                                <span>{alert.identity}</span>
                              </div>
                            )}
                            <div className="flex items-center space-x-1">
                              <Clock className="h-4 w-4" />
                              <span>{formatTimestamp(alert.timestamp)}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Globe className="h-4 w-4" />
                              <span>{alert.source}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {alert.actionRequired && (
                          <Button size="sm" className="bg-orange-600 hover:bg-orange-700">
                            Take Action
                          </Button>
                        )}
                        <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                          Mark as Read
                        </Button>
                      </div>
                    </div>

                    {alert.recommendations && alert.recommendations.length > 0 && (
                      <div className="mt-4 p-4 bg-slate-700/30 rounded-lg border border-slate-600">
                        <h4 className="text-white font-medium mb-3">Recommended Actions:</h4>
                        <div className="space-y-2">
                          {alert.recommendations.map((recommendation, index) => (
                            <div key={index} className="flex items-start space-x-2">
                              <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                              <span className="text-slate-300 text-sm">{recommendation}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Activity Tab */}
          <TabsContent value="activity" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">Monitoring Status</CardTitle>
                  <CardDescription>Current system status and performance</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300">System Status</span>
                    <Badge className="bg-green-500/20 text-green-300 border-green-500/50">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Operational
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300">Last Scan</span>
                    <span className="text-white">{monitoringStats.lastScanTime}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300">Response Time</span>
                    <span className="text-white">{monitoringStats.avgResponseTime}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-300">Alerts This Week</span>
                    <span className="text-white">{monitoringStats.alertsThisWeek}</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">Recent Activity</CardTitle>
                  <CardDescription>Latest monitoring events and scans</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">Identity scan completed - 2 hours ago</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">New breach detected - 6 hours ago</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">Dark web scan initiated - 12 hours ago</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">Suspicious activity detected - 1 day ago</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">Weekly security report generated - 3 days ago</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Monitoring Coverage</CardTitle>
                <CardDescription>What we're actively monitoring for you</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Globe className="h-8 w-8 text-purple-400" />
                    </div>
                    <h4 className="text-white font-medium mb-2">Data Breaches</h4>
                    <p className="text-slate-400 text-sm">
                      Monitoring 50+ million known breaches and new incidents
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Eye className="h-8 w-8 text-purple-400" />
                    </div>
                    <h4 className="text-white font-medium mb-2">Dark Web</h4>
                    <p className="text-slate-400 text-sm">
                      Scanning underground forums and marketplaces 24/7
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Shield className="h-8 w-8 text-purple-400" />
                    </div>
                    <h4 className="text-white font-medium mb-2">Threat Intelligence</h4>
                    <p className="text-slate-400 text-sm">
                      Real-time threat feeds from security researchers
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">Notification Preferences</CardTitle>
                  <CardDescription>Configure how you receive security alerts</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Email Notifications</p>
                      <p className="text-slate-400 text-sm">Receive alerts via email</p>
                    </div>
                    <Button variant="outline" size="sm" className="border-green-500/50 text-green-300">
                      Enabled
                    </Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">SMS Alerts</p>
                      <p className="text-slate-400 text-sm">Critical alerts via text message</p>
                    </div>
                    <Button variant="outline" size="sm" className="border-green-500/50 text-green-300">
                      Enabled
                    </Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Push Notifications</p>
                      <p className="text-slate-400 text-sm">Browser notifications</p>
                    </div>
                    <Button variant="outline" size="sm" className="border-slate-600 text-slate-300">
                      Disabled
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">Alert Thresholds</CardTitle>
                  <CardDescription>Set sensitivity levels for different alert types</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Data Breaches</p>
                      <p className="text-slate-400 text-sm">All severity levels</p>
                    </div>
                    <Badge className="bg-red-500/20 text-red-300 border-red-500/50">
                      High Sensitivity
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Suspicious Activity</p>
                      <p className="text-slate-400 text-sm">Medium and above</p>
                    </div>
                    <Badge className="bg-yellow-500/20 text-yellow-300 border-yellow-500/50">
                      Medium Sensitivity
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white font-medium">Security Tips</p>
                      <p className="text-slate-400 text-sm">Weekly digest</p>
                    </div>
                    <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/50">
                      Low Sensitivity
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Alert className="border-blue-500/50 bg-blue-500/10">
              <Bell className="h-4 w-4" />
              <AlertDescription className="text-blue-300">
                <strong>Tip:</strong> We recommend keeping high sensitivity for data breaches and password compromises to ensure you're notified immediately of critical security events.
              </AlertDescription>
            </Alert>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default MonitoringPage;

