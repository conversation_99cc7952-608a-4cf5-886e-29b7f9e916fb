from datetime import datetime
from enum import Enum
import hashlib
from src import db

class PasswordStrength(Enum):
    """Enumeration for password strength levels."""
    VERY_WEAK = 'very_weak'
    WEAK = 'weak'
    FAIR = 'fair'
    GOOD = 'good'
    STRONG = 'strong'
    VERY_STRONG = 'very_strong'

class PasswordStatus(Enum):
    """Enumeration for password compromise status."""
    SAFE = 'safe'
    COMPROMISED = 'compromised'
    POTENTIALLY_COMPROMISED = 'potentially_compromised'
    UNKNOWN = 'unknown'

class Password(db.Model):
    """Password model for tracking password security and breaches."""
    
    __tablename__ = 'passwords'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    
    # Password information (we store hash for security)
    password_hash_sha1 = db.Column(db.String(40), nullable=False, index=True)  # SHA-1 for HIBP API
    service_name = db.Column(db.String(255), nullable=False)  # e.g., "Gmail", "Facebook"
    username = db.Column(db.String(255))  # Associated username/email
    
    # Security assessment
    strength = db.Column(db.Enum(PasswordStrength), nullable=False)
    strength_score = db.Column(db.Integer, default=0, nullable=False)  # 0-100
    status = db.Column(db.Enum(PasswordStatus), default=PasswordStatus.UNKNOWN, nullable=False)
    
    # Analysis details
    strength_analysis = db.Column(db.JSON)  # Detailed strength analysis
    compromise_details = db.Column(db.JSON)  # Breach information if compromised
    recommendations = db.Column(db.JSON)  # Security recommendations
    
    # Monitoring
    last_checked = db.Column(db.DateTime)
    check_frequency = db.Column(db.Integer, default=168)  # hours (weekly by default)
    is_monitored = db.Column(db.Boolean, default=True, nullable=False)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    user = db.relationship('User', backref='passwords')
    
    def __init__(self, user_id, password_plain, service_name, username=None):
        self.user_id = user_id
        self.service_name = service_name.strip()
        self.username = username.strip() if username else None
        self.set_password_hash(password_plain)
    
    def set_password_hash(self, password_plain):
        """Set SHA-1 hash of password for HIBP API compatibility."""
        self.password_hash_sha1 = hashlib.sha1(password_plain.encode('utf-8')).hexdigest().upper()
    
    def update_strength_analysis(self, strength, score, analysis):
        """Update password strength analysis."""
        self.strength = strength
        self.strength_score = max(0, min(100, score))
        self.strength_analysis = analysis
        self.updated_at = datetime.utcnow()
    
    def update_compromise_status(self, status, details=None):
        """Update password compromise status."""
        self.status = status
        self.compromise_details = details or {}
        self.last_checked = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def add_recommendations(self, recommendations):
        """Add security recommendations."""
        self.recommendations = recommendations
        self.updated_at = datetime.utcnow()
    
    @property
    def is_compromised(self):
        """Check if password is known to be compromised."""
        return self.status == PasswordStatus.COMPROMISED
    
    @property
    def is_weak(self):
        """Check if password is considered weak."""
        return self.strength in [PasswordStrength.VERY_WEAK, PasswordStrength.WEAK]
    
    @property
    def needs_attention(self):
        """Check if password needs user attention."""
        return self.is_compromised or self.is_weak
    
    @property
    def hibp_prefix(self):
        """Get first 5 characters of SHA-1 hash for HIBP API."""
        return self.password_hash_sha1[:5]
    
    @property
    def hibp_suffix(self):
        """Get remaining characters of SHA-1 hash for HIBP API."""
        return self.password_hash_sha1[5:]
    
    def to_dict(self, include_sensitive=False):
        """Convert password to dictionary."""
        data = {
            'id': self.id,
            'user_id': self.user_id,
            'service_name': self.service_name,
            'username': self.username,
            'strength': self.strength.value,
            'strength_score': self.strength_score,
            'status': self.status.value,
            'strength_analysis': self.strength_analysis,
            'recommendations': self.recommendations,
            'is_monitored': self.is_monitored,
            'check_frequency': self.check_frequency,
            'last_checked': self.last_checked.isoformat() if self.last_checked else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_compromised': self.is_compromised,
            'is_weak': self.is_weak,
            'needs_attention': self.needs_attention
        }
        
        if include_sensitive:
            data['compromise_details'] = self.compromise_details
            data['hibp_prefix'] = self.hibp_prefix
        
        return data
    
    def __repr__(self):
        return f'<Password {self.service_name} - {self.strength.value}>'

