# Identity Guardian - Python Dependencies
# Backend requirements for Flask application

Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5
Flask-JWT-Extended==4.5.3
Flask-CORS==4.0.0
psycopg2-binary==2.9.7
python-dotenv==1.0.0
bcrypt==4.0.1
requests==2.31.0
celery==5.3.4
redis==5.0.1
Werkzeug==2.3.7
click==8.1.7
itsdangerous==2.1.2
Jinja2==3.1.2
MarkupSafe==2.1.3
PyJWT==2.8.0
six==1.16.0
urllib3==2.0.4
certifi==2023.7.22
charset-normalizer==3.2.0
idna==3.4

# Development dependencies
pytest==7.4.2
pytest-flask==1.2.0
black==23.7.0
flake8==6.0.0
mypy==1.5.1

# Production dependencies
gunicorn==21.2.0
supervisor==4.2.5

