# Identity Guardian - Complete Installation Guide

## 🚀 Overview

Identity Guardian is a comprehensive cybersecurity application built with modern web technologies. This guide will help you set up the complete development environment and deploy the application on macOS, Windows, and Linux.

## 📋 Prerequisites

### Required Software
- **Node.js** (v18 or higher)
- **Python** (v3.9 or higher)
- **PostgreSQL** (v13 or higher)
- **Git**
- **Visual Studio Code** (recommended)

### Recommended VSCode Extensions
- Python
- JavaScript (ES6) code snippets
- Tailwind CSS IntelliSense
- PostgreSQL
- GitLens
- Prettier - Code formatter
- ES7+ React/Redux/React-Native snippets

## 🛠️ Installation Instructions

### 1. Clone the Repository

```bash
git clone <your-repository-url>
cd identity-guardian
```

### 2. Database Setup (PostgreSQL)

#### macOS
```bash
# Install PostgreSQL using Homebrew
brew install postgresql
brew services start postgresql

# Create database
createdb theguardian

# Connect and run schema
psql theguardian < theguardian_database_schema.sql
```

#### Windows
```bash
# Download and install PostgreSQL from https://www.postgresql.org/download/windows/
# Add PostgreSQL to PATH

# Create database
createdb -U postgres theguardian

# Run schema
psql -U postgres -d theguardian -f theguardian_database_schema.sql
```

#### Linux (Ubuntu/Debian)
```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Switch to postgres user and create database
sudo -u postgres createdb theguardian

# Run schema
sudo -u postgres psql theguardian < theguardian_database_schema.sql
```

### 3. Backend Setup (Flask)

```bash
# Navigate to backend directory
cd identity-guardian-backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# macOS/Linux:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env

# Edit .env file with your database credentials
# DATABASE_URL=postgresql://username:password@localhost:5432/theguardian
```

#### Backend Dependencies (requirements.txt)
```
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5
Flask-JWT-Extended==4.5.3
Flask-CORS==4.0.0
psycopg2-binary==2.9.7
python-dotenv==1.0.0
bcrypt==4.0.1
requests==2.31.0
celery==5.3.4
redis==5.0.1
```

### 4. Frontend Setup (React)

```bash
# Navigate to frontend directory
cd identity-guardian-frontend

# Install dependencies
npm install

# Install additional packages if needed
npm install @tailwindcss/forms @headlessui/react
```

#### Frontend Dependencies (package.json)
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.15.0",
    "lucide-react": "^0.263.1",
    "tailwindcss": "^3.3.3",
    "@headlessui/react": "^1.7.17",
    "@tailwindcss/forms": "^0.5.6"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@vitejs/plugin-react": "^4.0.3",
    "autoprefixer": "^10.4.15",
    "postcss": "^8.4.29",
    "vite": "^4.4.5"
  }
}
```

## 🔧 Configuration

### Environment Variables (.env)

```bash
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/theguardian
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=theguardian
DATABASE_USER=your_username
DATABASE_PASSWORD=your_password

# Flask Configuration
FLASK_APP=src/main.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key

# Security Configuration
BCRYPT_LOG_ROUNDS=12
JWT_ACCESS_TOKEN_EXPIRES=3600

# External APIs
HAVEIBEENPWNED_API_KEY=your-api-key
BREACH_DIRECTORY_API_KEY=your-api-key

# Email Configuration (Optional)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0
```

### Database Configuration

The application uses PostgreSQL with the database name **"theguardian"**. The complete schema is provided in `theguardian_database_schema.sql` and includes:

- User management and authentication
- Identity monitoring and breach tracking
- Password security and analysis
- Security alerts and notifications
- Educational content and tips
- Audit logging and analytics

## 🚀 Running the Application

### Development Mode

#### Terminal 1 - Backend
```bash
cd identity-guardian-backend
source venv/bin/activate  # or venv\Scripts\activate on Windows
python src/main.py
```

#### Terminal 2 - Frontend
```bash
cd identity-guardian-frontend
npm run dev
```

#### Terminal 3 - Database (if not running as service)
```bash
# macOS
brew services start postgresql

# Linux
sudo systemctl start postgresql

# Windows
net start postgresql-x64-13
```

### Production Build

#### Build Frontend
```bash
cd identity-guardian-frontend
npm run build
```

#### Deploy with Flask
```bash
# Copy built files to Flask static directory
cp -r identity-guardian-frontend/dist/* identity-guardian-backend/src/static/

# Run Flask in production mode
cd identity-guardian-backend
source venv/bin/activate
export FLASK_ENV=production
python src/main.py
```

## 🔒 Security Configuration

### SSL/TLS Setup (Production)

```bash
# Generate SSL certificates
openssl req -x509 -newkey rsa:4096 -nodes -out cert.pem -keyout key.pem -days 365

# Update Flask configuration
app.run(host='0.0.0.0', port=443, ssl_context=('cert.pem', 'key.pem'))
```

### Firewall Configuration

```bash
# Ubuntu/Debian
sudo ufw allow 5000/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

## 📱 VSCode Setup

### Recommended Settings (settings.json)

```json
{
  "python.defaultInterpreterPath": "./identity-guardian-backend/venv/bin/python",
  "python.terminal.activateEnvironment": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  "tailwindCSS.includeLanguages": {
    "javascript": "javascript",
    "html": "HTML"
  },
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  }
}
```

### Launch Configuration (.vscode/launch.json)

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: Flask",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/identity-guardian-backend/src/main.py",
      "env": {
        "FLASK_APP": "src/main.py",
        "FLASK_ENV": "development"
      },
      "args": [],
      "jinja": true,
      "justMyCode": true
    },
    {
      "name": "Launch React",
      "type": "node",
      "request": "launch",
      "cwd": "${workspaceFolder}/identity-guardian-frontend",
      "runtimeExecutable": "npm",
      "runtimeArgs": ["run", "dev"]
    }
  ]
}
```

### Tasks Configuration (.vscode/tasks.json)

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Start Backend",
      "type": "shell",
      "command": "cd identity-guardian-backend && source venv/bin/activate && python src/main.py",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      }
    },
    {
      "label": "Start Frontend",
      "type": "shell",
      "command": "cd identity-guardian-frontend && npm run dev",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "new"
      }
    },
    {
      "label": "Build Frontend",
      "type": "shell",
      "command": "cd identity-guardian-frontend && npm run build",
      "group": "build"
    }
  ]
}
```

## 🐳 Docker Setup (Optional)

### Dockerfile (Backend)

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["python", "src/main.py"]
```

### Dockerfile (Frontend)

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=0 /app/dist /usr/share/nginx/html

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### docker-compose.yml

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: theguardian
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./theguardian_database_schema.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"

  backend:
    build: ./identity-guardian-backend
    environment:
      DATABASE_URL: ********************************************/theguardian
    ports:
      - "5000:5000"
    depends_on:
      - postgres

  frontend:
    build: ./identity-guardian-frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  postgres_data:
```

## 🧪 Testing

### Backend Tests

```bash
cd identity-guardian-backend
source venv/bin/activate
pip install pytest pytest-flask
pytest tests/
```

### Frontend Tests

```bash
cd identity-guardian-frontend
npm install --save-dev @testing-library/react @testing-library/jest-dom
npm test
```

## 📊 Monitoring and Logging

### Application Monitoring

```python
# Add to Flask app
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler('logs/identity_guardian.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
```

### Database Monitoring

```sql
-- Monitor database performance
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats
WHERE schemaname = 'public';

-- Check table sizes
SELECT 
    tablename,
    pg_size_pretty(pg_total_relation_size(tablename::regclass)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(tablename::regclass) DESC;
```

## 🔧 Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check database exists
psql -l | grep theguardian

# Test connection
psql postgresql://username:password@localhost:5432/theguardian
```

#### Node.js Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### Python Virtual Environment Issues
```bash
# Recreate virtual environment
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### Performance Optimization

#### Database Optimization
```sql
-- Create additional indexes for performance
CREATE INDEX CONCURRENTLY idx_users_email_active ON users(email) WHERE is_active = true;
CREATE INDEX CONCURRENTLY idx_security_alerts_user_status ON security_alerts(user_id, status);
CREATE INDEX CONCURRENTLY idx_monitored_identities_user_active ON monitored_identities(user_id) WHERE is_active = true;

-- Analyze tables for query optimization
ANALYZE users;
ANALYZE monitored_identities;
ANALYZE security_alerts;
```

#### Frontend Optimization
```bash
# Build with optimization
npm run build

# Analyze bundle size
npm install --save-dev webpack-bundle-analyzer
npx webpack-bundle-analyzer dist/static/js/*.js
```

## 📚 Additional Resources

### Documentation
- [Flask Documentation](https://flask.palletsprojects.com/)
- [React Documentation](https://reactjs.org/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

### Security Best Practices
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Flask Security](https://flask.palletsprojects.com/en/2.3.x/security/)
- [React Security](https://reactjs.org/docs/dom-elements.html#dangerouslysetinnerhtml)

### API Documentation
- [Have I Been Pwned API](https://haveibeenpwned.com/API/v3)
- [Breach Directory API](https://breachdirectory.org/api)

## 🎯 Next Steps

1. **Set up monitoring** with tools like Prometheus and Grafana
2. **Implement CI/CD** with GitHub Actions or GitLab CI
3. **Add comprehensive testing** with Jest and Pytest
4. **Set up logging** with ELK stack or similar
5. **Implement caching** with Redis
6. **Add rate limiting** for API endpoints
7. **Set up backup strategies** for database
8. **Implement health checks** for all services

## 📞 Support

For technical support or questions:
- Check the troubleshooting section above
- Review application logs in `logs/` directory
- Check database logs for connection issues
- Verify all environment variables are set correctly

---

**Identity Guardian** - Your comprehensive cybersecurity defense center, ready for deployment across all platforms!

