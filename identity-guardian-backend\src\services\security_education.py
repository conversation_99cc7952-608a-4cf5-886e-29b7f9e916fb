from typing import Dict, List
from src.models.security_tip import SecurityTip, TipCategory, TipPriority, UserTipInteraction
from src import db

class SecurityEducationService:
    """Service for managing cybersecurity education and awareness content."""
    
    def __init__(self):
        self.initialize_default_tips()
    
    def get_personalized_tips(self, user_id: int, limit: int = 5) -> List[Dict]:
        """
        Get personalized security tips based on user's security profile.
        
        Args:
            user_id: User ID to get personalized tips for
            limit: Maximum number of tips to return
            
        Returns:
            List of personalized security tips
        """
        # Get user's interaction history
        viewed_tips = db.session.query(UserTipInteraction.tip_id).filter_by(user_id=user_id).all()
        viewed_tip_ids = [tip[0] for tip in viewed_tips]
        
        # Get tips user hasn't seen, prioritizing high-priority ones
        query = SecurityTip.query.filter(
            SecurityTip.is_active == True,
            ~SecurityTip.id.in_(viewed_tip_ids)
        ).order_by(
            SecurityTip.priority.desc(),
            SecurityTip.helpful_count.desc()
        ).limit(limit)
        
        tips = query.all()
        return [tip.to_dict() for tip in tips]
    
    def get_tips_by_category(self, category: TipCategory, limit: int = 10) -> List[Dict]:
        """Get security tips by category."""
        tips = SecurityTip.query.filter_by(
            category=category,
            is_active=True
        ).order_by(
            SecurityTip.priority.desc(),
            SecurityTip.helpful_count.desc()
        ).limit(limit).all()
        
        return [tip.to_dict() for tip in tips]
    
    def get_critical_tips(self, limit: int = 5) -> List[Dict]:
        """Get critical security tips that all users should know."""
        tips = SecurityTip.query.filter_by(
            priority=TipPriority.CRITICAL,
            is_active=True
        ).order_by(SecurityTip.helpful_count.desc()).limit(limit).all()
        
        return [tip.to_dict() for tip in tips]
    
    def record_tip_interaction(self, user_id: int, tip_id: int) -> UserTipInteraction:
        """Record that a user viewed a security tip."""
        # Check if interaction already exists
        interaction = UserTipInteraction.query.filter_by(
            user_id=user_id,
            tip_id=tip_id
        ).first()
        
        if not interaction:
            interaction = UserTipInteraction(user_id=user_id, tip_id=tip_id)
            db.session.add(interaction)
            
            # Increment tip view count
            tip = SecurityTip.query.get(tip_id)
            if tip:
                tip.increment_view_count()
            
            db.session.commit()
        
        return interaction
    
    def mark_tip_helpful(self, user_id: int, tip_id: int) -> bool:
        """Mark a tip as helpful by a user."""
        interaction = UserTipInteraction.query.filter_by(
            user_id=user_id,
            tip_id=tip_id
        ).first()
        
        if interaction:
            interaction.mark_helpful()
            db.session.commit()
            return True
        
        return False
    
    def get_security_assessment_tips(self, assessment_results: Dict) -> List[Dict]:
        """
        Get targeted tips based on security assessment results.
        
        Args:
            assessment_results: Results from security assessment
            
        Returns:
            List of relevant security tips
        """
        relevant_categories = []
        
        # Determine relevant categories based on assessment
        if assessment_results.get('weak_passwords', 0) > 0:
            relevant_categories.append(TipCategory.PASSWORD_SECURITY)
        
        if assessment_results.get('compromised_identities', 0) > 0:
            relevant_categories.append(TipCategory.IDENTITY_PROTECTION)
        
        if assessment_results.get('no_2fa', False):
            relevant_categories.append(TipCategory.TWO_FACTOR_AUTH)
        
        if assessment_results.get('phishing_risk', False):
            relevant_categories.append(TipCategory.PHISHING_PROTECTION)
        
        # Default to general awareness if no specific risks
        if not relevant_categories:
            relevant_categories = [TipCategory.GENERAL_AWARENESS]
        
        # Get tips from relevant categories
        tips = []
        for category in relevant_categories:
            category_tips = self.get_tips_by_category(category, limit=3)
            tips.extend(category_tips)
        
        return tips[:10]  # Limit to 10 tips total
    
    def initialize_default_tips(self):
        """Initialize the database with default security tips."""
        default_tips = [
            {
                'title': 'Use Strong, Unique Passwords',
                'content': '''Creating strong, unique passwords is your first line of defense against cyber attacks. A strong password should be at least 12 characters long and include a mix of uppercase letters, lowercase letters, numbers, and special characters.

Never reuse passwords across multiple accounts. If one account gets compromised, attackers could access all your other accounts using the same password.

Consider using a passphrase - a series of random words that are easy for you to remember but hard for others to guess. For example: "Coffee-Mountain-Purple-42" is much stronger than "P@ssw0rd123".

The most important rule: use a different password for every account, especially for critical accounts like email, banking, and work systems.''',
                'category': TipCategory.PASSWORD_SECURITY,
                'priority': TipPriority.CRITICAL,
                'action_items': [
                    'Audit all your current passwords',
                    'Change any weak or reused passwords',
                    'Set up a password manager',
                    'Enable two-factor authentication where possible'
                ]
            },
            {
                'title': 'Recognize Phishing Attempts',
                'content': '''Phishing is one of the most common ways attackers steal personal information. These attacks often come through email, text messages, or fake websites that look legitimate.

Warning signs of phishing:
- Urgent language ("Act now!" or "Your account will be closed!")
- Requests for personal information via email
- Suspicious sender addresses that don't match the claimed organization
- Links that don't match the legitimate website URL
- Poor grammar or spelling errors

Always verify requests independently. If you receive an email claiming to be from your bank, don't click the link - instead, go directly to your bank's website or call them directly.

When in doubt, don't click. It's better to be cautious than to become a victim.''',
                'category': TipCategory.PHISHING_PROTECTION,
                'priority': TipPriority.CRITICAL,
                'action_items': [
                    'Learn to identify suspicious emails',
                    'Verify requests through official channels',
                    'Never click suspicious links',
                    'Report phishing attempts to your IT department'
                ]
            },
            {
                'title': 'Enable Two-Factor Authentication (2FA)',
                'content': '''Two-factor authentication adds an extra layer of security to your accounts by requiring something you know (your password) and something you have (your phone or a security key).

Even if someone steals your password, they won't be able to access your account without the second factor. This makes your accounts significantly more secure.

Types of 2FA:
- SMS codes (better than nothing, but not the most secure)
- Authenticator apps like Google Authenticator or Authy (more secure)
- Hardware security keys (most secure)

Enable 2FA on all important accounts, especially email, banking, social media, and work accounts. Many services now offer this feature for free.''',
                'category': TipCategory.TWO_FACTOR_AUTH,
                'priority': TipPriority.HIGH,
                'action_items': [
                    'Enable 2FA on your email accounts',
                    'Set up 2FA for banking and financial accounts',
                    'Install an authenticator app',
                    'Consider getting a hardware security key'
                ]
            },
            {
                'title': 'Keep Software Updated',
                'content': '''Software updates often include critical security patches that fix vulnerabilities. Cybercriminals actively exploit these vulnerabilities, so keeping your software updated is essential.

What to keep updated:
- Operating system (Windows, macOS, Linux)
- Web browsers (Chrome, Firefox, Safari, Edge)
- Antivirus software
- Mobile apps
- Router firmware

Enable automatic updates whenever possible. This ensures you get security patches as soon as they're available without having to remember to check manually.

Don't ignore update notifications - they're there for your protection. The inconvenience of an update is much less than the cost of a security breach.''',
                'category': TipCategory.MALWARE_PROTECTION,
                'priority': TipPriority.HIGH,
                'action_items': [
                    'Enable automatic updates on all devices',
                    'Check for router firmware updates',
                    'Update mobile apps regularly',
                    'Keep antivirus software current'
                ]
            },
            {
                'title': 'Be Cautious with Public Wi-Fi',
                'content': '''Public Wi-Fi networks are convenient but can be dangerous. Attackers can easily intercept data transmitted over unsecured networks, potentially stealing passwords, personal information, and other sensitive data.

Safety tips for public Wi-Fi:
- Avoid accessing sensitive accounts (banking, email) on public networks
- Use a VPN (Virtual Private Network) to encrypt your connection
- Turn off automatic Wi-Fi connection on your devices
- Verify the network name with staff before connecting
- Use your phone's hotspot instead when possible

If you must use public Wi-Fi for sensitive activities, make sure the websites you visit use HTTPS (look for the lock icon in your browser).''',
                'category': TipCategory.SAFE_BROWSING,
                'priority': TipPriority.MEDIUM,
                'action_items': [
                    'Install a reputable VPN app',
                    'Disable auto-connect to Wi-Fi networks',
                    'Use mobile data for sensitive activities',
                    'Always verify network names with staff'
                ]
            },
            {
                'title': 'Protect Your Personal Information',
                'content': '''Identity thieves can use surprisingly little information to steal your identity. Be careful about what personal information you share, both online and offline.

Information to protect:
- Social Security Number
- Date of birth
- Full name and address
- Phone numbers
- Financial account numbers
- Passwords and PINs

Be especially careful on social media. Avoid posting information that could be used to answer security questions, such as your pet's name, where you went to school, or your mother's maiden name.

Shred documents containing personal information before throwing them away. Identity thieves still go through trash looking for useful information.''',
                'category': TipCategory.IDENTITY_PROTECTION,
                'priority': TipPriority.HIGH,
                'action_items': [
                    'Review your social media privacy settings',
                    'Limit personal information in online profiles',
                    'Shred sensitive documents',
                    'Be cautious about sharing personal details'
                ]
            },
            {
                'title': 'Secure Your Mobile Devices',
                'content': '''Mobile devices contain vast amounts of personal information and are often targets for thieves and hackers. Securing your phone or tablet is crucial for protecting your data.

Mobile security essentials:
- Use a strong lock screen (PIN, password, fingerprint, or face unlock)
- Keep your device's operating system updated
- Only download apps from official app stores
- Review app permissions carefully
- Enable remote wipe capabilities
- Use encryption if available

Be cautious about what you do on your mobile device in public. Shoulder surfing (people watching you enter passwords) is a real threat in crowded areas.

Consider what happens if your device is lost or stolen. Having a backup plan and remote wipe capability can save you from a major security incident.''',
                'category': TipCategory.MOBILE_SECURITY,
                'priority': TipPriority.MEDIUM,
                'action_items': [
                    'Set up a strong lock screen',
                    'Enable automatic device locking',
                    'Set up remote wipe capabilities',
                    'Review and limit app permissions'
                ]
            },
            {
                'title': 'Think Before You Click',
                'content': '''Many cyber attacks succeed because people click on malicious links or download infected files. Developing good clicking habits can prevent most malware infections.

Before clicking any link, ask yourself:
- Do I trust the sender?
- Was I expecting this message?
- Does the link look legitimate?
- Is this too good to be true?

Hover over links to see where they actually lead before clicking. Be especially suspicious of shortened URLs (bit.ly, tinyurl, etc.) that hide the real destination.

Never download software from pop-up ads or unfamiliar websites. Stick to official sources and app stores for downloads.

When in doubt, don't click. It's better to miss out on something than to infect your computer with malware.''',
                'category': TipCategory.SAFE_BROWSING,
                'priority': TipPriority.HIGH,
                'action_items': [
                    'Hover over links before clicking',
                    'Verify sender identity for unexpected messages',
                    'Use official sources for software downloads',
                    'Install ad blockers to reduce malicious ads'
                ]
            }
        ]
        
        # Check if tips already exist to avoid duplicates
        existing_tips = SecurityTip.query.count()
        if existing_tips == 0:
            for tip_data in default_tips:
                tip = SecurityTip(
                    title=tip_data['title'],
                    content=tip_data['content'],
                    category=tip_data['category'],
                    priority=tip_data['priority']
                )
                
                if 'action_items' in tip_data:
                    tip.add_action_items(tip_data['action_items'])
                
                db.session.add(tip)
            
            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"Error initializing security tips: {e}")
    
    def get_tip_analytics(self) -> Dict:
        """Get analytics about security tip engagement."""
        total_tips = SecurityTip.query.filter_by(is_active=True).count()
        total_views = db.session.query(db.func.sum(SecurityTip.view_count)).scalar() or 0
        total_helpful = db.session.query(db.func.sum(SecurityTip.helpful_count)).scalar() or 0
        
        # Most popular tips
        popular_tips = SecurityTip.query.filter_by(is_active=True).order_by(
            SecurityTip.view_count.desc()
        ).limit(5).all()
        
        # Most helpful tips
        helpful_tips = SecurityTip.query.filter_by(is_active=True).order_by(
            SecurityTip.helpful_count.desc()
        ).limit(5).all()
        
        return {
            'total_tips': total_tips,
            'total_views': total_views,
            'total_helpful_votes': total_helpful,
            'average_helpfulness': (total_helpful / total_views * 100) if total_views > 0 else 0,
            'most_popular': [tip.to_dict(include_analytics=True) for tip in popular_tips],
            'most_helpful': [tip.to_dict(include_analytics=True) for tip in helpful_tips]
        }

