function wg(i,c){for(var o=0;o<c.length;o++){const u=c[o];if(typeof u!="string"&&!Array.isArray(u)){for(const d in u)if(d!=="default"&&!(d in i)){const h=Object.getOwnPropertyDescriptor(u,d);h&&Object.defineProperty(i,d,h.get?h:{enumerable:!0,get:()=>u[d]})}}}return Object.freeze(Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}))}(function(){const c=document.createElement("link").relList;if(c&&c.supports&&c.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))u(d);new MutationObserver(d=>{for(const h of d)if(h.type==="childList")for(const y of h.addedNodes)y.tagName==="LINK"&&y.rel==="modulepreload"&&u(y)}).observe(document,{childList:!0,subtree:!0});function o(d){const h={};return d.integrity&&(h.integrity=d.integrity),d.referrerPolicy&&(h.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?h.credentials="include":d.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function u(d){if(d.ep)return;d.ep=!0;const h=o(d);fetch(d.href,h)}})();function e0(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var eu={exports:{}},Yn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wh;function Sg(){if(wh)return Yn;wh=1;var i=Symbol.for("react.transitional.element"),c=Symbol.for("react.fragment");function o(u,d,h){var y=null;if(h!==void 0&&(y=""+h),d.key!==void 0&&(y=""+d.key),"key"in d){h={};for(var v in d)v!=="key"&&(h[v]=d[v])}else h=d;return d=h.ref,{$$typeof:i,type:u,key:y,ref:d!==void 0?d:null,props:h}}return Yn.Fragment=c,Yn.jsx=o,Yn.jsxs=o,Yn}var Sh;function Eg(){return Sh||(Sh=1,eu.exports=Sg()),eu.exports}var s=Eg(),tu={exports:{}},ge={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Eh;function Tg(){if(Eh)return ge;Eh=1;var i=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),y=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),b=Symbol.for("react.lazy"),x=Symbol.iterator;function j(S){return S===null||typeof S!="object"?null:(S=x&&S[x]||S["@@iterator"],typeof S=="function"?S:null)}var _={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},E=Object.assign,L={};function D(S,U,X){this.props=S,this.context=U,this.refs=L,this.updater=X||_}D.prototype.isReactComponent={},D.prototype.setState=function(S,U){if(typeof S!="object"&&typeof S!="function"&&S!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,S,U,"setState")},D.prototype.forceUpdate=function(S){this.updater.enqueueForceUpdate(this,S,"forceUpdate")};function B(){}B.prototype=D.prototype;function Q(S,U,X){this.props=S,this.context=U,this.refs=L,this.updater=X||_}var O=Q.prototype=new B;O.constructor=Q,E(O,D.prototype),O.isPureReactComponent=!0;var P=Array.isArray,J={H:null,A:null,T:null,S:null,V:null},ie=Object.prototype.hasOwnProperty;function pe(S,U,X,G,W,ue){return X=ue.ref,{$$typeof:i,type:S,key:U,ref:X!==void 0?X:null,props:ue}}function K(S,U){return pe(S.type,U,void 0,void 0,void 0,S.props)}function I(S){return typeof S=="object"&&S!==null&&S.$$typeof===i}function Ee(S){var U={"=":"=0",":":"=2"};return"$"+S.replace(/[=:]/g,function(X){return U[X]})}var Te=/\/+/g;function Ae(S,U){return typeof S=="object"&&S!==null&&S.key!=null?Ee(""+S.key):U.toString(36)}function ot(){}function jt(S){switch(S.status){case"fulfilled":return S.value;case"rejected":throw S.reason;default:switch(typeof S.status=="string"?S.then(ot,ot):(S.status="pending",S.then(function(U){S.status==="pending"&&(S.status="fulfilled",S.value=U)},function(U){S.status==="pending"&&(S.status="rejected",S.reason=U)})),S.status){case"fulfilled":return S.value;case"rejected":throw S.reason}}throw S}function Re(S,U,X,G,W){var ue=typeof S;(ue==="undefined"||ue==="boolean")&&(S=null);var F=!1;if(S===null)F=!0;else switch(ue){case"bigint":case"string":case"number":F=!0;break;case"object":switch(S.$$typeof){case i:case c:F=!0;break;case b:return F=S._init,Re(F(S._payload),U,X,G,W)}}if(F)return W=W(S),F=G===""?"."+Ae(S,0):G,P(W)?(X="",F!=null&&(X=F.replace(Te,"$&/")+"/"),Re(W,U,X,"",function(lt){return lt})):W!=null&&(I(W)&&(W=K(W,X+(W.key==null||S&&S.key===W.key?"":(""+W.key).replace(Te,"$&/")+"/")+F)),U.push(W)),1;F=0;var Ne=G===""?".":G+":";if(P(S))for(var Ce=0;Ce<S.length;Ce++)G=S[Ce],ue=Ne+Ae(G,Ce),F+=Re(G,U,X,ue,W);else if(Ce=j(S),typeof Ce=="function")for(S=Ce.call(S),Ce=0;!(G=S.next()).done;)G=G.value,ue=Ne+Ae(G,Ce++),F+=Re(G,U,X,ue,W);else if(ue==="object"){if(typeof S.then=="function")return Re(jt(S),U,X,G,W);throw U=String(S),Error("Objects are not valid as a React child (found: "+(U==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":U)+"). If you meant to render a collection of children, use an array instead.")}return F}function R(S,U,X){if(S==null)return S;var G=[],W=0;return Re(S,G,"","",function(ue){return U.call(X,ue,W++)}),G}function Z(S){if(S._status===-1){var U=S._result;U=U(),U.then(function(X){(S._status===0||S._status===-1)&&(S._status=1,S._result=X)},function(X){(S._status===0||S._status===-1)&&(S._status=2,S._result=X)}),S._status===-1&&(S._status=0,S._result=U)}if(S._status===1)return S._result.default;throw S._result}var V=typeof reportError=="function"?reportError:function(S){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var U=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof S=="object"&&S!==null&&typeof S.message=="string"?String(S.message):String(S),error:S});if(!window.dispatchEvent(U))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",S);return}console.error(S)};function fe(){}return ge.Children={map:R,forEach:function(S,U,X){R(S,function(){U.apply(this,arguments)},X)},count:function(S){var U=0;return R(S,function(){U++}),U},toArray:function(S){return R(S,function(U){return U})||[]},only:function(S){if(!I(S))throw Error("React.Children.only expected to receive a single React element child.");return S}},ge.Component=D,ge.Fragment=o,ge.Profiler=d,ge.PureComponent=Q,ge.StrictMode=u,ge.Suspense=g,ge.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=J,ge.__COMPILER_RUNTIME={__proto__:null,c:function(S){return J.H.useMemoCache(S)}},ge.cache=function(S){return function(){return S.apply(null,arguments)}},ge.cloneElement=function(S,U,X){if(S==null)throw Error("The argument must be a React element, but you passed "+S+".");var G=E({},S.props),W=S.key,ue=void 0;if(U!=null)for(F in U.ref!==void 0&&(ue=void 0),U.key!==void 0&&(W=""+U.key),U)!ie.call(U,F)||F==="key"||F==="__self"||F==="__source"||F==="ref"&&U.ref===void 0||(G[F]=U[F]);var F=arguments.length-2;if(F===1)G.children=X;else if(1<F){for(var Ne=Array(F),Ce=0;Ce<F;Ce++)Ne[Ce]=arguments[Ce+2];G.children=Ne}return pe(S.type,W,void 0,void 0,ue,G)},ge.createContext=function(S){return S={$$typeof:y,_currentValue:S,_currentValue2:S,_threadCount:0,Provider:null,Consumer:null},S.Provider=S,S.Consumer={$$typeof:h,_context:S},S},ge.createElement=function(S,U,X){var G,W={},ue=null;if(U!=null)for(G in U.key!==void 0&&(ue=""+U.key),U)ie.call(U,G)&&G!=="key"&&G!=="__self"&&G!=="__source"&&(W[G]=U[G]);var F=arguments.length-2;if(F===1)W.children=X;else if(1<F){for(var Ne=Array(F),Ce=0;Ce<F;Ce++)Ne[Ce]=arguments[Ce+2];W.children=Ne}if(S&&S.defaultProps)for(G in F=S.defaultProps,F)W[G]===void 0&&(W[G]=F[G]);return pe(S,ue,void 0,void 0,null,W)},ge.createRef=function(){return{current:null}},ge.forwardRef=function(S){return{$$typeof:v,render:S}},ge.isValidElement=I,ge.lazy=function(S){return{$$typeof:b,_payload:{_status:-1,_result:S},_init:Z}},ge.memo=function(S,U){return{$$typeof:m,type:S,compare:U===void 0?null:U}},ge.startTransition=function(S){var U=J.T,X={};J.T=X;try{var G=S(),W=J.S;W!==null&&W(X,G),typeof G=="object"&&G!==null&&typeof G.then=="function"&&G.then(fe,V)}catch(ue){V(ue)}finally{J.T=U}},ge.unstable_useCacheRefresh=function(){return J.H.useCacheRefresh()},ge.use=function(S){return J.H.use(S)},ge.useActionState=function(S,U,X){return J.H.useActionState(S,U,X)},ge.useCallback=function(S,U){return J.H.useCallback(S,U)},ge.useContext=function(S){return J.H.useContext(S)},ge.useDebugValue=function(){},ge.useDeferredValue=function(S,U){return J.H.useDeferredValue(S,U)},ge.useEffect=function(S,U,X){var G=J.H;if(typeof X=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return G.useEffect(S,U)},ge.useId=function(){return J.H.useId()},ge.useImperativeHandle=function(S,U,X){return J.H.useImperativeHandle(S,U,X)},ge.useInsertionEffect=function(S,U){return J.H.useInsertionEffect(S,U)},ge.useLayoutEffect=function(S,U){return J.H.useLayoutEffect(S,U)},ge.useMemo=function(S,U){return J.H.useMemo(S,U)},ge.useOptimistic=function(S,U){return J.H.useOptimistic(S,U)},ge.useReducer=function(S,U,X){return J.H.useReducer(S,U,X)},ge.useRef=function(S){return J.H.useRef(S)},ge.useState=function(S){return J.H.useState(S)},ge.useSyncExternalStore=function(S,U,X){return J.H.useSyncExternalStore(S,U,X)},ge.useTransition=function(){return J.H.useTransition()},ge.version="19.1.0",ge}var Th;function Nu(){return Th||(Th=1,tu.exports=Tg()),tu.exports}var N=Nu();const $=e0(N),t0=wg({__proto__:null,default:$},[N]);var au={exports:{}},Vn={},lu={exports:{}},su={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ah;function Ag(){return Ah||(Ah=1,function(i){function c(R,Z){var V=R.length;R.push(Z);e:for(;0<V;){var fe=V-1>>>1,S=R[fe];if(0<d(S,Z))R[fe]=Z,R[V]=S,V=fe;else break e}}function o(R){return R.length===0?null:R[0]}function u(R){if(R.length===0)return null;var Z=R[0],V=R.pop();if(V!==Z){R[0]=V;e:for(var fe=0,S=R.length,U=S>>>1;fe<U;){var X=2*(fe+1)-1,G=R[X],W=X+1,ue=R[W];if(0>d(G,V))W<S&&0>d(ue,G)?(R[fe]=ue,R[W]=V,fe=W):(R[fe]=G,R[X]=V,fe=X);else if(W<S&&0>d(ue,V))R[fe]=ue,R[W]=V,fe=W;else break e}}return Z}function d(R,Z){var V=R.sortIndex-Z.sortIndex;return V!==0?V:R.id-Z.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;i.unstable_now=function(){return h.now()}}else{var y=Date,v=y.now();i.unstable_now=function(){return y.now()-v}}var g=[],m=[],b=1,x=null,j=3,_=!1,E=!1,L=!1,D=!1,B=typeof setTimeout=="function"?setTimeout:null,Q=typeof clearTimeout=="function"?clearTimeout:null,O=typeof setImmediate<"u"?setImmediate:null;function P(R){for(var Z=o(m);Z!==null;){if(Z.callback===null)u(m);else if(Z.startTime<=R)u(m),Z.sortIndex=Z.expirationTime,c(g,Z);else break;Z=o(m)}}function J(R){if(L=!1,P(R),!E)if(o(g)!==null)E=!0,ie||(ie=!0,Ae());else{var Z=o(m);Z!==null&&Re(J,Z.startTime-R)}}var ie=!1,pe=-1,K=5,I=-1;function Ee(){return D?!0:!(i.unstable_now()-I<K)}function Te(){if(D=!1,ie){var R=i.unstable_now();I=R;var Z=!0;try{e:{E=!1,L&&(L=!1,Q(pe),pe=-1),_=!0;var V=j;try{t:{for(P(R),x=o(g);x!==null&&!(x.expirationTime>R&&Ee());){var fe=x.callback;if(typeof fe=="function"){x.callback=null,j=x.priorityLevel;var S=fe(x.expirationTime<=R);if(R=i.unstable_now(),typeof S=="function"){x.callback=S,P(R),Z=!0;break t}x===o(g)&&u(g),P(R)}else u(g);x=o(g)}if(x!==null)Z=!0;else{var U=o(m);U!==null&&Re(J,U.startTime-R),Z=!1}}break e}finally{x=null,j=V,_=!1}Z=void 0}}finally{Z?Ae():ie=!1}}}var Ae;if(typeof O=="function")Ae=function(){O(Te)};else if(typeof MessageChannel<"u"){var ot=new MessageChannel,jt=ot.port2;ot.port1.onmessage=Te,Ae=function(){jt.postMessage(null)}}else Ae=function(){B(Te,0)};function Re(R,Z){pe=B(function(){R(i.unstable_now())},Z)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(R){R.callback=null},i.unstable_forceFrameRate=function(R){0>R||125<R?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):K=0<R?Math.floor(1e3/R):5},i.unstable_getCurrentPriorityLevel=function(){return j},i.unstable_next=function(R){switch(j){case 1:case 2:case 3:var Z=3;break;default:Z=j}var V=j;j=Z;try{return R()}finally{j=V}},i.unstable_requestPaint=function(){D=!0},i.unstable_runWithPriority=function(R,Z){switch(R){case 1:case 2:case 3:case 4:case 5:break;default:R=3}var V=j;j=R;try{return Z()}finally{j=V}},i.unstable_scheduleCallback=function(R,Z,V){var fe=i.unstable_now();switch(typeof V=="object"&&V!==null?(V=V.delay,V=typeof V=="number"&&0<V?fe+V:fe):V=fe,R){case 1:var S=-1;break;case 2:S=250;break;case 5:S=1073741823;break;case 4:S=1e4;break;default:S=5e3}return S=V+S,R={id:b++,callback:Z,priorityLevel:R,startTime:V,expirationTime:S,sortIndex:-1},V>fe?(R.sortIndex=V,c(m,R),o(g)===null&&R===o(m)&&(L?(Q(pe),pe=-1):L=!0,Re(J,V-fe))):(R.sortIndex=S,c(g,R),E||_||(E=!0,ie||(ie=!0,Ae()))),R},i.unstable_shouldYield=Ee,i.unstable_wrapCallback=function(R){var Z=j;return function(){var V=j;j=Z;try{return R.apply(this,arguments)}finally{j=V}}}}(su)),su}var Rh;function Rg(){return Rh||(Rh=1,lu.exports=Ag()),lu.exports}var nu={exports:{}},Et={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ch;function Cg(){if(Ch)return Et;Ch=1;var i=Nu();function c(g){var m="https://react.dev/errors/"+g;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var b=2;b<arguments.length;b++)m+="&args[]="+encodeURIComponent(arguments[b])}return"Minified React error #"+g+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var u={d:{f:o,r:function(){throw Error(c(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},d=Symbol.for("react.portal");function h(g,m,b){var x=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:d,key:x==null?null:""+x,children:g,containerInfo:m,implementation:b}}var y=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function v(g,m){if(g==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return Et.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=u,Et.createPortal=function(g,m){var b=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(c(299));return h(g,m,null,b)},Et.flushSync=function(g){var m=y.T,b=u.p;try{if(y.T=null,u.p=2,g)return g()}finally{y.T=m,u.p=b,u.d.f()}},Et.preconnect=function(g,m){typeof g=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,u.d.C(g,m))},Et.prefetchDNS=function(g){typeof g=="string"&&u.d.D(g)},Et.preinit=function(g,m){if(typeof g=="string"&&m&&typeof m.as=="string"){var b=m.as,x=v(b,m.crossOrigin),j=typeof m.integrity=="string"?m.integrity:void 0,_=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;b==="style"?u.d.S(g,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:x,integrity:j,fetchPriority:_}):b==="script"&&u.d.X(g,{crossOrigin:x,integrity:j,fetchPriority:_,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},Et.preinitModule=function(g,m){if(typeof g=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var b=v(m.as,m.crossOrigin);u.d.M(g,{crossOrigin:b,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&u.d.M(g)},Et.preload=function(g,m){if(typeof g=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var b=m.as,x=v(b,m.crossOrigin);u.d.L(g,b,{crossOrigin:x,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},Et.preloadModule=function(g,m){if(typeof g=="string")if(m){var b=v(m.as,m.crossOrigin);u.d.m(g,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:b,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else u.d.m(g)},Et.requestFormReset=function(g){u.d.r(g)},Et.unstable_batchedUpdates=function(g,m){return g(m)},Et.useFormState=function(g,m,b){return y.H.useFormState(g,m,b)},Et.useFormStatus=function(){return y.H.useHostTransitionStatus()},Et.version="19.1.0",Et}var Mh;function a0(){if(Mh)return nu.exports;Mh=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(c){console.error(c)}}return i(),nu.exports=Cg(),nu.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kh;function Mg(){if(kh)return Vn;kh=1;var i=Rg(),c=Nu(),o=a0();function u(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function h(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function y(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function v(e){if(h(e)!==e)throw Error(u(188))}function g(e){var t=e.alternate;if(!t){if(t=h(e),t===null)throw Error(u(188));return t!==e?null:e}for(var a=e,l=t;;){var n=a.return;if(n===null)break;var r=n.alternate;if(r===null){if(l=n.return,l!==null){a=l;continue}break}if(n.child===r.child){for(r=n.child;r;){if(r===a)return v(n),e;if(r===l)return v(n),t;r=r.sibling}throw Error(u(188))}if(a.return!==l.return)a=n,l=r;else{for(var f=!1,p=n.child;p;){if(p===a){f=!0,a=n,l=r;break}if(p===l){f=!0,l=n,a=r;break}p=p.sibling}if(!f){for(p=r.child;p;){if(p===a){f=!0,a=r,l=n;break}if(p===l){f=!0,l=r,a=n;break}p=p.sibling}if(!f)throw Error(u(189))}}if(a.alternate!==l)throw Error(u(190))}if(a.tag!==3)throw Error(u(188));return a.stateNode.current===a?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var b=Object.assign,x=Symbol.for("react.element"),j=Symbol.for("react.transitional.element"),_=Symbol.for("react.portal"),E=Symbol.for("react.fragment"),L=Symbol.for("react.strict_mode"),D=Symbol.for("react.profiler"),B=Symbol.for("react.provider"),Q=Symbol.for("react.consumer"),O=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),J=Symbol.for("react.suspense"),ie=Symbol.for("react.suspense_list"),pe=Symbol.for("react.memo"),K=Symbol.for("react.lazy"),I=Symbol.for("react.activity"),Ee=Symbol.for("react.memo_cache_sentinel"),Te=Symbol.iterator;function Ae(e){return e===null||typeof e!="object"?null:(e=Te&&e[Te]||e["@@iterator"],typeof e=="function"?e:null)}var ot=Symbol.for("react.client.reference");function jt(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ot?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case E:return"Fragment";case D:return"Profiler";case L:return"StrictMode";case J:return"Suspense";case ie:return"SuspenseList";case I:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case _:return"Portal";case O:return(e.displayName||"Context")+".Provider";case Q:return(e._context.displayName||"Context")+".Consumer";case P:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case pe:return t=e.displayName||null,t!==null?t:jt(e.type)||"Memo";case K:t=e._payload,e=e._init;try{return jt(e(t))}catch{}}return null}var Re=Array.isArray,R=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Z=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,V={pending:!1,data:null,method:null,action:null},fe=[],S=-1;function U(e){return{current:e}}function X(e){0>S||(e.current=fe[S],fe[S]=null,S--)}function G(e,t){S++,fe[S]=e.current,e.current=t}var W=U(null),ue=U(null),F=U(null),Ne=U(null);function Ce(e,t){switch(G(F,t),G(ue,e),G(W,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Wm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Wm(t),e=Fm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}X(W),G(W,e)}function lt(){X(W),X(ue),X(F)}function kt(e){e.memoizedState!==null&&G(Ne,e);var t=W.current,a=Fm(t,e.type);t!==a&&(G(ue,e),G(W,a))}function pa(e){ue.current===e&&(X(W),X(ue)),Ne.current===e&&(X(Ne),Bn._currentValue=V)}var ia=Object.prototype.hasOwnProperty,Xs=i.unstable_scheduleCallback,wl=i.unstable_cancelCallback,Vr=i.unstable_shouldYield,ii=i.unstable_requestPaint,wt=i.unstable_now,ri=i.unstable_getCurrentPriorityLevel,Jt=i.unstable_ImmediatePriority,ci=i.unstable_UserBlockingPriority,Zl=i.unstable_NormalPriority,Rt=i.unstable_LowPriority,Aa=i.unstable_IdlePriority,Xr=i.log,Qr=i.unstable_setDisableYieldValue,ga=null,Ct=null;function ra(e){if(typeof Xr=="function"&&Qr(e),Ct&&typeof Ct.setStrictMode=="function")try{Ct.setStrictMode(ga,e)}catch{}}var pt=Math.clz32?Math.clz32:Pa,Ja=Math.log,oi=Math.LN2;function Pa(e){return e>>>=0,e===0?32:31-(Ja(e)/oi|0)|0}var Kl=256,$l=4194304;function ya(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Sl(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var n=0,r=e.suspendedLanes,f=e.pingedLanes;e=e.warmLanes;var p=l&134217727;return p!==0?(l=p&~r,l!==0?n=ya(l):(f&=p,f!==0?n=ya(f):a||(a=p&~e,a!==0&&(n=ya(a))))):(p=l&~r,p!==0?n=ya(p):f!==0?n=ya(f):a||(a=l&~e,a!==0&&(n=ya(a)))),n===0?0:t!==0&&t!==n&&(t&r)===0&&(r=n&-n,a=t&-t,r>=a||r===32&&(a&4194048)!==0)?t:n}function El(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Qs(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ca(){var e=Kl;return Kl<<=1,(Kl&4194048)===0&&(Kl=256),e}function ui(){var e=$l;return $l<<=1,($l&62914560)===0&&($l=4194304),e}function Zs(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function Wa(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function di(e,t,a,l,n,r){var f=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var p=e.entanglements,w=e.expirationTimes,M=e.hiddenUpdates;for(a=f&~a;0<a;){var H=31-pt(a),Y=1<<H;p[H]=0,w[H]=-1;var k=M[H];if(k!==null)for(M[H]=null,H=0;H<k.length;H++){var z=k[H];z!==null&&(z.lane&=-536870913)}a&=~Y}l!==0&&de(e,l,0),r!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=r&~(f&~t))}function de(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-pt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function $e(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-pt(a),n=1<<l;n&t|e[l]&t&&(e[l]|=t),a&=~n}}function Ie(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function gt(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Tl(){var e=Z.p;return e!==0?e:(e=window.event,e===void 0?32:gh(e.type))}function st(e,t){var a=Z.p;try{return Z.p=e,t()}finally{Z.p=a}}var Je=Math.random().toString(36).slice(2),Pe="__reactFiber$"+Je,He="__reactProps$"+Je,ut="__reactContainer$"+Je,Jl="__reactEvents$"+Je,Ra="__reactListeners$"+Je,Bu="__reactHandles$"+Je,Lu="__reactResources$"+Je,Ks="__reactMarker$"+Je;function Zr(e){delete e[Pe],delete e[He],delete e[Jl],delete e[Ra],delete e[Bu]}function Pl(e){var t=e[Pe];if(t)return t;for(var a=e.parentNode;a;){if(t=a[ut]||a[Pe]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=ah(e);e!==null;){if(a=e[Pe])return a;e=ah(e)}return t}e=a,a=e.parentNode}return null}function Wl(e){if(e=e[Pe]||e[ut]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function $s(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(u(33))}function Fl(e){var t=e[Lu];return t||(t=e[Lu]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function dt(e){e[Ks]=!0}var Hu=new Set,qu={};function Al(e,t){Il(e,t),Il(e+"Capture",t)}function Il(e,t){for(qu[e]=t,e=0;e<t.length;e++)Hu.add(t[e])}var xx=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Gu={},Yu={};function px(e){return ia.call(Yu,e)?!0:ia.call(Gu,e)?!1:xx.test(e)?Yu[e]=!0:(Gu[e]=!0,!1)}function fi(e,t,a){if(px(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function mi(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function Ca(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var Kr,Vu;function es(e){if(Kr===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);Kr=t&&t[1]||"",Vu=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Kr+e+Vu}var $r=!1;function Jr(e,t){if(!e||$r)return"";$r=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var Y=function(){throw Error()};if(Object.defineProperty(Y.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Y,[])}catch(z){var k=z}Reflect.construct(e,[],Y)}else{try{Y.call()}catch(z){k=z}e.call(Y.prototype)}}else{try{throw Error()}catch(z){k=z}(Y=e())&&typeof Y.catch=="function"&&Y.catch(function(){})}}catch(z){if(z&&k&&typeof z.stack=="string")return[z.stack,k.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=l.DetermineComponentFrameRoot(),f=r[0],p=r[1];if(f&&p){var w=f.split(`
`),M=p.split(`
`);for(n=l=0;l<w.length&&!w[l].includes("DetermineComponentFrameRoot");)l++;for(;n<M.length&&!M[n].includes("DetermineComponentFrameRoot");)n++;if(l===w.length||n===M.length)for(l=w.length-1,n=M.length-1;1<=l&&0<=n&&w[l]!==M[n];)n--;for(;1<=l&&0<=n;l--,n--)if(w[l]!==M[n]){if(l!==1||n!==1)do if(l--,n--,0>n||w[l]!==M[n]){var H=`
`+w[l].replace(" at new "," at ");return e.displayName&&H.includes("<anonymous>")&&(H=H.replace("<anonymous>",e.displayName)),H}while(1<=l&&0<=n);break}}}finally{$r=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?es(a):""}function gx(e){switch(e.tag){case 26:case 27:case 5:return es(e.type);case 16:return es("Lazy");case 13:return es("Suspense");case 19:return es("SuspenseList");case 0:case 15:return Jr(e.type,!1);case 11:return Jr(e.type.render,!1);case 1:return Jr(e.type,!0);case 31:return es("Activity");default:return""}}function Xu(e){try{var t="";do t+=gx(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Pt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Qu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function yx(e){var t=Qu(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var n=a.get,r=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(f){l=""+f,r.call(this,f)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function hi(e){e._valueTracker||(e._valueTracker=yx(e))}function Zu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=Qu(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function xi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var vx=/[\n"\\]/g;function Wt(e){return e.replace(vx,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Pr(e,t,a,l,n,r,f,p){e.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?e.type=f:e.removeAttribute("type"),t!=null?f==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Pt(t)):e.value!==""+Pt(t)&&(e.value=""+Pt(t)):f!=="submit"&&f!=="reset"||e.removeAttribute("value"),t!=null?Wr(e,f,Pt(t)):a!=null?Wr(e,f,Pt(a)):l!=null&&e.removeAttribute("value"),n==null&&r!=null&&(e.defaultChecked=!!r),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?e.name=""+Pt(p):e.removeAttribute("name")}function Ku(e,t,a,l,n,r,f,p){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(e.type=r),t!=null||a!=null){if(!(r!=="submit"&&r!=="reset"||t!=null))return;a=a!=null?""+Pt(a):"",t=t!=null?""+Pt(t):a,p||t===e.value||(e.value=t),e.defaultValue=t}l=l??n,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=p?e.checked:!!l,e.defaultChecked=!!l,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.name=f)}function Wr(e,t,a){t==="number"&&xi(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function ts(e,t,a,l){if(e=e.options,t){t={};for(var n=0;n<a.length;n++)t["$"+a[n]]=!0;for(a=0;a<e.length;a++)n=t.hasOwnProperty("$"+e[a].value),e[a].selected!==n&&(e[a].selected=n),n&&l&&(e[a].defaultSelected=!0)}else{for(a=""+Pt(a),t=null,n=0;n<e.length;n++){if(e[n].value===a){e[n].selected=!0,l&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function $u(e,t,a){if(t!=null&&(t=""+Pt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Pt(a):""}function Ju(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(u(92));if(Re(l)){if(1<l.length)throw Error(u(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=Pt(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function as(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var bx=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Pu(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||bx.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function Wu(e,t,a){if(t!=null&&typeof t!="object")throw Error(u(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var n in t)l=t[n],t.hasOwnProperty(n)&&a[n]!==l&&Pu(e,n,l)}else for(var r in t)t.hasOwnProperty(r)&&Pu(e,r,t[r])}function Fr(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Nx=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),jx=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function pi(e){return jx.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ir=null;function ec(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ls=null,ss=null;function Fu(e){var t=Wl(e);if(t&&(e=t.stateNode)){var a=e[He]||null;e:switch(e=t.stateNode,t.type){case"input":if(Pr(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Wt(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var n=l[He]||null;if(!n)throw Error(u(90));Pr(l,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&Zu(l)}break e;case"textarea":$u(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&ts(e,!!a.multiple,t,!1)}}}var tc=!1;function Iu(e,t,a){if(tc)return e(t,a);tc=!0;try{var l=e(t);return l}finally{if(tc=!1,(ls!==null||ss!==null)&&(tr(),ls&&(t=ls,e=ss,ss=ls=null,Fu(t),e)))for(t=0;t<e.length;t++)Fu(e[t])}}function Js(e,t){var a=e.stateNode;if(a===null)return null;var l=a[He]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(u(231,t,typeof a));return a}var Ma=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ac=!1;if(Ma)try{var Ps={};Object.defineProperty(Ps,"passive",{get:function(){ac=!0}}),window.addEventListener("test",Ps,Ps),window.removeEventListener("test",Ps,Ps)}catch{ac=!1}var Fa=null,lc=null,gi=null;function ed(){if(gi)return gi;var e,t=lc,a=t.length,l,n="value"in Fa?Fa.value:Fa.textContent,r=n.length;for(e=0;e<a&&t[e]===n[e];e++);var f=a-e;for(l=1;l<=f&&t[a-l]===n[r-l];l++);return gi=n.slice(e,1<l?1-l:void 0)}function yi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function vi(){return!0}function td(){return!1}function _t(e){function t(a,l,n,r,f){this._reactName=a,this._targetInst=n,this.type=l,this.nativeEvent=r,this.target=f,this.currentTarget=null;for(var p in e)e.hasOwnProperty(p)&&(a=e[p],this[p]=a?a(r):r[p]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?vi:td,this.isPropagationStopped=td,this}return b(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=vi)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=vi)},persist:function(){},isPersistent:vi}),t}var Rl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},bi=_t(Rl),Ws=b({},Rl,{view:0,detail:0}),wx=_t(Ws),sc,nc,Fs,Ni=b({},Ws,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:rc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Fs&&(Fs&&e.type==="mousemove"?(sc=e.screenX-Fs.screenX,nc=e.screenY-Fs.screenY):nc=sc=0,Fs=e),sc)},movementY:function(e){return"movementY"in e?e.movementY:nc}}),ad=_t(Ni),Sx=b({},Ni,{dataTransfer:0}),Ex=_t(Sx),Tx=b({},Ws,{relatedTarget:0}),ic=_t(Tx),Ax=b({},Rl,{animationName:0,elapsedTime:0,pseudoElement:0}),Rx=_t(Ax),Cx=b({},Rl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Mx=_t(Cx),kx=b({},Rl,{data:0}),ld=_t(kx),_x={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},zx={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Dx={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ox(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Dx[e])?!!t[e]:!1}function rc(){return Ox}var Ux=b({},Ws,{key:function(e){if(e.key){var t=_x[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=yi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?zx[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:rc,charCode:function(e){return e.type==="keypress"?yi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?yi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Bx=_t(Ux),Lx=b({},Ni,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),sd=_t(Lx),Hx=b({},Ws,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:rc}),qx=_t(Hx),Gx=b({},Rl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Yx=_t(Gx),Vx=b({},Ni,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Xx=_t(Vx),Qx=b({},Rl,{newState:0,oldState:0}),Zx=_t(Qx),Kx=[9,13,27,32],cc=Ma&&"CompositionEvent"in window,Is=null;Ma&&"documentMode"in document&&(Is=document.documentMode);var $x=Ma&&"TextEvent"in window&&!Is,nd=Ma&&(!cc||Is&&8<Is&&11>=Is),id=" ",rd=!1;function cd(e,t){switch(e){case"keyup":return Kx.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function od(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ns=!1;function Jx(e,t){switch(e){case"compositionend":return od(t);case"keypress":return t.which!==32?null:(rd=!0,id);case"textInput":return e=t.data,e===id&&rd?null:e;default:return null}}function Px(e,t){if(ns)return e==="compositionend"||!cc&&cd(e,t)?(e=ed(),gi=lc=Fa=null,ns=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return nd&&t.locale!=="ko"?null:t.data;default:return null}}var Wx={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ud(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Wx[e.type]:t==="textarea"}function dd(e,t,a,l){ls?ss?ss.push(l):ss=[l]:ls=l,t=rr(t,"onChange"),0<t.length&&(a=new bi("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var en=null,tn=null;function Fx(e){Zm(e,0)}function ji(e){var t=$s(e);if(Zu(t))return e}function fd(e,t){if(e==="change")return t}var md=!1;if(Ma){var oc;if(Ma){var uc="oninput"in document;if(!uc){var hd=document.createElement("div");hd.setAttribute("oninput","return;"),uc=typeof hd.oninput=="function"}oc=uc}else oc=!1;md=oc&&(!document.documentMode||9<document.documentMode)}function xd(){en&&(en.detachEvent("onpropertychange",pd),tn=en=null)}function pd(e){if(e.propertyName==="value"&&ji(tn)){var t=[];dd(t,tn,e,ec(e)),Iu(Fx,t)}}function Ix(e,t,a){e==="focusin"?(xd(),en=t,tn=a,en.attachEvent("onpropertychange",pd)):e==="focusout"&&xd()}function ep(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ji(tn)}function tp(e,t){if(e==="click")return ji(t)}function ap(e,t){if(e==="input"||e==="change")return ji(t)}function lp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Gt=typeof Object.is=="function"?Object.is:lp;function an(e,t){if(Gt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var n=a[l];if(!ia.call(t,n)||!Gt(e[n],t[n]))return!1}return!0}function gd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function yd(e,t){var a=gd(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=gd(a)}}function vd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?vd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function bd(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=xi(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=xi(e.document)}return t}function dc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var sp=Ma&&"documentMode"in document&&11>=document.documentMode,is=null,fc=null,ln=null,mc=!1;function Nd(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;mc||is==null||is!==xi(l)||(l=is,"selectionStart"in l&&dc(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),ln&&an(ln,l)||(ln=l,l=rr(fc,"onSelect"),0<l.length&&(t=new bi("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=is)))}function Cl(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var rs={animationend:Cl("Animation","AnimationEnd"),animationiteration:Cl("Animation","AnimationIteration"),animationstart:Cl("Animation","AnimationStart"),transitionrun:Cl("Transition","TransitionRun"),transitionstart:Cl("Transition","TransitionStart"),transitioncancel:Cl("Transition","TransitionCancel"),transitionend:Cl("Transition","TransitionEnd")},hc={},jd={};Ma&&(jd=document.createElement("div").style,"AnimationEvent"in window||(delete rs.animationend.animation,delete rs.animationiteration.animation,delete rs.animationstart.animation),"TransitionEvent"in window||delete rs.transitionend.transition);function Ml(e){if(hc[e])return hc[e];if(!rs[e])return e;var t=rs[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in jd)return hc[e]=t[a];return e}var wd=Ml("animationend"),Sd=Ml("animationiteration"),Ed=Ml("animationstart"),np=Ml("transitionrun"),ip=Ml("transitionstart"),rp=Ml("transitioncancel"),Td=Ml("transitionend"),Ad=new Map,xc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");xc.push("scrollEnd");function oa(e,t){Ad.set(e,t),Al(t,[e])}var Rd=new WeakMap;function Ft(e,t){if(typeof e=="object"&&e!==null){var a=Rd.get(e);return a!==void 0?a:(t={value:e,source:t,stack:Xu(t)},Rd.set(e,t),t)}return{value:e,source:t,stack:Xu(t)}}var It=[],cs=0,pc=0;function wi(){for(var e=cs,t=pc=cs=0;t<e;){var a=It[t];It[t++]=null;var l=It[t];It[t++]=null;var n=It[t];It[t++]=null;var r=It[t];if(It[t++]=null,l!==null&&n!==null){var f=l.pending;f===null?n.next=n:(n.next=f.next,f.next=n),l.pending=n}r!==0&&Cd(a,n,r)}}function Si(e,t,a,l){It[cs++]=e,It[cs++]=t,It[cs++]=a,It[cs++]=l,pc|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function gc(e,t,a,l){return Si(e,t,a,l),Ei(e)}function os(e,t){return Si(e,null,null,t),Ei(e)}function Cd(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var n=!1,r=e.return;r!==null;)r.childLanes|=a,l=r.alternate,l!==null&&(l.childLanes|=a),r.tag===22&&(e=r.stateNode,e===null||e._visibility&1||(n=!0)),e=r,r=r.return;return e.tag===3?(r=e.stateNode,n&&t!==null&&(n=31-pt(a),e=r.hiddenUpdates,l=e[n],l===null?e[n]=[t]:l.push(t),t.lane=a|536870912),r):null}function Ei(e){if(50<Cn)throw Cn=0,So=null,Error(u(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var us={};function cp(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Yt(e,t,a,l){return new cp(e,t,a,l)}function yc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ka(e,t){var a=e.alternate;return a===null?(a=Yt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Md(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ti(e,t,a,l,n,r){var f=0;if(l=e,typeof e=="function")yc(e)&&(f=1);else if(typeof e=="string")f=ug(e,a,W.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case I:return e=Yt(31,a,t,n),e.elementType=I,e.lanes=r,e;case E:return kl(a.children,n,r,t);case L:f=8,n|=24;break;case D:return e=Yt(12,a,t,n|2),e.elementType=D,e.lanes=r,e;case J:return e=Yt(13,a,t,n),e.elementType=J,e.lanes=r,e;case ie:return e=Yt(19,a,t,n),e.elementType=ie,e.lanes=r,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case B:case O:f=10;break e;case Q:f=9;break e;case P:f=11;break e;case pe:f=14;break e;case K:f=16,l=null;break e}f=29,a=Error(u(130,e===null?"null":typeof e,"")),l=null}return t=Yt(f,a,t,n),t.elementType=e,t.type=l,t.lanes=r,t}function kl(e,t,a,l){return e=Yt(7,e,l,t),e.lanes=a,e}function vc(e,t,a){return e=Yt(6,e,null,t),e.lanes=a,e}function bc(e,t,a){return t=Yt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var ds=[],fs=0,Ai=null,Ri=0,ea=[],ta=0,_l=null,_a=1,za="";function zl(e,t){ds[fs++]=Ri,ds[fs++]=Ai,Ai=e,Ri=t}function kd(e,t,a){ea[ta++]=_a,ea[ta++]=za,ea[ta++]=_l,_l=e;var l=_a;e=za;var n=32-pt(l)-1;l&=~(1<<n),a+=1;var r=32-pt(t)+n;if(30<r){var f=n-n%5;r=(l&(1<<f)-1).toString(32),l>>=f,n-=f,_a=1<<32-pt(t)+n|a<<n|l,za=r+e}else _a=1<<r|a<<n|l,za=e}function Nc(e){e.return!==null&&(zl(e,1),kd(e,1,0))}function jc(e){for(;e===Ai;)Ai=ds[--fs],ds[fs]=null,Ri=ds[--fs],ds[fs]=null;for(;e===_l;)_l=ea[--ta],ea[ta]=null,za=ea[--ta],ea[ta]=null,_a=ea[--ta],ea[ta]=null}var Mt=null,Ze=null,_e=!1,Dl=null,va=!1,wc=Error(u(519));function Ol(e){var t=Error(u(418,""));throw rn(Ft(t,e)),wc}function _d(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[Pe]=e,t[He]=l,a){case"dialog":Se("cancel",t),Se("close",t);break;case"iframe":case"object":case"embed":Se("load",t);break;case"video":case"audio":for(a=0;a<kn.length;a++)Se(kn[a],t);break;case"source":Se("error",t);break;case"img":case"image":case"link":Se("error",t),Se("load",t);break;case"details":Se("toggle",t);break;case"input":Se("invalid",t),Ku(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),hi(t);break;case"select":Se("invalid",t);break;case"textarea":Se("invalid",t),Ju(t,l.value,l.defaultValue,l.children),hi(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||Pm(t.textContent,a)?(l.popover!=null&&(Se("beforetoggle",t),Se("toggle",t)),l.onScroll!=null&&Se("scroll",t),l.onScrollEnd!=null&&Se("scrollend",t),l.onClick!=null&&(t.onclick=cr),t=!0):t=!1,t||Ol(e)}function zd(e){for(Mt=e.return;Mt;)switch(Mt.tag){case 5:case 13:va=!1;return;case 27:case 3:va=!0;return;default:Mt=Mt.return}}function sn(e){if(e!==Mt)return!1;if(!_e)return zd(e),_e=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||qo(e.type,e.memoizedProps)),a=!a),a&&Ze&&Ol(e),zd(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(u(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){Ze=da(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}Ze=null}}else t===27?(t=Ze,hl(e.type)?(e=Xo,Xo=null,Ze=e):Ze=t):Ze=Mt?da(e.stateNode.nextSibling):null;return!0}function nn(){Ze=Mt=null,_e=!1}function Dd(){var e=Dl;return e!==null&&(Ot===null?Ot=e:Ot.push.apply(Ot,e),Dl=null),e}function rn(e){Dl===null?Dl=[e]:Dl.push(e)}var Sc=U(null),Ul=null,Da=null;function Ia(e,t,a){G(Sc,t._currentValue),t._currentValue=a}function Oa(e){e._currentValue=Sc.current,X(Sc)}function Ec(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function Tc(e,t,a,l){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var r=n.dependencies;if(r!==null){var f=n.child;r=r.firstContext;e:for(;r!==null;){var p=r;r=n;for(var w=0;w<t.length;w++)if(p.context===t[w]){r.lanes|=a,p=r.alternate,p!==null&&(p.lanes|=a),Ec(r.return,a,e),l||(f=null);break e}r=p.next}}else if(n.tag===18){if(f=n.return,f===null)throw Error(u(341));f.lanes|=a,r=f.alternate,r!==null&&(r.lanes|=a),Ec(f,a,e),f=null}else f=n.child;if(f!==null)f.return=n;else for(f=n;f!==null;){if(f===e){f=null;break}if(n=f.sibling,n!==null){n.return=f.return,f=n;break}f=f.return}n=f}}function cn(e,t,a,l){e=null;for(var n=t,r=!1;n!==null;){if(!r){if((n.flags&524288)!==0)r=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var f=n.alternate;if(f===null)throw Error(u(387));if(f=f.memoizedProps,f!==null){var p=n.type;Gt(n.pendingProps.value,f.value)||(e!==null?e.push(p):e=[p])}}else if(n===Ne.current){if(f=n.alternate,f===null)throw Error(u(387));f.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(Bn):e=[Bn])}n=n.return}e!==null&&Tc(t,e,a,l),t.flags|=262144}function Ci(e){for(e=e.firstContext;e!==null;){if(!Gt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Bl(e){Ul=e,Da=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function St(e){return Od(Ul,e)}function Mi(e,t){return Ul===null&&Bl(e),Od(e,t)}function Od(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},Da===null){if(e===null)throw Error(u(308));Da=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Da=Da.next=t;return a}var op=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},up=i.unstable_scheduleCallback,dp=i.unstable_NormalPriority,nt={$$typeof:O,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ac(){return{controller:new op,data:new Map,refCount:0}}function on(e){e.refCount--,e.refCount===0&&up(dp,function(){e.controller.abort()})}var un=null,Rc=0,ms=0,hs=null;function fp(e,t){if(un===null){var a=un=[];Rc=0,ms=ko(),hs={status:"pending",value:void 0,then:function(l){a.push(l)}}}return Rc++,t.then(Ud,Ud),t}function Ud(){if(--Rc===0&&un!==null){hs!==null&&(hs.status="fulfilled");var e=un;un=null,ms=0,hs=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function mp(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(n){a.push(n)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var n=0;n<a.length;n++)(0,a[n])(t)},function(n){for(l.status="rejected",l.reason=n,n=0;n<a.length;n++)(0,a[n])(void 0)}),l}var Bd=R.S;R.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&fp(e,t),Bd!==null&&Bd(e,t)};var Ll=U(null);function Cc(){var e=Ll.current;return e!==null?e:qe.pooledCache}function ki(e,t){t===null?G(Ll,Ll.current):G(Ll,t.pool)}function Ld(){var e=Cc();return e===null?null:{parent:nt._currentValue,pool:e}}var dn=Error(u(460)),Hd=Error(u(474)),_i=Error(u(542)),Mc={then:function(){}};function qd(e){return e=e.status,e==="fulfilled"||e==="rejected"}function zi(){}function Gd(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(zi,zi),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Vd(e),e;default:if(typeof t.status=="string")t.then(zi,zi);else{if(e=qe,e!==null&&100<e.shellSuspendCounter)throw Error(u(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=l}},function(l){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Vd(e),e}throw fn=t,dn}}var fn=null;function Yd(){if(fn===null)throw Error(u(459));var e=fn;return fn=null,e}function Vd(e){if(e===dn||e===_i)throw Error(u(483))}var el=!1;function kc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function _c(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function tl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function al(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(ze&2)!==0){var n=l.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),l.pending=t,t=Ei(e),Cd(e,null,a),t}return Si(e,l,t,a),Ei(e)}function mn(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,$e(e,a)}}function zc(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var n=null,r=null;if(a=a.firstBaseUpdate,a!==null){do{var f={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};r===null?n=r=f:r=r.next=f,a=a.next}while(a!==null);r===null?n=r=t:r=r.next=t}else n=r=t;a={baseState:l.baseState,firstBaseUpdate:n,lastBaseUpdate:r,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var Dc=!1;function hn(){if(Dc){var e=hs;if(e!==null)throw e}}function xn(e,t,a,l){Dc=!1;var n=e.updateQueue;el=!1;var r=n.firstBaseUpdate,f=n.lastBaseUpdate,p=n.shared.pending;if(p!==null){n.shared.pending=null;var w=p,M=w.next;w.next=null,f===null?r=M:f.next=M,f=w;var H=e.alternate;H!==null&&(H=H.updateQueue,p=H.lastBaseUpdate,p!==f&&(p===null?H.firstBaseUpdate=M:p.next=M,H.lastBaseUpdate=w))}if(r!==null){var Y=n.baseState;f=0,H=M=w=null,p=r;do{var k=p.lane&-536870913,z=k!==p.lane;if(z?(Me&k)===k:(l&k)===k){k!==0&&k===ms&&(Dc=!0),H!==null&&(H=H.next={lane:0,tag:p.tag,payload:p.payload,callback:null,next:null});e:{var re=e,se=p;k=t;var Be=a;switch(se.tag){case 1:if(re=se.payload,typeof re=="function"){Y=re.call(Be,Y,k);break e}Y=re;break e;case 3:re.flags=re.flags&-65537|128;case 0:if(re=se.payload,k=typeof re=="function"?re.call(Be,Y,k):re,k==null)break e;Y=b({},Y,k);break e;case 2:el=!0}}k=p.callback,k!==null&&(e.flags|=64,z&&(e.flags|=8192),z=n.callbacks,z===null?n.callbacks=[k]:z.push(k))}else z={lane:k,tag:p.tag,payload:p.payload,callback:p.callback,next:null},H===null?(M=H=z,w=Y):H=H.next=z,f|=k;if(p=p.next,p===null){if(p=n.shared.pending,p===null)break;z=p,p=z.next,z.next=null,n.lastBaseUpdate=z,n.shared.pending=null}}while(!0);H===null&&(w=Y),n.baseState=w,n.firstBaseUpdate=M,n.lastBaseUpdate=H,r===null&&(n.shared.lanes=0),ul|=f,e.lanes=f,e.memoizedState=Y}}function Xd(e,t){if(typeof e!="function")throw Error(u(191,e));e.call(t)}function Qd(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)Xd(a[e],t)}var xs=U(null),Di=U(0);function Zd(e,t){e=Ya,G(Di,e),G(xs,t),Ya=e|t.baseLanes}function Oc(){G(Di,Ya),G(xs,xs.current)}function Uc(){Ya=Di.current,X(xs),X(Di)}var ll=0,ve=null,Oe=null,et=null,Oi=!1,ps=!1,Hl=!1,Ui=0,pn=0,gs=null,hp=0;function We(){throw Error(u(321))}function Bc(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!Gt(e[a],t[a]))return!1;return!0}function Lc(e,t,a,l,n,r){return ll=r,ve=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,R.H=e===null||e.memoizedState===null?Mf:kf,Hl=!1,r=a(l,n),Hl=!1,ps&&(r=$d(t,a,l,n)),Kd(e),r}function Kd(e){R.H=Yi;var t=Oe!==null&&Oe.next!==null;if(ll=0,et=Oe=ve=null,Oi=!1,pn=0,gs=null,t)throw Error(u(300));e===null||ft||(e=e.dependencies,e!==null&&Ci(e)&&(ft=!0))}function $d(e,t,a,l){ve=e;var n=0;do{if(ps&&(gs=null),pn=0,ps=!1,25<=n)throw Error(u(301));if(n+=1,et=Oe=null,e.updateQueue!=null){var r=e.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}R.H=Np,r=t(a,l)}while(ps);return r}function xp(){var e=R.H,t=e.useState()[0];return t=typeof t.then=="function"?gn(t):t,e=e.useState()[0],(Oe!==null?Oe.memoizedState:null)!==e&&(ve.flags|=1024),t}function Hc(){var e=Ui!==0;return Ui=0,e}function qc(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function Gc(e){if(Oi){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Oi=!1}ll=0,et=Oe=ve=null,ps=!1,pn=Ui=0,gs=null}function zt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return et===null?ve.memoizedState=et=e:et=et.next=e,et}function tt(){if(Oe===null){var e=ve.alternate;e=e!==null?e.memoizedState:null}else e=Oe.next;var t=et===null?ve.memoizedState:et.next;if(t!==null)et=t,Oe=e;else{if(e===null)throw ve.alternate===null?Error(u(467)):Error(u(310));Oe=e,e={memoizedState:Oe.memoizedState,baseState:Oe.baseState,baseQueue:Oe.baseQueue,queue:Oe.queue,next:null},et===null?ve.memoizedState=et=e:et=et.next=e}return et}function Yc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function gn(e){var t=pn;return pn+=1,gs===null&&(gs=[]),e=Gd(gs,e,t),t=ve,(et===null?t.memoizedState:et.next)===null&&(t=t.alternate,R.H=t===null||t.memoizedState===null?Mf:kf),e}function Bi(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return gn(e);if(e.$$typeof===O)return St(e)}throw Error(u(438,String(e)))}function Vc(e){var t=null,a=ve.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=ve.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Yc(),ve.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=Ee;return t.index++,a}function Ua(e,t){return typeof t=="function"?t(e):t}function Li(e){var t=tt();return Xc(t,Oe,e)}function Xc(e,t,a){var l=e.queue;if(l===null)throw Error(u(311));l.lastRenderedReducer=a;var n=e.baseQueue,r=l.pending;if(r!==null){if(n!==null){var f=n.next;n.next=r.next,r.next=f}t.baseQueue=n=r,l.pending=null}if(r=e.baseState,n===null)e.memoizedState=r;else{t=n.next;var p=f=null,w=null,M=t,H=!1;do{var Y=M.lane&-536870913;if(Y!==M.lane?(Me&Y)===Y:(ll&Y)===Y){var k=M.revertLane;if(k===0)w!==null&&(w=w.next={lane:0,revertLane:0,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null}),Y===ms&&(H=!0);else if((ll&k)===k){M=M.next,k===ms&&(H=!0);continue}else Y={lane:0,revertLane:M.revertLane,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null},w===null?(p=w=Y,f=r):w=w.next=Y,ve.lanes|=k,ul|=k;Y=M.action,Hl&&a(r,Y),r=M.hasEagerState?M.eagerState:a(r,Y)}else k={lane:Y,revertLane:M.revertLane,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null},w===null?(p=w=k,f=r):w=w.next=k,ve.lanes|=Y,ul|=Y;M=M.next}while(M!==null&&M!==t);if(w===null?f=r:w.next=p,!Gt(r,e.memoizedState)&&(ft=!0,H&&(a=hs,a!==null)))throw a;e.memoizedState=r,e.baseState=f,e.baseQueue=w,l.lastRenderedState=r}return n===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Qc(e){var t=tt(),a=t.queue;if(a===null)throw Error(u(311));a.lastRenderedReducer=e;var l=a.dispatch,n=a.pending,r=t.memoizedState;if(n!==null){a.pending=null;var f=n=n.next;do r=e(r,f.action),f=f.next;while(f!==n);Gt(r,t.memoizedState)||(ft=!0),t.memoizedState=r,t.baseQueue===null&&(t.baseState=r),a.lastRenderedState=r}return[r,l]}function Jd(e,t,a){var l=ve,n=tt(),r=_e;if(r){if(a===void 0)throw Error(u(407));a=a()}else a=t();var f=!Gt((Oe||n).memoizedState,a);f&&(n.memoizedState=a,ft=!0),n=n.queue;var p=Fd.bind(null,l,n,e);if(yn(2048,8,p,[e]),n.getSnapshot!==t||f||et!==null&&et.memoizedState.tag&1){if(l.flags|=2048,ys(9,Hi(),Wd.bind(null,l,n,a,t),null),qe===null)throw Error(u(349));r||(ll&124)!==0||Pd(l,t,a)}return a}function Pd(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=ve.updateQueue,t===null?(t=Yc(),ve.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function Wd(e,t,a,l){t.value=a,t.getSnapshot=l,Id(t)&&ef(e)}function Fd(e,t,a){return a(function(){Id(t)&&ef(e)})}function Id(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!Gt(e,a)}catch{return!0}}function ef(e){var t=os(e,2);t!==null&&Kt(t,e,2)}function Zc(e){var t=zt();if(typeof e=="function"){var a=e;if(e=a(),Hl){ra(!0);try{a()}finally{ra(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ua,lastRenderedState:e},t}function tf(e,t,a,l){return e.baseState=a,Xc(e,Oe,typeof l=="function"?l:Ua)}function pp(e,t,a,l,n){if(Gi(e))throw Error(u(485));if(e=t.action,e!==null){var r={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){r.listeners.push(f)}};R.T!==null?a(!0):r.isTransition=!1,l(r),a=t.pending,a===null?(r.next=t.pending=r,af(t,r)):(r.next=a.next,t.pending=a.next=r)}}function af(e,t){var a=t.action,l=t.payload,n=e.state;if(t.isTransition){var r=R.T,f={};R.T=f;try{var p=a(n,l),w=R.S;w!==null&&w(f,p),lf(e,t,p)}catch(M){Kc(e,t,M)}finally{R.T=r}}else try{r=a(n,l),lf(e,t,r)}catch(M){Kc(e,t,M)}}function lf(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){sf(e,t,l)},function(l){return Kc(e,t,l)}):sf(e,t,a)}function sf(e,t,a){t.status="fulfilled",t.value=a,nf(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,af(e,a)))}function Kc(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,nf(t),t=t.next;while(t!==l)}e.action=null}function nf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function rf(e,t){return t}function cf(e,t){if(_e){var a=qe.formState;if(a!==null){e:{var l=ve;if(_e){if(Ze){t:{for(var n=Ze,r=va;n.nodeType!==8;){if(!r){n=null;break t}if(n=da(n.nextSibling),n===null){n=null;break t}}r=n.data,n=r==="F!"||r==="F"?n:null}if(n){Ze=da(n.nextSibling),l=n.data==="F!";break e}}Ol(l)}l=!1}l&&(t=a[0])}}return a=zt(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:rf,lastRenderedState:t},a.queue=l,a=Af.bind(null,ve,l),l.dispatch=a,l=Zc(!1),r=Fc.bind(null,ve,!1,l.queue),l=zt(),n={state:t,dispatch:null,action:e,pending:null},l.queue=n,a=pp.bind(null,ve,n,r,a),n.dispatch=a,l.memoizedState=e,[t,a,!1]}function of(e){var t=tt();return uf(t,Oe,e)}function uf(e,t,a){if(t=Xc(e,t,rf)[0],e=Li(Ua)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=gn(t)}catch(f){throw f===dn?_i:f}else l=t;t=tt();var n=t.queue,r=n.dispatch;return a!==t.memoizedState&&(ve.flags|=2048,ys(9,Hi(),gp.bind(null,n,a),null)),[l,r,e]}function gp(e,t){e.action=t}function df(e){var t=tt(),a=Oe;if(a!==null)return uf(t,a,e);tt(),t=t.memoizedState,a=tt();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function ys(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=ve.updateQueue,t===null&&(t=Yc(),ve.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function Hi(){return{destroy:void 0,resource:void 0}}function ff(){return tt().memoizedState}function qi(e,t,a,l){var n=zt();l=l===void 0?null:l,ve.flags|=e,n.memoizedState=ys(1|t,Hi(),a,l)}function yn(e,t,a,l){var n=tt();l=l===void 0?null:l;var r=n.memoizedState.inst;Oe!==null&&l!==null&&Bc(l,Oe.memoizedState.deps)?n.memoizedState=ys(t,r,a,l):(ve.flags|=e,n.memoizedState=ys(1|t,r,a,l))}function mf(e,t){qi(8390656,8,e,t)}function hf(e,t){yn(2048,8,e,t)}function xf(e,t){return yn(4,2,e,t)}function pf(e,t){return yn(4,4,e,t)}function gf(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function yf(e,t,a){a=a!=null?a.concat([e]):null,yn(4,4,gf.bind(null,t,e),a)}function $c(){}function vf(e,t){var a=tt();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&Bc(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function bf(e,t){var a=tt();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&Bc(t,l[1]))return l[0];if(l=e(),Hl){ra(!0);try{e()}finally{ra(!1)}}return a.memoizedState=[l,t],l}function Jc(e,t,a){return a===void 0||(ll&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=wm(),ve.lanes|=e,ul|=e,a)}function Nf(e,t,a,l){return Gt(a,t)?a:xs.current!==null?(e=Jc(e,a,l),Gt(e,t)||(ft=!0),e):(ll&42)===0?(ft=!0,e.memoizedState=a):(e=wm(),ve.lanes|=e,ul|=e,t)}function jf(e,t,a,l,n){var r=Z.p;Z.p=r!==0&&8>r?r:8;var f=R.T,p={};R.T=p,Fc(e,!1,t,a);try{var w=n(),M=R.S;if(M!==null&&M(p,w),w!==null&&typeof w=="object"&&typeof w.then=="function"){var H=mp(w,l);vn(e,t,H,Zt(e))}else vn(e,t,l,Zt(e))}catch(Y){vn(e,t,{then:function(){},status:"rejected",reason:Y},Zt())}finally{Z.p=r,R.T=f}}function yp(){}function Pc(e,t,a,l){if(e.tag!==5)throw Error(u(476));var n=wf(e).queue;jf(e,n,t,V,a===null?yp:function(){return Sf(e),a(l)})}function wf(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:V,baseState:V,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ua,lastRenderedState:V},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ua,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Sf(e){var t=wf(e).next.queue;vn(e,t,{},Zt())}function Wc(){return St(Bn)}function Ef(){return tt().memoizedState}function Tf(){return tt().memoizedState}function vp(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=Zt();e=tl(a);var l=al(t,e,a);l!==null&&(Kt(l,t,a),mn(l,t,a)),t={cache:Ac()},e.payload=t;return}t=t.return}}function bp(e,t,a){var l=Zt();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Gi(e)?Rf(t,a):(a=gc(e,t,a,l),a!==null&&(Kt(a,e,l),Cf(a,t,l)))}function Af(e,t,a){var l=Zt();vn(e,t,a,l)}function vn(e,t,a,l){var n={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Gi(e))Rf(t,n);else{var r=e.alternate;if(e.lanes===0&&(r===null||r.lanes===0)&&(r=t.lastRenderedReducer,r!==null))try{var f=t.lastRenderedState,p=r(f,a);if(n.hasEagerState=!0,n.eagerState=p,Gt(p,f))return Si(e,t,n,0),qe===null&&wi(),!1}catch{}finally{}if(a=gc(e,t,n,l),a!==null)return Kt(a,e,l),Cf(a,t,l),!0}return!1}function Fc(e,t,a,l){if(l={lane:2,revertLane:ko(),action:l,hasEagerState:!1,eagerState:null,next:null},Gi(e)){if(t)throw Error(u(479))}else t=gc(e,a,l,2),t!==null&&Kt(t,e,2)}function Gi(e){var t=e.alternate;return e===ve||t!==null&&t===ve}function Rf(e,t){ps=Oi=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function Cf(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,$e(e,a)}}var Yi={readContext:St,use:Bi,useCallback:We,useContext:We,useEffect:We,useImperativeHandle:We,useLayoutEffect:We,useInsertionEffect:We,useMemo:We,useReducer:We,useRef:We,useState:We,useDebugValue:We,useDeferredValue:We,useTransition:We,useSyncExternalStore:We,useId:We,useHostTransitionStatus:We,useFormState:We,useActionState:We,useOptimistic:We,useMemoCache:We,useCacheRefresh:We},Mf={readContext:St,use:Bi,useCallback:function(e,t){return zt().memoizedState=[e,t===void 0?null:t],e},useContext:St,useEffect:mf,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,qi(4194308,4,gf.bind(null,t,e),a)},useLayoutEffect:function(e,t){return qi(4194308,4,e,t)},useInsertionEffect:function(e,t){qi(4,2,e,t)},useMemo:function(e,t){var a=zt();t=t===void 0?null:t;var l=e();if(Hl){ra(!0);try{e()}finally{ra(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=zt();if(a!==void 0){var n=a(t);if(Hl){ra(!0);try{a(t)}finally{ra(!1)}}}else n=t;return l.memoizedState=l.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},l.queue=e,e=e.dispatch=bp.bind(null,ve,e),[l.memoizedState,e]},useRef:function(e){var t=zt();return e={current:e},t.memoizedState=e},useState:function(e){e=Zc(e);var t=e.queue,a=Af.bind(null,ve,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:$c,useDeferredValue:function(e,t){var a=zt();return Jc(a,e,t)},useTransition:function(){var e=Zc(!1);return e=jf.bind(null,ve,e.queue,!0,!1),zt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=ve,n=zt();if(_e){if(a===void 0)throw Error(u(407));a=a()}else{if(a=t(),qe===null)throw Error(u(349));(Me&124)!==0||Pd(l,t,a)}n.memoizedState=a;var r={value:a,getSnapshot:t};return n.queue=r,mf(Fd.bind(null,l,r,e),[e]),l.flags|=2048,ys(9,Hi(),Wd.bind(null,l,r,a,t),null),a},useId:function(){var e=zt(),t=qe.identifierPrefix;if(_e){var a=za,l=_a;a=(l&~(1<<32-pt(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=Ui++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=hp++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Wc,useFormState:cf,useActionState:cf,useOptimistic:function(e){var t=zt();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=Fc.bind(null,ve,!0,a),a.dispatch=t,[e,t]},useMemoCache:Vc,useCacheRefresh:function(){return zt().memoizedState=vp.bind(null,ve)}},kf={readContext:St,use:Bi,useCallback:vf,useContext:St,useEffect:hf,useImperativeHandle:yf,useInsertionEffect:xf,useLayoutEffect:pf,useMemo:bf,useReducer:Li,useRef:ff,useState:function(){return Li(Ua)},useDebugValue:$c,useDeferredValue:function(e,t){var a=tt();return Nf(a,Oe.memoizedState,e,t)},useTransition:function(){var e=Li(Ua)[0],t=tt().memoizedState;return[typeof e=="boolean"?e:gn(e),t]},useSyncExternalStore:Jd,useId:Ef,useHostTransitionStatus:Wc,useFormState:of,useActionState:of,useOptimistic:function(e,t){var a=tt();return tf(a,Oe,e,t)},useMemoCache:Vc,useCacheRefresh:Tf},Np={readContext:St,use:Bi,useCallback:vf,useContext:St,useEffect:hf,useImperativeHandle:yf,useInsertionEffect:xf,useLayoutEffect:pf,useMemo:bf,useReducer:Qc,useRef:ff,useState:function(){return Qc(Ua)},useDebugValue:$c,useDeferredValue:function(e,t){var a=tt();return Oe===null?Jc(a,e,t):Nf(a,Oe.memoizedState,e,t)},useTransition:function(){var e=Qc(Ua)[0],t=tt().memoizedState;return[typeof e=="boolean"?e:gn(e),t]},useSyncExternalStore:Jd,useId:Ef,useHostTransitionStatus:Wc,useFormState:df,useActionState:df,useOptimistic:function(e,t){var a=tt();return Oe!==null?tf(a,Oe,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Vc,useCacheRefresh:Tf},vs=null,bn=0;function Vi(e){var t=bn;return bn+=1,vs===null&&(vs=[]),Gd(vs,e,t)}function Nn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Xi(e,t){throw t.$$typeof===x?Error(u(525)):(e=Object.prototype.toString.call(t),Error(u(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function _f(e){var t=e._init;return t(e._payload)}function zf(e){function t(A,T){if(e){var C=A.deletions;C===null?(A.deletions=[T],A.flags|=16):C.push(T)}}function a(A,T){if(!e)return null;for(;T!==null;)t(A,T),T=T.sibling;return null}function l(A){for(var T=new Map;A!==null;)A.key!==null?T.set(A.key,A):T.set(A.index,A),A=A.sibling;return T}function n(A,T){return A=ka(A,T),A.index=0,A.sibling=null,A}function r(A,T,C){return A.index=C,e?(C=A.alternate,C!==null?(C=C.index,C<T?(A.flags|=67108866,T):C):(A.flags|=67108866,T)):(A.flags|=1048576,T)}function f(A){return e&&A.alternate===null&&(A.flags|=67108866),A}function p(A,T,C,q){return T===null||T.tag!==6?(T=vc(C,A.mode,q),T.return=A,T):(T=n(T,C),T.return=A,T)}function w(A,T,C,q){var ee=C.type;return ee===E?H(A,T,C.props.children,q,C.key):T!==null&&(T.elementType===ee||typeof ee=="object"&&ee!==null&&ee.$$typeof===K&&_f(ee)===T.type)?(T=n(T,C.props),Nn(T,C),T.return=A,T):(T=Ti(C.type,C.key,C.props,null,A.mode,q),Nn(T,C),T.return=A,T)}function M(A,T,C,q){return T===null||T.tag!==4||T.stateNode.containerInfo!==C.containerInfo||T.stateNode.implementation!==C.implementation?(T=bc(C,A.mode,q),T.return=A,T):(T=n(T,C.children||[]),T.return=A,T)}function H(A,T,C,q,ee){return T===null||T.tag!==7?(T=kl(C,A.mode,q,ee),T.return=A,T):(T=n(T,C),T.return=A,T)}function Y(A,T,C){if(typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint")return T=vc(""+T,A.mode,C),T.return=A,T;if(typeof T=="object"&&T!==null){switch(T.$$typeof){case j:return C=Ti(T.type,T.key,T.props,null,A.mode,C),Nn(C,T),C.return=A,C;case _:return T=bc(T,A.mode,C),T.return=A,T;case K:var q=T._init;return T=q(T._payload),Y(A,T,C)}if(Re(T)||Ae(T))return T=kl(T,A.mode,C,null),T.return=A,T;if(typeof T.then=="function")return Y(A,Vi(T),C);if(T.$$typeof===O)return Y(A,Mi(A,T),C);Xi(A,T)}return null}function k(A,T,C,q){var ee=T!==null?T.key:null;if(typeof C=="string"&&C!==""||typeof C=="number"||typeof C=="bigint")return ee!==null?null:p(A,T,""+C,q);if(typeof C=="object"&&C!==null){switch(C.$$typeof){case j:return C.key===ee?w(A,T,C,q):null;case _:return C.key===ee?M(A,T,C,q):null;case K:return ee=C._init,C=ee(C._payload),k(A,T,C,q)}if(Re(C)||Ae(C))return ee!==null?null:H(A,T,C,q,null);if(typeof C.then=="function")return k(A,T,Vi(C),q);if(C.$$typeof===O)return k(A,T,Mi(A,C),q);Xi(A,C)}return null}function z(A,T,C,q,ee){if(typeof q=="string"&&q!==""||typeof q=="number"||typeof q=="bigint")return A=A.get(C)||null,p(T,A,""+q,ee);if(typeof q=="object"&&q!==null){switch(q.$$typeof){case j:return A=A.get(q.key===null?C:q.key)||null,w(T,A,q,ee);case _:return A=A.get(q.key===null?C:q.key)||null,M(T,A,q,ee);case K:var je=q._init;return q=je(q._payload),z(A,T,C,q,ee)}if(Re(q)||Ae(q))return A=A.get(C)||null,H(T,A,q,ee,null);if(typeof q.then=="function")return z(A,T,C,Vi(q),ee);if(q.$$typeof===O)return z(A,T,C,Mi(T,q),ee);Xi(T,q)}return null}function re(A,T,C,q){for(var ee=null,je=null,le=T,ne=T=0,ht=null;le!==null&&ne<C.length;ne++){le.index>ne?(ht=le,le=null):ht=le.sibling;var ke=k(A,le,C[ne],q);if(ke===null){le===null&&(le=ht);break}e&&le&&ke.alternate===null&&t(A,le),T=r(ke,T,ne),je===null?ee=ke:je.sibling=ke,je=ke,le=ht}if(ne===C.length)return a(A,le),_e&&zl(A,ne),ee;if(le===null){for(;ne<C.length;ne++)le=Y(A,C[ne],q),le!==null&&(T=r(le,T,ne),je===null?ee=le:je.sibling=le,je=le);return _e&&zl(A,ne),ee}for(le=l(le);ne<C.length;ne++)ht=z(le,A,ne,C[ne],q),ht!==null&&(e&&ht.alternate!==null&&le.delete(ht.key===null?ne:ht.key),T=r(ht,T,ne),je===null?ee=ht:je.sibling=ht,je=ht);return e&&le.forEach(function(vl){return t(A,vl)}),_e&&zl(A,ne),ee}function se(A,T,C,q){if(C==null)throw Error(u(151));for(var ee=null,je=null,le=T,ne=T=0,ht=null,ke=C.next();le!==null&&!ke.done;ne++,ke=C.next()){le.index>ne?(ht=le,le=null):ht=le.sibling;var vl=k(A,le,ke.value,q);if(vl===null){le===null&&(le=ht);break}e&&le&&vl.alternate===null&&t(A,le),T=r(vl,T,ne),je===null?ee=vl:je.sibling=vl,je=vl,le=ht}if(ke.done)return a(A,le),_e&&zl(A,ne),ee;if(le===null){for(;!ke.done;ne++,ke=C.next())ke=Y(A,ke.value,q),ke!==null&&(T=r(ke,T,ne),je===null?ee=ke:je.sibling=ke,je=ke);return _e&&zl(A,ne),ee}for(le=l(le);!ke.done;ne++,ke=C.next())ke=z(le,A,ne,ke.value,q),ke!==null&&(e&&ke.alternate!==null&&le.delete(ke.key===null?ne:ke.key),T=r(ke,T,ne),je===null?ee=ke:je.sibling=ke,je=ke);return e&&le.forEach(function(jg){return t(A,jg)}),_e&&zl(A,ne),ee}function Be(A,T,C,q){if(typeof C=="object"&&C!==null&&C.type===E&&C.key===null&&(C=C.props.children),typeof C=="object"&&C!==null){switch(C.$$typeof){case j:e:{for(var ee=C.key;T!==null;){if(T.key===ee){if(ee=C.type,ee===E){if(T.tag===7){a(A,T.sibling),q=n(T,C.props.children),q.return=A,A=q;break e}}else if(T.elementType===ee||typeof ee=="object"&&ee!==null&&ee.$$typeof===K&&_f(ee)===T.type){a(A,T.sibling),q=n(T,C.props),Nn(q,C),q.return=A,A=q;break e}a(A,T);break}else t(A,T);T=T.sibling}C.type===E?(q=kl(C.props.children,A.mode,q,C.key),q.return=A,A=q):(q=Ti(C.type,C.key,C.props,null,A.mode,q),Nn(q,C),q.return=A,A=q)}return f(A);case _:e:{for(ee=C.key;T!==null;){if(T.key===ee)if(T.tag===4&&T.stateNode.containerInfo===C.containerInfo&&T.stateNode.implementation===C.implementation){a(A,T.sibling),q=n(T,C.children||[]),q.return=A,A=q;break e}else{a(A,T);break}else t(A,T);T=T.sibling}q=bc(C,A.mode,q),q.return=A,A=q}return f(A);case K:return ee=C._init,C=ee(C._payload),Be(A,T,C,q)}if(Re(C))return re(A,T,C,q);if(Ae(C)){if(ee=Ae(C),typeof ee!="function")throw Error(u(150));return C=ee.call(C),se(A,T,C,q)}if(typeof C.then=="function")return Be(A,T,Vi(C),q);if(C.$$typeof===O)return Be(A,T,Mi(A,C),q);Xi(A,C)}return typeof C=="string"&&C!==""||typeof C=="number"||typeof C=="bigint"?(C=""+C,T!==null&&T.tag===6?(a(A,T.sibling),q=n(T,C),q.return=A,A=q):(a(A,T),q=vc(C,A.mode,q),q.return=A,A=q),f(A)):a(A,T)}return function(A,T,C,q){try{bn=0;var ee=Be(A,T,C,q);return vs=null,ee}catch(le){if(le===dn||le===_i)throw le;var je=Yt(29,le,null,A.mode);return je.lanes=q,je.return=A,je}finally{}}}var bs=zf(!0),Df=zf(!1),aa=U(null),ba=null;function sl(e){var t=e.alternate;G(it,it.current&1),G(aa,e),ba===null&&(t===null||xs.current!==null||t.memoizedState!==null)&&(ba=e)}function Of(e){if(e.tag===22){if(G(it,it.current),G(aa,e),ba===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(ba=e)}}else nl()}function nl(){G(it,it.current),G(aa,aa.current)}function Ba(e){X(aa),ba===e&&(ba=null),X(it)}var it=U(0);function Qi(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Vo(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Ic(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:b({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var eo={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=Zt(),n=tl(l);n.payload=t,a!=null&&(n.callback=a),t=al(e,n,l),t!==null&&(Kt(t,e,l),mn(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=Zt(),n=tl(l);n.tag=1,n.payload=t,a!=null&&(n.callback=a),t=al(e,n,l),t!==null&&(Kt(t,e,l),mn(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=Zt(),l=tl(a);l.tag=2,t!=null&&(l.callback=t),t=al(e,l,a),t!==null&&(Kt(t,e,a),mn(t,e,a))}};function Uf(e,t,a,l,n,r,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,r,f):t.prototype&&t.prototype.isPureReactComponent?!an(a,l)||!an(n,r):!0}function Bf(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&eo.enqueueReplaceState(t,t.state,null)}function ql(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=b({},a));for(var n in e)a[n]===void 0&&(a[n]=e[n])}return a}var Zi=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Lf(e){Zi(e)}function Hf(e){console.error(e)}function qf(e){Zi(e)}function Ki(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Gf(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function to(e,t,a){return a=tl(a),a.tag=3,a.payload={element:null},a.callback=function(){Ki(e,t)},a}function Yf(e){return e=tl(e),e.tag=3,e}function Vf(e,t,a,l){var n=a.type.getDerivedStateFromError;if(typeof n=="function"){var r=l.value;e.payload=function(){return n(r)},e.callback=function(){Gf(t,a,l)}}var f=a.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(e.callback=function(){Gf(t,a,l),typeof n!="function"&&(dl===null?dl=new Set([this]):dl.add(this));var p=l.stack;this.componentDidCatch(l.value,{componentStack:p!==null?p:""})})}function jp(e,t,a,l,n){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&cn(t,a,n,!0),a=aa.current,a!==null){switch(a.tag){case 13:return ba===null?To():a.alternate===null&&Ke===0&&(Ke=3),a.flags&=-257,a.flags|=65536,a.lanes=n,l===Mc?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),Ro(e,l,n)),!1;case 22:return a.flags|=65536,l===Mc?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),Ro(e,l,n)),!1}throw Error(u(435,a.tag))}return Ro(e,l,n),To(),!1}if(_e)return t=aa.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,l!==wc&&(e=Error(u(422),{cause:l}),rn(Ft(e,a)))):(l!==wc&&(t=Error(u(423),{cause:l}),rn(Ft(t,a))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,l=Ft(l,a),n=to(e.stateNode,l,n),zc(e,n),Ke!==4&&(Ke=2)),!1;var r=Error(u(520),{cause:l});if(r=Ft(r,a),Rn===null?Rn=[r]:Rn.push(r),Ke!==4&&(Ke=2),t===null)return!0;l=Ft(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=n&-n,a.lanes|=e,e=to(a.stateNode,l,e),zc(a,e),!1;case 1:if(t=a.type,r=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(dl===null||!dl.has(r))))return a.flags|=65536,n&=-n,a.lanes|=n,n=Yf(n),Vf(n,e,a,l),zc(a,n),!1}a=a.return}while(a!==null);return!1}var Xf=Error(u(461)),ft=!1;function yt(e,t,a,l){t.child=e===null?Df(t,null,a,l):bs(t,e.child,a,l)}function Qf(e,t,a,l,n){a=a.render;var r=t.ref;if("ref"in l){var f={};for(var p in l)p!=="ref"&&(f[p]=l[p])}else f=l;return Bl(t),l=Lc(e,t,a,f,r,n),p=Hc(),e!==null&&!ft?(qc(e,t,n),La(e,t,n)):(_e&&p&&Nc(t),t.flags|=1,yt(e,t,l,n),t.child)}function Zf(e,t,a,l,n){if(e===null){var r=a.type;return typeof r=="function"&&!yc(r)&&r.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=r,Kf(e,t,r,l,n)):(e=Ti(a.type,null,l,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(r=e.child,!oo(e,n)){var f=r.memoizedProps;if(a=a.compare,a=a!==null?a:an,a(f,l)&&e.ref===t.ref)return La(e,t,n)}return t.flags|=1,e=ka(r,l),e.ref=t.ref,e.return=t,t.child=e}function Kf(e,t,a,l,n){if(e!==null){var r=e.memoizedProps;if(an(r,l)&&e.ref===t.ref)if(ft=!1,t.pendingProps=l=r,oo(e,n))(e.flags&131072)!==0&&(ft=!0);else return t.lanes=e.lanes,La(e,t,n)}return ao(e,t,a,l,n)}function $f(e,t,a){var l=t.pendingProps,n=l.children,r=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=r!==null?r.baseLanes|a:a,e!==null){for(n=t.child=e.child,r=0;n!==null;)r=r|n.lanes|n.childLanes,n=n.sibling;t.childLanes=r&~l}else t.childLanes=0,t.child=null;return Jf(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&ki(t,r!==null?r.cachePool:null),r!==null?Zd(t,r):Oc(),Of(t);else return t.lanes=t.childLanes=536870912,Jf(e,t,r!==null?r.baseLanes|a:a,a)}else r!==null?(ki(t,r.cachePool),Zd(t,r),nl(),t.memoizedState=null):(e!==null&&ki(t,null),Oc(),nl());return yt(e,t,n,a),t.child}function Jf(e,t,a,l){var n=Cc();return n=n===null?null:{parent:nt._currentValue,pool:n},t.memoizedState={baseLanes:a,cachePool:n},e!==null&&ki(t,null),Oc(),Of(t),e!==null&&cn(e,t,l,!0),null}function $i(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(u(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function ao(e,t,a,l,n){return Bl(t),a=Lc(e,t,a,l,void 0,n),l=Hc(),e!==null&&!ft?(qc(e,t,n),La(e,t,n)):(_e&&l&&Nc(t),t.flags|=1,yt(e,t,a,n),t.child)}function Pf(e,t,a,l,n,r){return Bl(t),t.updateQueue=null,a=$d(t,l,a,n),Kd(e),l=Hc(),e!==null&&!ft?(qc(e,t,r),La(e,t,r)):(_e&&l&&Nc(t),t.flags|=1,yt(e,t,a,r),t.child)}function Wf(e,t,a,l,n){if(Bl(t),t.stateNode===null){var r=us,f=a.contextType;typeof f=="object"&&f!==null&&(r=St(f)),r=new a(l,r),t.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=eo,t.stateNode=r,r._reactInternals=t,r=t.stateNode,r.props=l,r.state=t.memoizedState,r.refs={},kc(t),f=a.contextType,r.context=typeof f=="object"&&f!==null?St(f):us,r.state=t.memoizedState,f=a.getDerivedStateFromProps,typeof f=="function"&&(Ic(t,a,f,l),r.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(f=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),f!==r.state&&eo.enqueueReplaceState(r,r.state,null),xn(t,l,r,n),hn(),r.state=t.memoizedState),typeof r.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){r=t.stateNode;var p=t.memoizedProps,w=ql(a,p);r.props=w;var M=r.context,H=a.contextType;f=us,typeof H=="object"&&H!==null&&(f=St(H));var Y=a.getDerivedStateFromProps;H=typeof Y=="function"||typeof r.getSnapshotBeforeUpdate=="function",p=t.pendingProps!==p,H||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(p||M!==f)&&Bf(t,r,l,f),el=!1;var k=t.memoizedState;r.state=k,xn(t,l,r,n),hn(),M=t.memoizedState,p||k!==M||el?(typeof Y=="function"&&(Ic(t,a,Y,l),M=t.memoizedState),(w=el||Uf(t,a,w,l,k,M,f))?(H||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(t.flags|=4194308)):(typeof r.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=M),r.props=l,r.state=M,r.context=f,l=w):(typeof r.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{r=t.stateNode,_c(e,t),f=t.memoizedProps,H=ql(a,f),r.props=H,Y=t.pendingProps,k=r.context,M=a.contextType,w=us,typeof M=="object"&&M!==null&&(w=St(M)),p=a.getDerivedStateFromProps,(M=typeof p=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(f!==Y||k!==w)&&Bf(t,r,l,w),el=!1,k=t.memoizedState,r.state=k,xn(t,l,r,n),hn();var z=t.memoizedState;f!==Y||k!==z||el||e!==null&&e.dependencies!==null&&Ci(e.dependencies)?(typeof p=="function"&&(Ic(t,a,p,l),z=t.memoizedState),(H=el||Uf(t,a,H,l,k,z,w)||e!==null&&e.dependencies!==null&&Ci(e.dependencies))?(M||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(l,z,w),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(l,z,w)),typeof r.componentDidUpdate=="function"&&(t.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof r.componentDidUpdate!="function"||f===e.memoizedProps&&k===e.memoizedState||(t.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&k===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=z),r.props=l,r.state=z,r.context=w,l=H):(typeof r.componentDidUpdate!="function"||f===e.memoizedProps&&k===e.memoizedState||(t.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&k===e.memoizedState||(t.flags|=1024),l=!1)}return r=l,$i(e,t),l=(t.flags&128)!==0,r||l?(r=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:r.render(),t.flags|=1,e!==null&&l?(t.child=bs(t,e.child,null,n),t.child=bs(t,null,a,n)):yt(e,t,a,n),t.memoizedState=r.state,e=t.child):e=La(e,t,n),e}function Ff(e,t,a,l){return nn(),t.flags|=256,yt(e,t,a,l),t.child}var lo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function so(e){return{baseLanes:e,cachePool:Ld()}}function no(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=la),e}function If(e,t,a){var l=t.pendingProps,n=!1,r=(t.flags&128)!==0,f;if((f=r)||(f=e!==null&&e.memoizedState===null?!1:(it.current&2)!==0),f&&(n=!0,t.flags&=-129),f=(t.flags&32)!==0,t.flags&=-33,e===null){if(_e){if(n?sl(t):nl(),_e){var p=Ze,w;if(w=p){e:{for(w=p,p=va;w.nodeType!==8;){if(!p){p=null;break e}if(w=da(w.nextSibling),w===null){p=null;break e}}p=w}p!==null?(t.memoizedState={dehydrated:p,treeContext:_l!==null?{id:_a,overflow:za}:null,retryLane:536870912,hydrationErrors:null},w=Yt(18,null,null,0),w.stateNode=p,w.return=t,t.child=w,Mt=t,Ze=null,w=!0):w=!1}w||Ol(t)}if(p=t.memoizedState,p!==null&&(p=p.dehydrated,p!==null))return Vo(p)?t.lanes=32:t.lanes=536870912,null;Ba(t)}return p=l.children,l=l.fallback,n?(nl(),n=t.mode,p=Ji({mode:"hidden",children:p},n),l=kl(l,n,a,null),p.return=t,l.return=t,p.sibling=l,t.child=p,n=t.child,n.memoizedState=so(a),n.childLanes=no(e,f,a),t.memoizedState=lo,l):(sl(t),io(t,p))}if(w=e.memoizedState,w!==null&&(p=w.dehydrated,p!==null)){if(r)t.flags&256?(sl(t),t.flags&=-257,t=ro(e,t,a)):t.memoizedState!==null?(nl(),t.child=e.child,t.flags|=128,t=null):(nl(),n=l.fallback,p=t.mode,l=Ji({mode:"visible",children:l.children},p),n=kl(n,p,a,null),n.flags|=2,l.return=t,n.return=t,l.sibling=n,t.child=l,bs(t,e.child,null,a),l=t.child,l.memoizedState=so(a),l.childLanes=no(e,f,a),t.memoizedState=lo,t=n);else if(sl(t),Vo(p)){if(f=p.nextSibling&&p.nextSibling.dataset,f)var M=f.dgst;f=M,l=Error(u(419)),l.stack="",l.digest=f,rn({value:l,source:null,stack:null}),t=ro(e,t,a)}else if(ft||cn(e,t,a,!1),f=(a&e.childLanes)!==0,ft||f){if(f=qe,f!==null&&(l=a&-a,l=(l&42)!==0?1:Ie(l),l=(l&(f.suspendedLanes|a))!==0?0:l,l!==0&&l!==w.retryLane))throw w.retryLane=l,os(e,l),Kt(f,e,l),Xf;p.data==="$?"||To(),t=ro(e,t,a)}else p.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=w.treeContext,Ze=da(p.nextSibling),Mt=t,_e=!0,Dl=null,va=!1,e!==null&&(ea[ta++]=_a,ea[ta++]=za,ea[ta++]=_l,_a=e.id,za=e.overflow,_l=t),t=io(t,l.children),t.flags|=4096);return t}return n?(nl(),n=l.fallback,p=t.mode,w=e.child,M=w.sibling,l=ka(w,{mode:"hidden",children:l.children}),l.subtreeFlags=w.subtreeFlags&65011712,M!==null?n=ka(M,n):(n=kl(n,p,a,null),n.flags|=2),n.return=t,l.return=t,l.sibling=n,t.child=l,l=n,n=t.child,p=e.child.memoizedState,p===null?p=so(a):(w=p.cachePool,w!==null?(M=nt._currentValue,w=w.parent!==M?{parent:M,pool:M}:w):w=Ld(),p={baseLanes:p.baseLanes|a,cachePool:w}),n.memoizedState=p,n.childLanes=no(e,f,a),t.memoizedState=lo,l):(sl(t),a=e.child,e=a.sibling,a=ka(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(f=t.deletions,f===null?(t.deletions=[e],t.flags|=16):f.push(e)),t.child=a,t.memoizedState=null,a)}function io(e,t){return t=Ji({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Ji(e,t){return e=Yt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function ro(e,t,a){return bs(t,e.child,null,a),e=io(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function em(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Ec(e.return,t,a)}function co(e,t,a,l,n){var r=e.memoizedState;r===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:n}:(r.isBackwards=t,r.rendering=null,r.renderingStartTime=0,r.last=l,r.tail=a,r.tailMode=n)}function tm(e,t,a){var l=t.pendingProps,n=l.revealOrder,r=l.tail;if(yt(e,t,l.children,a),l=it.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&em(e,a,t);else if(e.tag===19)em(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(G(it,l),n){case"forwards":for(a=t.child,n=null;a!==null;)e=a.alternate,e!==null&&Qi(e)===null&&(n=a),a=a.sibling;a=n,a===null?(n=t.child,t.child=null):(n=a.sibling,a.sibling=null),co(t,!1,n,a,r);break;case"backwards":for(a=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&Qi(e)===null){t.child=n;break}e=n.sibling,n.sibling=a,a=n,n=e}co(t,!0,a,null,r);break;case"together":co(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function La(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),ul|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(cn(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(u(153));if(t.child!==null){for(e=t.child,a=ka(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=ka(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function oo(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Ci(e)))}function wp(e,t,a){switch(t.tag){case 3:Ce(t,t.stateNode.containerInfo),Ia(t,nt,e.memoizedState.cache),nn();break;case 27:case 5:kt(t);break;case 4:Ce(t,t.stateNode.containerInfo);break;case 10:Ia(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(sl(t),t.flags|=128,null):(a&t.child.childLanes)!==0?If(e,t,a):(sl(t),e=La(e,t,a),e!==null?e.sibling:null);sl(t);break;case 19:var n=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(cn(e,t,a,!1),l=(a&t.childLanes)!==0),n){if(l)return tm(e,t,a);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),G(it,it.current),l)break;return null;case 22:case 23:return t.lanes=0,$f(e,t,a);case 24:Ia(t,nt,e.memoizedState.cache)}return La(e,t,a)}function am(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)ft=!0;else{if(!oo(e,a)&&(t.flags&128)===0)return ft=!1,wp(e,t,a);ft=(e.flags&131072)!==0}else ft=!1,_e&&(t.flags&1048576)!==0&&kd(t,Ri,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,n=l._init;if(l=n(l._payload),t.type=l,typeof l=="function")yc(l)?(e=ql(l,e),t.tag=1,t=Wf(null,t,l,e,a)):(t.tag=0,t=ao(null,t,l,e,a));else{if(l!=null){if(n=l.$$typeof,n===P){t.tag=11,t=Qf(null,t,l,e,a);break e}else if(n===pe){t.tag=14,t=Zf(null,t,l,e,a);break e}}throw t=jt(l)||l,Error(u(306,t,""))}}return t;case 0:return ao(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,n=ql(l,t.pendingProps),Wf(e,t,l,n,a);case 3:e:{if(Ce(t,t.stateNode.containerInfo),e===null)throw Error(u(387));l=t.pendingProps;var r=t.memoizedState;n=r.element,_c(e,t),xn(t,l,null,a);var f=t.memoizedState;if(l=f.cache,Ia(t,nt,l),l!==r.cache&&Tc(t,[nt],a,!0),hn(),l=f.element,r.isDehydrated)if(r={element:l,isDehydrated:!1,cache:f.cache},t.updateQueue.baseState=r,t.memoizedState=r,t.flags&256){t=Ff(e,t,l,a);break e}else if(l!==n){n=Ft(Error(u(424)),t),rn(n),t=Ff(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ze=da(e.firstChild),Mt=t,_e=!0,Dl=null,va=!0,a=Df(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(nn(),l===n){t=La(e,t,a);break e}yt(e,t,l,a)}t=t.child}return t;case 26:return $i(e,t),e===null?(a=ih(t.type,null,t.pendingProps,null))?t.memoizedState=a:_e||(a=t.type,e=t.pendingProps,l=or(F.current).createElement(a),l[Pe]=t,l[He]=e,bt(l,a,e),dt(l),t.stateNode=l):t.memoizedState=ih(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return kt(t),e===null&&_e&&(l=t.stateNode=lh(t.type,t.pendingProps,F.current),Mt=t,va=!0,n=Ze,hl(t.type)?(Xo=n,Ze=da(l.firstChild)):Ze=n),yt(e,t,t.pendingProps.children,a),$i(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&_e&&((n=l=Ze)&&(l=Wp(l,t.type,t.pendingProps,va),l!==null?(t.stateNode=l,Mt=t,Ze=da(l.firstChild),va=!1,n=!0):n=!1),n||Ol(t)),kt(t),n=t.type,r=t.pendingProps,f=e!==null?e.memoizedProps:null,l=r.children,qo(n,r)?l=null:f!==null&&qo(n,f)&&(t.flags|=32),t.memoizedState!==null&&(n=Lc(e,t,xp,null,null,a),Bn._currentValue=n),$i(e,t),yt(e,t,l,a),t.child;case 6:return e===null&&_e&&((e=a=Ze)&&(a=Fp(a,t.pendingProps,va),a!==null?(t.stateNode=a,Mt=t,Ze=null,e=!0):e=!1),e||Ol(t)),null;case 13:return If(e,t,a);case 4:return Ce(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=bs(t,null,l,a):yt(e,t,l,a),t.child;case 11:return Qf(e,t,t.type,t.pendingProps,a);case 7:return yt(e,t,t.pendingProps,a),t.child;case 8:return yt(e,t,t.pendingProps.children,a),t.child;case 12:return yt(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,Ia(t,t.type,l.value),yt(e,t,l.children,a),t.child;case 9:return n=t.type._context,l=t.pendingProps.children,Bl(t),n=St(n),l=l(n),t.flags|=1,yt(e,t,l,a),t.child;case 14:return Zf(e,t,t.type,t.pendingProps,a);case 15:return Kf(e,t,t.type,t.pendingProps,a);case 19:return tm(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=Ji(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=ka(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return $f(e,t,a);case 24:return Bl(t),l=St(nt),e===null?(n=Cc(),n===null&&(n=qe,r=Ac(),n.pooledCache=r,r.refCount++,r!==null&&(n.pooledCacheLanes|=a),n=r),t.memoizedState={parent:l,cache:n},kc(t),Ia(t,nt,n)):((e.lanes&a)!==0&&(_c(e,t),xn(t,null,null,a),hn()),n=e.memoizedState,r=t.memoizedState,n.parent!==l?(n={parent:l,cache:l},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),Ia(t,nt,l)):(l=r.cache,Ia(t,nt,l),l!==n.cache&&Tc(t,[nt],a,!0))),yt(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(u(156,t.tag))}function Ha(e){e.flags|=4}function lm(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!dh(t)){if(t=aa.current,t!==null&&((Me&4194048)===Me?ba!==null:(Me&62914560)!==Me&&(Me&536870912)===0||t!==ba))throw fn=Mc,Hd;e.flags|=8192}}function Pi(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?ui():536870912,e.lanes|=t,Ss|=t)}function jn(e,t){if(!_e)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Qe(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags&65011712,l|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags,l|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function Sp(e,t,a){var l=t.pendingProps;switch(jc(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Qe(t),null;case 1:return Qe(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Oa(nt),lt(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(sn(t)?Ha(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Dd())),Qe(t),null;case 26:return a=t.memoizedState,e===null?(Ha(t),a!==null?(Qe(t),lm(t,a)):(Qe(t),t.flags&=-16777217)):a?a!==e.memoizedState?(Ha(t),Qe(t),lm(t,a)):(Qe(t),t.flags&=-16777217):(e.memoizedProps!==l&&Ha(t),Qe(t),t.flags&=-16777217),null;case 27:pa(t),a=F.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Ha(t);else{if(!l){if(t.stateNode===null)throw Error(u(166));return Qe(t),null}e=W.current,sn(t)?_d(t):(e=lh(n,l,a),t.stateNode=e,Ha(t))}return Qe(t),null;case 5:if(pa(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Ha(t);else{if(!l){if(t.stateNode===null)throw Error(u(166));return Qe(t),null}if(e=W.current,sn(t))_d(t);else{switch(n=or(F.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?n.createElement("select",{is:l.is}):n.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?n.createElement(a,{is:l.is}):n.createElement(a)}}e[Pe]=t,e[He]=l;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(bt(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Ha(t)}}return Qe(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&Ha(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(u(166));if(e=F.current,sn(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,n=Mt,n!==null)switch(n.tag){case 27:case 5:l=n.memoizedProps}e[Pe]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||Pm(e.nodeValue,a)),e||Ol(t)}else e=or(e).createTextNode(l),e[Pe]=t,t.stateNode=e}return Qe(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=sn(t),l!==null&&l.dehydrated!==null){if(e===null){if(!n)throw Error(u(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(u(317));n[Pe]=t}else nn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Qe(t),n=!1}else n=Dd(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(Ba(t),t):(Ba(t),null)}if(Ba(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,n=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(n=l.alternate.memoizedState.cachePool.pool);var r=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(r=l.memoizedState.cachePool.pool),r!==n&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),Pi(t,t.updateQueue),Qe(t),null;case 4:return lt(),e===null&&Oo(t.stateNode.containerInfo),Qe(t),null;case 10:return Oa(t.type),Qe(t),null;case 19:if(X(it),n=t.memoizedState,n===null)return Qe(t),null;if(l=(t.flags&128)!==0,r=n.rendering,r===null)if(l)jn(n,!1);else{if(Ke!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(r=Qi(e),r!==null){for(t.flags|=128,jn(n,!1),e=r.updateQueue,t.updateQueue=e,Pi(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Md(a,e),a=a.sibling;return G(it,it.current&1|2),t.child}e=e.sibling}n.tail!==null&&wt()>Ii&&(t.flags|=128,l=!0,jn(n,!1),t.lanes=4194304)}else{if(!l)if(e=Qi(r),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,Pi(t,e),jn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!r.alternate&&!_e)return Qe(t),null}else 2*wt()-n.renderingStartTime>Ii&&a!==536870912&&(t.flags|=128,l=!0,jn(n,!1),t.lanes=4194304);n.isBackwards?(r.sibling=t.child,t.child=r):(e=n.last,e!==null?e.sibling=r:t.child=r,n.last=r)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=wt(),t.sibling=null,e=it.current,G(it,l?e&1|2:e&1),t):(Qe(t),null);case 22:case 23:return Ba(t),Uc(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(Qe(t),t.subtreeFlags&6&&(t.flags|=8192)):Qe(t),a=t.updateQueue,a!==null&&Pi(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&X(Ll),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Oa(nt),Qe(t),null;case 25:return null;case 30:return null}throw Error(u(156,t.tag))}function Ep(e,t){switch(jc(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Oa(nt),lt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return pa(t),null;case 13:if(Ba(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(u(340));nn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return X(it),null;case 4:return lt(),null;case 10:return Oa(t.type),null;case 22:case 23:return Ba(t),Uc(),e!==null&&X(Ll),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Oa(nt),null;case 25:return null;default:return null}}function sm(e,t){switch(jc(t),t.tag){case 3:Oa(nt),lt();break;case 26:case 27:case 5:pa(t);break;case 4:lt();break;case 13:Ba(t);break;case 19:X(it);break;case 10:Oa(t.type);break;case 22:case 23:Ba(t),Uc(),e!==null&&X(Ll);break;case 24:Oa(nt)}}function wn(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var n=l.next;a=n;do{if((a.tag&e)===e){l=void 0;var r=a.create,f=a.inst;l=r(),f.destroy=l}a=a.next}while(a!==n)}}catch(p){Le(t,t.return,p)}}function il(e,t,a){try{var l=t.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var r=n.next;l=r;do{if((l.tag&e)===e){var f=l.inst,p=f.destroy;if(p!==void 0){f.destroy=void 0,n=t;var w=a,M=p;try{M()}catch(H){Le(n,w,H)}}}l=l.next}while(l!==r)}}catch(H){Le(t,t.return,H)}}function nm(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{Qd(t,a)}catch(l){Le(e,e.return,l)}}}function im(e,t,a){a.props=ql(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){Le(e,t,l)}}function Sn(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(n){Le(e,t,n)}}function Na(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(n){Le(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(n){Le(e,t,n)}else a.current=null}function rm(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(n){Le(e,e.return,n)}}function uo(e,t,a){try{var l=e.stateNode;Zp(l,e.type,a,t),l[He]=t}catch(n){Le(e,e.return,n)}}function cm(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&hl(e.type)||e.tag===4}function fo(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||cm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&hl(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function mo(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=cr));else if(l!==4&&(l===27&&hl(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(mo(e,t,a),e=e.sibling;e!==null;)mo(e,t,a),e=e.sibling}function Wi(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&hl(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(Wi(e,t,a),e=e.sibling;e!==null;)Wi(e,t,a),e=e.sibling}function om(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);bt(t,l,a),t[Pe]=e,t[He]=a}catch(r){Le(e,e.return,r)}}var qa=!1,Fe=!1,ho=!1,um=typeof WeakSet=="function"?WeakSet:Set,mt=null;function Tp(e,t){if(e=e.containerInfo,Lo=xr,e=bd(e),dc(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var n=l.anchorOffset,r=l.focusNode;l=l.focusOffset;try{a.nodeType,r.nodeType}catch{a=null;break e}var f=0,p=-1,w=-1,M=0,H=0,Y=e,k=null;t:for(;;){for(var z;Y!==a||n!==0&&Y.nodeType!==3||(p=f+n),Y!==r||l!==0&&Y.nodeType!==3||(w=f+l),Y.nodeType===3&&(f+=Y.nodeValue.length),(z=Y.firstChild)!==null;)k=Y,Y=z;for(;;){if(Y===e)break t;if(k===a&&++M===n&&(p=f),k===r&&++H===l&&(w=f),(z=Y.nextSibling)!==null)break;Y=k,k=Y.parentNode}Y=z}a=p===-1||w===-1?null:{start:p,end:w}}else a=null}a=a||{start:0,end:0}}else a=null;for(Ho={focusedElem:e,selectionRange:a},xr=!1,mt=t;mt!==null;)if(t=mt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,mt=e;else for(;mt!==null;){switch(t=mt,r=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&r!==null){e=void 0,a=t,n=r.memoizedProps,r=r.memoizedState,l=a.stateNode;try{var re=ql(a.type,n,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(re,r),l.__reactInternalSnapshotBeforeUpdate=e}catch(se){Le(a,a.return,se)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Yo(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Yo(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(u(163))}if(e=t.sibling,e!==null){e.return=t.return,mt=e;break}mt=t.return}}function dm(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:rl(e,a),l&4&&wn(5,a);break;case 1:if(rl(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(f){Le(a,a.return,f)}else{var n=ql(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(f){Le(a,a.return,f)}}l&64&&nm(a),l&512&&Sn(a,a.return);break;case 3:if(rl(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Qd(e,t)}catch(f){Le(a,a.return,f)}}break;case 27:t===null&&l&4&&om(a);case 26:case 5:rl(e,a),t===null&&l&4&&rm(a),l&512&&Sn(a,a.return);break;case 12:rl(e,a);break;case 13:rl(e,a),l&4&&hm(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=Op.bind(null,a),Ip(e,a))));break;case 22:if(l=a.memoizedState!==null||qa,!l){t=t!==null&&t.memoizedState!==null||Fe,n=qa;var r=Fe;qa=l,(Fe=t)&&!r?cl(e,a,(a.subtreeFlags&8772)!==0):rl(e,a),qa=n,Fe=r}break;case 30:break;default:rl(e,a)}}function fm(e){var t=e.alternate;t!==null&&(e.alternate=null,fm(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Zr(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ye=null,Dt=!1;function Ga(e,t,a){for(a=a.child;a!==null;)mm(e,t,a),a=a.sibling}function mm(e,t,a){if(Ct&&typeof Ct.onCommitFiberUnmount=="function")try{Ct.onCommitFiberUnmount(ga,a)}catch{}switch(a.tag){case 26:Fe||Na(a,t),Ga(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Fe||Na(a,t);var l=Ye,n=Dt;hl(a.type)&&(Ye=a.stateNode,Dt=!1),Ga(e,t,a),zn(a.stateNode),Ye=l,Dt=n;break;case 5:Fe||Na(a,t);case 6:if(l=Ye,n=Dt,Ye=null,Ga(e,t,a),Ye=l,Dt=n,Ye!==null)if(Dt)try{(Ye.nodeType===9?Ye.body:Ye.nodeName==="HTML"?Ye.ownerDocument.body:Ye).removeChild(a.stateNode)}catch(r){Le(a,t,r)}else try{Ye.removeChild(a.stateNode)}catch(r){Le(a,t,r)}break;case 18:Ye!==null&&(Dt?(e=Ye,th(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),Gn(e)):th(Ye,a.stateNode));break;case 4:l=Ye,n=Dt,Ye=a.stateNode.containerInfo,Dt=!0,Ga(e,t,a),Ye=l,Dt=n;break;case 0:case 11:case 14:case 15:Fe||il(2,a,t),Fe||il(4,a,t),Ga(e,t,a);break;case 1:Fe||(Na(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&im(a,t,l)),Ga(e,t,a);break;case 21:Ga(e,t,a);break;case 22:Fe=(l=Fe)||a.memoizedState!==null,Ga(e,t,a),Fe=l;break;default:Ga(e,t,a)}}function hm(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Gn(e)}catch(a){Le(t,t.return,a)}}function Ap(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new um),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new um),t;default:throw Error(u(435,e.tag))}}function xo(e,t){var a=Ap(e);t.forEach(function(l){var n=Up.bind(null,e,l);a.has(l)||(a.add(l),l.then(n,n))})}function Vt(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var n=a[l],r=e,f=t,p=f;e:for(;p!==null;){switch(p.tag){case 27:if(hl(p.type)){Ye=p.stateNode,Dt=!1;break e}break;case 5:Ye=p.stateNode,Dt=!1;break e;case 3:case 4:Ye=p.stateNode.containerInfo,Dt=!0;break e}p=p.return}if(Ye===null)throw Error(u(160));mm(r,f,n),Ye=null,Dt=!1,r=n.alternate,r!==null&&(r.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)xm(t,e),t=t.sibling}var ua=null;function xm(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Vt(t,e),Xt(e),l&4&&(il(3,e,e.return),wn(3,e),il(5,e,e.return));break;case 1:Vt(t,e),Xt(e),l&512&&(Fe||a===null||Na(a,a.return)),l&64&&qa&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var n=ua;if(Vt(t,e),Xt(e),l&512&&(Fe||a===null||Na(a,a.return)),l&4){var r=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,n=n.ownerDocument||n;t:switch(l){case"title":r=n.getElementsByTagName("title")[0],(!r||r[Ks]||r[Pe]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=n.createElement(l),n.head.insertBefore(r,n.querySelector("head > title"))),bt(r,l,a),r[Pe]=e,dt(r),l=r;break e;case"link":var f=oh("link","href",n).get(l+(a.href||""));if(f){for(var p=0;p<f.length;p++)if(r=f[p],r.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&r.getAttribute("rel")===(a.rel==null?null:a.rel)&&r.getAttribute("title")===(a.title==null?null:a.title)&&r.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){f.splice(p,1);break t}}r=n.createElement(l),bt(r,l,a),n.head.appendChild(r);break;case"meta":if(f=oh("meta","content",n).get(l+(a.content||""))){for(p=0;p<f.length;p++)if(r=f[p],r.getAttribute("content")===(a.content==null?null:""+a.content)&&r.getAttribute("name")===(a.name==null?null:a.name)&&r.getAttribute("property")===(a.property==null?null:a.property)&&r.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&r.getAttribute("charset")===(a.charSet==null?null:a.charSet)){f.splice(p,1);break t}}r=n.createElement(l),bt(r,l,a),n.head.appendChild(r);break;default:throw Error(u(468,l))}r[Pe]=e,dt(r),l=r}e.stateNode=l}else uh(n,e.type,e.stateNode);else e.stateNode=ch(n,l,e.memoizedProps);else r!==l?(r===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):r.count--,l===null?uh(n,e.type,e.stateNode):ch(n,l,e.memoizedProps)):l===null&&e.stateNode!==null&&uo(e,e.memoizedProps,a.memoizedProps)}break;case 27:Vt(t,e),Xt(e),l&512&&(Fe||a===null||Na(a,a.return)),a!==null&&l&4&&uo(e,e.memoizedProps,a.memoizedProps);break;case 5:if(Vt(t,e),Xt(e),l&512&&(Fe||a===null||Na(a,a.return)),e.flags&32){n=e.stateNode;try{as(n,"")}catch(z){Le(e,e.return,z)}}l&4&&e.stateNode!=null&&(n=e.memoizedProps,uo(e,n,a!==null?a.memoizedProps:n)),l&1024&&(ho=!0);break;case 6:if(Vt(t,e),Xt(e),l&4){if(e.stateNode===null)throw Error(u(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(z){Le(e,e.return,z)}}break;case 3:if(fr=null,n=ua,ua=ur(t.containerInfo),Vt(t,e),ua=n,Xt(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{Gn(t.containerInfo)}catch(z){Le(e,e.return,z)}ho&&(ho=!1,pm(e));break;case 4:l=ua,ua=ur(e.stateNode.containerInfo),Vt(t,e),Xt(e),ua=l;break;case 12:Vt(t,e),Xt(e);break;case 13:Vt(t,e),Xt(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(No=wt()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,xo(e,l)));break;case 22:n=e.memoizedState!==null;var w=a!==null&&a.memoizedState!==null,M=qa,H=Fe;if(qa=M||n,Fe=H||w,Vt(t,e),Fe=H,qa=M,Xt(e),l&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(a===null||w||qa||Fe||Gl(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){w=a=t;try{if(r=w.stateNode,n)f=r.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{p=w.stateNode;var Y=w.memoizedProps.style,k=Y!=null&&Y.hasOwnProperty("display")?Y.display:null;p.style.display=k==null||typeof k=="boolean"?"":(""+k).trim()}}catch(z){Le(w,w.return,z)}}}else if(t.tag===6){if(a===null){w=t;try{w.stateNode.nodeValue=n?"":w.memoizedProps}catch(z){Le(w,w.return,z)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,xo(e,a))));break;case 19:Vt(t,e),Xt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,xo(e,l)));break;case 30:break;case 21:break;default:Vt(t,e),Xt(e)}}function Xt(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(cm(l)){a=l;break}l=l.return}if(a==null)throw Error(u(160));switch(a.tag){case 27:var n=a.stateNode,r=fo(e);Wi(e,r,n);break;case 5:var f=a.stateNode;a.flags&32&&(as(f,""),a.flags&=-33);var p=fo(e);Wi(e,p,f);break;case 3:case 4:var w=a.stateNode.containerInfo,M=fo(e);mo(e,M,w);break;default:throw Error(u(161))}}catch(H){Le(e,e.return,H)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function pm(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;pm(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function rl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)dm(e,t.alternate,t),t=t.sibling}function Gl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:il(4,t,t.return),Gl(t);break;case 1:Na(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&im(t,t.return,a),Gl(t);break;case 27:zn(t.stateNode);case 26:case 5:Na(t,t.return),Gl(t);break;case 22:t.memoizedState===null&&Gl(t);break;case 30:Gl(t);break;default:Gl(t)}e=e.sibling}}function cl(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,n=e,r=t,f=r.flags;switch(r.tag){case 0:case 11:case 15:cl(n,r,a),wn(4,r);break;case 1:if(cl(n,r,a),l=r,n=l.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(M){Le(l,l.return,M)}if(l=r,n=l.updateQueue,n!==null){var p=l.stateNode;try{var w=n.shared.hiddenCallbacks;if(w!==null)for(n.shared.hiddenCallbacks=null,n=0;n<w.length;n++)Xd(w[n],p)}catch(M){Le(l,l.return,M)}}a&&f&64&&nm(r),Sn(r,r.return);break;case 27:om(r);case 26:case 5:cl(n,r,a),a&&l===null&&f&4&&rm(r),Sn(r,r.return);break;case 12:cl(n,r,a);break;case 13:cl(n,r,a),a&&f&4&&hm(n,r);break;case 22:r.memoizedState===null&&cl(n,r,a),Sn(r,r.return);break;case 30:break;default:cl(n,r,a)}t=t.sibling}}function po(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&on(a))}function go(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&on(e))}function ja(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)gm(e,t,a,l),t=t.sibling}function gm(e,t,a,l){var n=t.flags;switch(t.tag){case 0:case 11:case 15:ja(e,t,a,l),n&2048&&wn(9,t);break;case 1:ja(e,t,a,l);break;case 3:ja(e,t,a,l),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&on(e)));break;case 12:if(n&2048){ja(e,t,a,l),e=t.stateNode;try{var r=t.memoizedProps,f=r.id,p=r.onPostCommit;typeof p=="function"&&p(f,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(w){Le(t,t.return,w)}}else ja(e,t,a,l);break;case 13:ja(e,t,a,l);break;case 23:break;case 22:r=t.stateNode,f=t.alternate,t.memoizedState!==null?r._visibility&2?ja(e,t,a,l):En(e,t):r._visibility&2?ja(e,t,a,l):(r._visibility|=2,Ns(e,t,a,l,(t.subtreeFlags&10256)!==0)),n&2048&&po(f,t);break;case 24:ja(e,t,a,l),n&2048&&go(t.alternate,t);break;default:ja(e,t,a,l)}}function Ns(e,t,a,l,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var r=e,f=t,p=a,w=l,M=f.flags;switch(f.tag){case 0:case 11:case 15:Ns(r,f,p,w,n),wn(8,f);break;case 23:break;case 22:var H=f.stateNode;f.memoizedState!==null?H._visibility&2?Ns(r,f,p,w,n):En(r,f):(H._visibility|=2,Ns(r,f,p,w,n)),n&&M&2048&&po(f.alternate,f);break;case 24:Ns(r,f,p,w,n),n&&M&2048&&go(f.alternate,f);break;default:Ns(r,f,p,w,n)}t=t.sibling}}function En(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,n=l.flags;switch(l.tag){case 22:En(a,l),n&2048&&po(l.alternate,l);break;case 24:En(a,l),n&2048&&go(l.alternate,l);break;default:En(a,l)}t=t.sibling}}var Tn=8192;function js(e){if(e.subtreeFlags&Tn)for(e=e.child;e!==null;)ym(e),e=e.sibling}function ym(e){switch(e.tag){case 26:js(e),e.flags&Tn&&e.memoizedState!==null&&fg(ua,e.memoizedState,e.memoizedProps);break;case 5:js(e);break;case 3:case 4:var t=ua;ua=ur(e.stateNode.containerInfo),js(e),ua=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Tn,Tn=16777216,js(e),Tn=t):js(e));break;default:js(e)}}function vm(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function An(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];mt=l,Nm(l,e)}vm(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)bm(e),e=e.sibling}function bm(e){switch(e.tag){case 0:case 11:case 15:An(e),e.flags&2048&&il(9,e,e.return);break;case 3:An(e);break;case 12:An(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Fi(e)):An(e);break;default:An(e)}}function Fi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];mt=l,Nm(l,e)}vm(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:il(8,t,t.return),Fi(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,Fi(t));break;default:Fi(t)}e=e.sibling}}function Nm(e,t){for(;mt!==null;){var a=mt;switch(a.tag){case 0:case 11:case 15:il(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:on(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,mt=l;else e:for(a=e;mt!==null;){l=mt;var n=l.sibling,r=l.return;if(fm(l),l===a){mt=null;break e}if(n!==null){n.return=r,mt=n;break e}mt=r}}}var Rp={getCacheForType:function(e){var t=St(nt),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},Cp=typeof WeakMap=="function"?WeakMap:Map,ze=0,qe=null,we=null,Me=0,De=0,Qt=null,ol=!1,ws=!1,yo=!1,Ya=0,Ke=0,ul=0,Yl=0,vo=0,la=0,Ss=0,Rn=null,Ot=null,bo=!1,No=0,Ii=1/0,er=null,dl=null,vt=0,fl=null,Es=null,Ts=0,jo=0,wo=null,jm=null,Cn=0,So=null;function Zt(){if((ze&2)!==0&&Me!==0)return Me&-Me;if(R.T!==null){var e=ms;return e!==0?e:ko()}return Tl()}function wm(){la===0&&(la=(Me&536870912)===0||_e?ca():536870912);var e=aa.current;return e!==null&&(e.flags|=32),la}function Kt(e,t,a){(e===qe&&(De===2||De===9)||e.cancelPendingCommit!==null)&&(As(e,0),ml(e,Me,la,!1)),Wa(e,a),((ze&2)===0||e!==qe)&&(e===qe&&((ze&2)===0&&(Yl|=a),Ke===4&&ml(e,Me,la,!1)),wa(e))}function Sm(e,t,a){if((ze&6)!==0)throw Error(u(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||El(e,t),n=l?_p(e,t):Ao(e,t,!0),r=l;do{if(n===0){ws&&!l&&ml(e,t,0,!1);break}else{if(a=e.current.alternate,r&&!Mp(a)){n=Ao(e,t,!1),r=!1;continue}if(n===2){if(r=t,e.errorRecoveryDisabledLanes&r)var f=0;else f=e.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){t=f;e:{var p=e;n=Rn;var w=p.current.memoizedState.isDehydrated;if(w&&(As(p,f).flags|=256),f=Ao(p,f,!1),f!==2){if(yo&&!w){p.errorRecoveryDisabledLanes|=r,Yl|=r,n=4;break e}r=Ot,Ot=n,r!==null&&(Ot===null?Ot=r:Ot.push.apply(Ot,r))}n=f}if(r=!1,n!==2)continue}}if(n===1){As(e,0),ml(e,t,0,!0);break}e:{switch(l=e,r=n,r){case 0:case 1:throw Error(u(345));case 4:if((t&4194048)!==t)break;case 6:ml(l,t,la,!ol);break e;case 2:Ot=null;break;case 3:case 5:break;default:throw Error(u(329))}if((t&62914560)===t&&(n=No+300-wt(),10<n)){if(ml(l,t,la,!ol),Sl(l,0,!0)!==0)break e;l.timeoutHandle=Im(Em.bind(null,l,a,Ot,er,bo,t,la,Yl,Ss,ol,r,2,-0,0),n);break e}Em(l,a,Ot,er,bo,t,la,Yl,Ss,ol,r,0,-0,0)}}break}while(!0);wa(e)}function Em(e,t,a,l,n,r,f,p,w,M,H,Y,k,z){if(e.timeoutHandle=-1,Y=t.subtreeFlags,(Y&8192||(Y&16785408)===16785408)&&(Un={stylesheets:null,count:0,unsuspend:dg},ym(t),Y=mg(),Y!==null)){e.cancelPendingCommit=Y(_m.bind(null,e,t,r,a,l,n,f,p,w,H,1,k,z)),ml(e,r,f,!M);return}_m(e,t,r,a,l,n,f,p,w)}function Mp(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var n=a[l],r=n.getSnapshot;n=n.value;try{if(!Gt(r(),n))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ml(e,t,a,l){t&=~vo,t&=~Yl,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var n=t;0<n;){var r=31-pt(n),f=1<<r;l[r]=-1,n&=~f}a!==0&&de(e,a,t)}function tr(){return(ze&6)===0?(Mn(0),!1):!0}function Eo(){if(we!==null){if(De===0)var e=we.return;else e=we,Da=Ul=null,Gc(e),vs=null,bn=0,e=we;for(;e!==null;)sm(e.alternate,e),e=e.return;we=null}}function As(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,$p(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Eo(),qe=e,we=a=ka(e.current,null),Me=t,De=0,Qt=null,ol=!1,ws=El(e,t),yo=!1,Ss=la=vo=Yl=ul=Ke=0,Ot=Rn=null,bo=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var n=31-pt(l),r=1<<n;t|=e[n],l&=~r}return Ya=t,wi(),a}function Tm(e,t){ve=null,R.H=Yi,t===dn||t===_i?(t=Yd(),De=3):t===Hd?(t=Yd(),De=4):De=t===Xf?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Qt=t,we===null&&(Ke=1,Ki(e,Ft(t,e.current)))}function Am(){var e=R.H;return R.H=Yi,e===null?Yi:e}function Rm(){var e=R.A;return R.A=Rp,e}function To(){Ke=4,ol||(Me&4194048)!==Me&&aa.current!==null||(ws=!0),(ul&134217727)===0&&(Yl&134217727)===0||qe===null||ml(qe,Me,la,!1)}function Ao(e,t,a){var l=ze;ze|=2;var n=Am(),r=Rm();(qe!==e||Me!==t)&&(er=null,As(e,t)),t=!1;var f=Ke;e:do try{if(De!==0&&we!==null){var p=we,w=Qt;switch(De){case 8:Eo(),f=6;break e;case 3:case 2:case 9:case 6:aa.current===null&&(t=!0);var M=De;if(De=0,Qt=null,Rs(e,p,w,M),a&&ws){f=0;break e}break;default:M=De,De=0,Qt=null,Rs(e,p,w,M)}}kp(),f=Ke;break}catch(H){Tm(e,H)}while(!0);return t&&e.shellSuspendCounter++,Da=Ul=null,ze=l,R.H=n,R.A=r,we===null&&(qe=null,Me=0,wi()),f}function kp(){for(;we!==null;)Cm(we)}function _p(e,t){var a=ze;ze|=2;var l=Am(),n=Rm();qe!==e||Me!==t?(er=null,Ii=wt()+500,As(e,t)):ws=El(e,t);e:do try{if(De!==0&&we!==null){t=we;var r=Qt;t:switch(De){case 1:De=0,Qt=null,Rs(e,t,r,1);break;case 2:case 9:if(qd(r)){De=0,Qt=null,Mm(t);break}t=function(){De!==2&&De!==9||qe!==e||(De=7),wa(e)},r.then(t,t);break e;case 3:De=7;break e;case 4:De=5;break e;case 7:qd(r)?(De=0,Qt=null,Mm(t)):(De=0,Qt=null,Rs(e,t,r,7));break;case 5:var f=null;switch(we.tag){case 26:f=we.memoizedState;case 5:case 27:var p=we;if(!f||dh(f)){De=0,Qt=null;var w=p.sibling;if(w!==null)we=w;else{var M=p.return;M!==null?(we=M,ar(M)):we=null}break t}}De=0,Qt=null,Rs(e,t,r,5);break;case 6:De=0,Qt=null,Rs(e,t,r,6);break;case 8:Eo(),Ke=6;break e;default:throw Error(u(462))}}zp();break}catch(H){Tm(e,H)}while(!0);return Da=Ul=null,R.H=l,R.A=n,ze=a,we!==null?0:(qe=null,Me=0,wi(),Ke)}function zp(){for(;we!==null&&!Vr();)Cm(we)}function Cm(e){var t=am(e.alternate,e,Ya);e.memoizedProps=e.pendingProps,t===null?ar(e):we=t}function Mm(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=Pf(a,t,t.pendingProps,t.type,void 0,Me);break;case 11:t=Pf(a,t,t.pendingProps,t.type.render,t.ref,Me);break;case 5:Gc(t);default:sm(a,t),t=we=Md(t,Ya),t=am(a,t,Ya)}e.memoizedProps=e.pendingProps,t===null?ar(e):we=t}function Rs(e,t,a,l){Da=Ul=null,Gc(t),vs=null,bn=0;var n=t.return;try{if(jp(e,n,t,a,Me)){Ke=1,Ki(e,Ft(a,e.current)),we=null;return}}catch(r){if(n!==null)throw we=n,r;Ke=1,Ki(e,Ft(a,e.current)),we=null;return}t.flags&32768?(_e||l===1?e=!0:ws||(Me&536870912)!==0?e=!1:(ol=e=!0,(l===2||l===9||l===3||l===6)&&(l=aa.current,l!==null&&l.tag===13&&(l.flags|=16384))),km(t,e)):ar(t)}function ar(e){var t=e;do{if((t.flags&32768)!==0){km(t,ol);return}e=t.return;var a=Sp(t.alternate,t,Ya);if(a!==null){we=a;return}if(t=t.sibling,t!==null){we=t;return}we=t=e}while(t!==null);Ke===0&&(Ke=5)}function km(e,t){do{var a=Ep(e.alternate,e);if(a!==null){a.flags&=32767,we=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){we=e;return}we=e=a}while(e!==null);Ke=6,we=null}function _m(e,t,a,l,n,r,f,p,w){e.cancelPendingCommit=null;do lr();while(vt!==0);if((ze&6)!==0)throw Error(u(327));if(t!==null){if(t===e.current)throw Error(u(177));if(r=t.lanes|t.childLanes,r|=pc,di(e,a,r,f,p,w),e===qe&&(we=qe=null,Me=0),Es=t,fl=e,Ts=a,jo=r,wo=n,jm=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Bp(Zl,function(){return Bm(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=R.T,R.T=null,n=Z.p,Z.p=2,f=ze,ze|=4;try{Tp(e,t,a)}finally{ze=f,Z.p=n,R.T=l}}vt=1,zm(),Dm(),Om()}}function zm(){if(vt===1){vt=0;var e=fl,t=Es,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=R.T,R.T=null;var l=Z.p;Z.p=2;var n=ze;ze|=4;try{xm(t,e);var r=Ho,f=bd(e.containerInfo),p=r.focusedElem,w=r.selectionRange;if(f!==p&&p&&p.ownerDocument&&vd(p.ownerDocument.documentElement,p)){if(w!==null&&dc(p)){var M=w.start,H=w.end;if(H===void 0&&(H=M),"selectionStart"in p)p.selectionStart=M,p.selectionEnd=Math.min(H,p.value.length);else{var Y=p.ownerDocument||document,k=Y&&Y.defaultView||window;if(k.getSelection){var z=k.getSelection(),re=p.textContent.length,se=Math.min(w.start,re),Be=w.end===void 0?se:Math.min(w.end,re);!z.extend&&se>Be&&(f=Be,Be=se,se=f);var A=yd(p,se),T=yd(p,Be);if(A&&T&&(z.rangeCount!==1||z.anchorNode!==A.node||z.anchorOffset!==A.offset||z.focusNode!==T.node||z.focusOffset!==T.offset)){var C=Y.createRange();C.setStart(A.node,A.offset),z.removeAllRanges(),se>Be?(z.addRange(C),z.extend(T.node,T.offset)):(C.setEnd(T.node,T.offset),z.addRange(C))}}}}for(Y=[],z=p;z=z.parentNode;)z.nodeType===1&&Y.push({element:z,left:z.scrollLeft,top:z.scrollTop});for(typeof p.focus=="function"&&p.focus(),p=0;p<Y.length;p++){var q=Y[p];q.element.scrollLeft=q.left,q.element.scrollTop=q.top}}xr=!!Lo,Ho=Lo=null}finally{ze=n,Z.p=l,R.T=a}}e.current=t,vt=2}}function Dm(){if(vt===2){vt=0;var e=fl,t=Es,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=R.T,R.T=null;var l=Z.p;Z.p=2;var n=ze;ze|=4;try{dm(e,t.alternate,t)}finally{ze=n,Z.p=l,R.T=a}}vt=3}}function Om(){if(vt===4||vt===3){vt=0,ii();var e=fl,t=Es,a=Ts,l=jm;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?vt=5:(vt=0,Es=fl=null,Um(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(dl=null),gt(a),t=t.stateNode,Ct&&typeof Ct.onCommitFiberRoot=="function")try{Ct.onCommitFiberRoot(ga,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=R.T,n=Z.p,Z.p=2,R.T=null;try{for(var r=e.onRecoverableError,f=0;f<l.length;f++){var p=l[f];r(p.value,{componentStack:p.stack})}}finally{R.T=t,Z.p=n}}(Ts&3)!==0&&lr(),wa(e),n=e.pendingLanes,(a&4194090)!==0&&(n&42)!==0?e===So?Cn++:(Cn=0,So=e):Cn=0,Mn(0)}}function Um(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,on(t)))}function lr(e){return zm(),Dm(),Om(),Bm()}function Bm(){if(vt!==5)return!1;var e=fl,t=jo;jo=0;var a=gt(Ts),l=R.T,n=Z.p;try{Z.p=32>a?32:a,R.T=null,a=wo,wo=null;var r=fl,f=Ts;if(vt=0,Es=fl=null,Ts=0,(ze&6)!==0)throw Error(u(331));var p=ze;if(ze|=4,bm(r.current),gm(r,r.current,f,a),ze=p,Mn(0,!1),Ct&&typeof Ct.onPostCommitFiberRoot=="function")try{Ct.onPostCommitFiberRoot(ga,r)}catch{}return!0}finally{Z.p=n,R.T=l,Um(e,t)}}function Lm(e,t,a){t=Ft(a,t),t=to(e.stateNode,t,2),e=al(e,t,2),e!==null&&(Wa(e,2),wa(e))}function Le(e,t,a){if(e.tag===3)Lm(e,e,a);else for(;t!==null;){if(t.tag===3){Lm(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(dl===null||!dl.has(l))){e=Ft(a,e),a=Yf(2),l=al(t,a,2),l!==null&&(Vf(a,l,t,e),Wa(l,2),wa(l));break}}t=t.return}}function Ro(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new Cp;var n=new Set;l.set(t,n)}else n=l.get(t),n===void 0&&(n=new Set,l.set(t,n));n.has(a)||(yo=!0,n.add(a),e=Dp.bind(null,e,t,a),t.then(e,e))}function Dp(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,qe===e&&(Me&a)===a&&(Ke===4||Ke===3&&(Me&62914560)===Me&&300>wt()-No?(ze&2)===0&&As(e,0):vo|=a,Ss===Me&&(Ss=0)),wa(e)}function Hm(e,t){t===0&&(t=ui()),e=os(e,t),e!==null&&(Wa(e,t),wa(e))}function Op(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Hm(e,a)}function Up(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,n=e.memoizedState;n!==null&&(a=n.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(u(314))}l!==null&&l.delete(t),Hm(e,a)}function Bp(e,t){return Xs(e,t)}var sr=null,Cs=null,Co=!1,nr=!1,Mo=!1,Vl=0;function wa(e){e!==Cs&&e.next===null&&(Cs===null?sr=Cs=e:Cs=Cs.next=e),nr=!0,Co||(Co=!0,Hp())}function Mn(e,t){if(!Mo&&nr){Mo=!0;do for(var a=!1,l=sr;l!==null;){if(e!==0){var n=l.pendingLanes;if(n===0)var r=0;else{var f=l.suspendedLanes,p=l.pingedLanes;r=(1<<31-pt(42|e)+1)-1,r&=n&~(f&~p),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(a=!0,Vm(l,r))}else r=Me,r=Sl(l,l===qe?r:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(r&3)===0||El(l,r)||(a=!0,Vm(l,r));l=l.next}while(a);Mo=!1}}function Lp(){qm()}function qm(){nr=Co=!1;var e=0;Vl!==0&&(Kp()&&(e=Vl),Vl=0);for(var t=wt(),a=null,l=sr;l!==null;){var n=l.next,r=Gm(l,t);r===0?(l.next=null,a===null?sr=n:a.next=n,n===null&&(Cs=a)):(a=l,(e!==0||(r&3)!==0)&&(nr=!0)),l=n}Mn(e)}function Gm(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,n=e.expirationTimes,r=e.pendingLanes&-62914561;0<r;){var f=31-pt(r),p=1<<f,w=n[f];w===-1?((p&a)===0||(p&l)!==0)&&(n[f]=Qs(p,t)):w<=t&&(e.expiredLanes|=p),r&=~p}if(t=qe,a=Me,a=Sl(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(De===2||De===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&wl(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||El(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&wl(l),gt(a)){case 2:case 8:a=ci;break;case 32:a=Zl;break;case 268435456:a=Aa;break;default:a=Zl}return l=Ym.bind(null,e),a=Xs(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&wl(l),e.callbackPriority=2,e.callbackNode=null,2}function Ym(e,t){if(vt!==0&&vt!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(lr()&&e.callbackNode!==a)return null;var l=Me;return l=Sl(e,e===qe?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Sm(e,l,t),Gm(e,wt()),e.callbackNode!=null&&e.callbackNode===a?Ym.bind(null,e):null)}function Vm(e,t){if(lr())return null;Sm(e,t,!0)}function Hp(){Jp(function(){(ze&6)!==0?Xs(Jt,Lp):qm()})}function ko(){return Vl===0&&(Vl=ca()),Vl}function Xm(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:pi(""+e)}function Qm(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function qp(e,t,a,l,n){if(t==="submit"&&a&&a.stateNode===n){var r=Xm((n[He]||null).action),f=l.submitter;f&&(t=(t=f[He]||null)?Xm(t.formAction):f.getAttribute("formAction"),t!==null&&(r=t,f=null));var p=new bi("action","action",null,l,n);e.push({event:p,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Vl!==0){var w=f?Qm(n,f):new FormData(n);Pc(a,{pending:!0,data:w,method:n.method,action:r},null,w)}}else typeof r=="function"&&(p.preventDefault(),w=f?Qm(n,f):new FormData(n),Pc(a,{pending:!0,data:w,method:n.method,action:r},r,w))},currentTarget:n}]})}}for(var _o=0;_o<xc.length;_o++){var zo=xc[_o],Gp=zo.toLowerCase(),Yp=zo[0].toUpperCase()+zo.slice(1);oa(Gp,"on"+Yp)}oa(wd,"onAnimationEnd"),oa(Sd,"onAnimationIteration"),oa(Ed,"onAnimationStart"),oa("dblclick","onDoubleClick"),oa("focusin","onFocus"),oa("focusout","onBlur"),oa(np,"onTransitionRun"),oa(ip,"onTransitionStart"),oa(rp,"onTransitionCancel"),oa(Td,"onTransitionEnd"),Il("onMouseEnter",["mouseout","mouseover"]),Il("onMouseLeave",["mouseout","mouseover"]),Il("onPointerEnter",["pointerout","pointerover"]),Il("onPointerLeave",["pointerout","pointerover"]),Al("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Al("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Al("onBeforeInput",["compositionend","keypress","textInput","paste"]),Al("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Al("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Al("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var kn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Vp=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(kn));function Zm(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],n=l.event;l=l.listeners;e:{var r=void 0;if(t)for(var f=l.length-1;0<=f;f--){var p=l[f],w=p.instance,M=p.currentTarget;if(p=p.listener,w!==r&&n.isPropagationStopped())break e;r=p,n.currentTarget=M;try{r(n)}catch(H){Zi(H)}n.currentTarget=null,r=w}else for(f=0;f<l.length;f++){if(p=l[f],w=p.instance,M=p.currentTarget,p=p.listener,w!==r&&n.isPropagationStopped())break e;r=p,n.currentTarget=M;try{r(n)}catch(H){Zi(H)}n.currentTarget=null,r=w}}}}function Se(e,t){var a=t[Jl];a===void 0&&(a=t[Jl]=new Set);var l=e+"__bubble";a.has(l)||(Km(t,e,2,!1),a.add(l))}function Do(e,t,a){var l=0;t&&(l|=4),Km(a,e,l,t)}var ir="_reactListening"+Math.random().toString(36).slice(2);function Oo(e){if(!e[ir]){e[ir]=!0,Hu.forEach(function(a){a!=="selectionchange"&&(Vp.has(a)||Do(a,!1,e),Do(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ir]||(t[ir]=!0,Do("selectionchange",!1,t))}}function Km(e,t,a,l){switch(gh(t)){case 2:var n=pg;break;case 8:n=gg;break;default:n=Jo}a=n.bind(null,t,a,e),n=void 0,!ac||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),l?n!==void 0?e.addEventListener(t,a,{capture:!0,passive:n}):e.addEventListener(t,a,!0):n!==void 0?e.addEventListener(t,a,{passive:n}):e.addEventListener(t,a,!1)}function Uo(e,t,a,l,n){var r=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var f=l.tag;if(f===3||f===4){var p=l.stateNode.containerInfo;if(p===n)break;if(f===4)for(f=l.return;f!==null;){var w=f.tag;if((w===3||w===4)&&f.stateNode.containerInfo===n)return;f=f.return}for(;p!==null;){if(f=Pl(p),f===null)return;if(w=f.tag,w===5||w===6||w===26||w===27){l=r=f;continue e}p=p.parentNode}}l=l.return}Iu(function(){var M=r,H=ec(a),Y=[];e:{var k=Ad.get(e);if(k!==void 0){var z=bi,re=e;switch(e){case"keypress":if(yi(a)===0)break e;case"keydown":case"keyup":z=Bx;break;case"focusin":re="focus",z=ic;break;case"focusout":re="blur",z=ic;break;case"beforeblur":case"afterblur":z=ic;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":z=ad;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":z=Ex;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":z=qx;break;case wd:case Sd:case Ed:z=Rx;break;case Td:z=Yx;break;case"scroll":case"scrollend":z=wx;break;case"wheel":z=Xx;break;case"copy":case"cut":case"paste":z=Mx;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":z=sd;break;case"toggle":case"beforetoggle":z=Zx}var se=(t&4)!==0,Be=!se&&(e==="scroll"||e==="scrollend"),A=se?k!==null?k+"Capture":null:k;se=[];for(var T=M,C;T!==null;){var q=T;if(C=q.stateNode,q=q.tag,q!==5&&q!==26&&q!==27||C===null||A===null||(q=Js(T,A),q!=null&&se.push(_n(T,q,C))),Be)break;T=T.return}0<se.length&&(k=new z(k,re,null,a,H),Y.push({event:k,listeners:se}))}}if((t&7)===0){e:{if(k=e==="mouseover"||e==="pointerover",z=e==="mouseout"||e==="pointerout",k&&a!==Ir&&(re=a.relatedTarget||a.fromElement)&&(Pl(re)||re[ut]))break e;if((z||k)&&(k=H.window===H?H:(k=H.ownerDocument)?k.defaultView||k.parentWindow:window,z?(re=a.relatedTarget||a.toElement,z=M,re=re?Pl(re):null,re!==null&&(Be=h(re),se=re.tag,re!==Be||se!==5&&se!==27&&se!==6)&&(re=null)):(z=null,re=M),z!==re)){if(se=ad,q="onMouseLeave",A="onMouseEnter",T="mouse",(e==="pointerout"||e==="pointerover")&&(se=sd,q="onPointerLeave",A="onPointerEnter",T="pointer"),Be=z==null?k:$s(z),C=re==null?k:$s(re),k=new se(q,T+"leave",z,a,H),k.target=Be,k.relatedTarget=C,q=null,Pl(H)===M&&(se=new se(A,T+"enter",re,a,H),se.target=C,se.relatedTarget=Be,q=se),Be=q,z&&re)t:{for(se=z,A=re,T=0,C=se;C;C=Ms(C))T++;for(C=0,q=A;q;q=Ms(q))C++;for(;0<T-C;)se=Ms(se),T--;for(;0<C-T;)A=Ms(A),C--;for(;T--;){if(se===A||A!==null&&se===A.alternate)break t;se=Ms(se),A=Ms(A)}se=null}else se=null;z!==null&&$m(Y,k,z,se,!1),re!==null&&Be!==null&&$m(Y,Be,re,se,!0)}}e:{if(k=M?$s(M):window,z=k.nodeName&&k.nodeName.toLowerCase(),z==="select"||z==="input"&&k.type==="file")var ee=fd;else if(ud(k))if(md)ee=ap;else{ee=ep;var je=Ix}else z=k.nodeName,!z||z.toLowerCase()!=="input"||k.type!=="checkbox"&&k.type!=="radio"?M&&Fr(M.elementType)&&(ee=fd):ee=tp;if(ee&&(ee=ee(e,M))){dd(Y,ee,a,H);break e}je&&je(e,k,M),e==="focusout"&&M&&k.type==="number"&&M.memoizedProps.value!=null&&Wr(k,"number",k.value)}switch(je=M?$s(M):window,e){case"focusin":(ud(je)||je.contentEditable==="true")&&(is=je,fc=M,ln=null);break;case"focusout":ln=fc=is=null;break;case"mousedown":mc=!0;break;case"contextmenu":case"mouseup":case"dragend":mc=!1,Nd(Y,a,H);break;case"selectionchange":if(sp)break;case"keydown":case"keyup":Nd(Y,a,H)}var le;if(cc)e:{switch(e){case"compositionstart":var ne="onCompositionStart";break e;case"compositionend":ne="onCompositionEnd";break e;case"compositionupdate":ne="onCompositionUpdate";break e}ne=void 0}else ns?cd(e,a)&&(ne="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(ne="onCompositionStart");ne&&(nd&&a.locale!=="ko"&&(ns||ne!=="onCompositionStart"?ne==="onCompositionEnd"&&ns&&(le=ed()):(Fa=H,lc="value"in Fa?Fa.value:Fa.textContent,ns=!0)),je=rr(M,ne),0<je.length&&(ne=new ld(ne,e,null,a,H),Y.push({event:ne,listeners:je}),le?ne.data=le:(le=od(a),le!==null&&(ne.data=le)))),(le=$x?Jx(e,a):Px(e,a))&&(ne=rr(M,"onBeforeInput"),0<ne.length&&(je=new ld("onBeforeInput","beforeinput",null,a,H),Y.push({event:je,listeners:ne}),je.data=le)),qp(Y,e,M,a,H)}Zm(Y,t)})}function _n(e,t,a){return{instance:e,listener:t,currentTarget:a}}function rr(e,t){for(var a=t+"Capture",l=[];e!==null;){var n=e,r=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||r===null||(n=Js(e,a),n!=null&&l.unshift(_n(e,n,r)),n=Js(e,t),n!=null&&l.push(_n(e,n,r))),e.tag===3)return l;e=e.return}return[]}function Ms(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function $m(e,t,a,l,n){for(var r=t._reactName,f=[];a!==null&&a!==l;){var p=a,w=p.alternate,M=p.stateNode;if(p=p.tag,w!==null&&w===l)break;p!==5&&p!==26&&p!==27||M===null||(w=M,n?(M=Js(a,r),M!=null&&f.unshift(_n(a,M,w))):n||(M=Js(a,r),M!=null&&f.push(_n(a,M,w)))),a=a.return}f.length!==0&&e.push({event:t,listeners:f})}var Xp=/\r\n?/g,Qp=/\u0000|\uFFFD/g;function Jm(e){return(typeof e=="string"?e:""+e).replace(Xp,`
`).replace(Qp,"")}function Pm(e,t){return t=Jm(t),Jm(e)===t}function cr(){}function Ue(e,t,a,l,n,r){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||as(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&as(e,""+l);break;case"className":mi(e,"class",l);break;case"tabIndex":mi(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":mi(e,a,l);break;case"style":Wu(e,l,r);break;case"data":if(t!=="object"){mi(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=pi(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(a==="formAction"?(t!=="input"&&Ue(e,t,"name",n.name,n,null),Ue(e,t,"formEncType",n.formEncType,n,null),Ue(e,t,"formMethod",n.formMethod,n,null),Ue(e,t,"formTarget",n.formTarget,n,null)):(Ue(e,t,"encType",n.encType,n,null),Ue(e,t,"method",n.method,n,null),Ue(e,t,"target",n.target,n,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=pi(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=cr);break;case"onScroll":l!=null&&Se("scroll",e);break;case"onScrollEnd":l!=null&&Se("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(u(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=pi(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":Se("beforetoggle",e),Se("toggle",e),fi(e,"popover",l);break;case"xlinkActuate":Ca(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Ca(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Ca(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Ca(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Ca(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Ca(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Ca(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Ca(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Ca(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":fi(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=Nx.get(a)||a,fi(e,a,l))}}function Bo(e,t,a,l,n,r){switch(a){case"style":Wu(e,l,r);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(u(60));e.innerHTML=a}}break;case"children":typeof l=="string"?as(e,l):(typeof l=="number"||typeof l=="bigint")&&as(e,""+l);break;case"onScroll":l!=null&&Se("scroll",e);break;case"onScrollEnd":l!=null&&Se("scrollend",e);break;case"onClick":l!=null&&(e.onclick=cr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!qu.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(n=a.endsWith("Capture"),t=a.slice(2,n?a.length-7:void 0),r=e[He]||null,r=r!=null?r[a]:null,typeof r=="function"&&e.removeEventListener(t,r,n),typeof l=="function")){typeof r!="function"&&r!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,n);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):fi(e,a,l)}}}function bt(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Se("error",e),Se("load",e);var l=!1,n=!1,r;for(r in a)if(a.hasOwnProperty(r)){var f=a[r];if(f!=null)switch(r){case"src":l=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:Ue(e,t,r,f,a,null)}}n&&Ue(e,t,"srcSet",a.srcSet,a,null),l&&Ue(e,t,"src",a.src,a,null);return;case"input":Se("invalid",e);var p=r=f=n=null,w=null,M=null;for(l in a)if(a.hasOwnProperty(l)){var H=a[l];if(H!=null)switch(l){case"name":n=H;break;case"type":f=H;break;case"checked":w=H;break;case"defaultChecked":M=H;break;case"value":r=H;break;case"defaultValue":p=H;break;case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(u(137,t));break;default:Ue(e,t,l,H,a,null)}}Ku(e,r,p,w,M,f,n,!1),hi(e);return;case"select":Se("invalid",e),l=f=r=null;for(n in a)if(a.hasOwnProperty(n)&&(p=a[n],p!=null))switch(n){case"value":r=p;break;case"defaultValue":f=p;break;case"multiple":l=p;default:Ue(e,t,n,p,a,null)}t=r,a=f,e.multiple=!!l,t!=null?ts(e,!!l,t,!1):a!=null&&ts(e,!!l,a,!0);return;case"textarea":Se("invalid",e),r=n=l=null;for(f in a)if(a.hasOwnProperty(f)&&(p=a[f],p!=null))switch(f){case"value":l=p;break;case"defaultValue":n=p;break;case"children":r=p;break;case"dangerouslySetInnerHTML":if(p!=null)throw Error(u(91));break;default:Ue(e,t,f,p,a,null)}Ju(e,l,n,r),hi(e);return;case"option":for(w in a)if(a.hasOwnProperty(w)&&(l=a[w],l!=null))switch(w){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Ue(e,t,w,l,a,null)}return;case"dialog":Se("beforetoggle",e),Se("toggle",e),Se("cancel",e),Se("close",e);break;case"iframe":case"object":Se("load",e);break;case"video":case"audio":for(l=0;l<kn.length;l++)Se(kn[l],e);break;case"image":Se("error",e),Se("load",e);break;case"details":Se("toggle",e);break;case"embed":case"source":case"link":Se("error",e),Se("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(M in a)if(a.hasOwnProperty(M)&&(l=a[M],l!=null))switch(M){case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:Ue(e,t,M,l,a,null)}return;default:if(Fr(t)){for(H in a)a.hasOwnProperty(H)&&(l=a[H],l!==void 0&&Bo(e,t,H,l,a,void 0));return}}for(p in a)a.hasOwnProperty(p)&&(l=a[p],l!=null&&Ue(e,t,p,l,a,null))}function Zp(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,r=null,f=null,p=null,w=null,M=null,H=null;for(z in a){var Y=a[z];if(a.hasOwnProperty(z)&&Y!=null)switch(z){case"checked":break;case"value":break;case"defaultValue":w=Y;default:l.hasOwnProperty(z)||Ue(e,t,z,null,l,Y)}}for(var k in l){var z=l[k];if(Y=a[k],l.hasOwnProperty(k)&&(z!=null||Y!=null))switch(k){case"type":r=z;break;case"name":n=z;break;case"checked":M=z;break;case"defaultChecked":H=z;break;case"value":f=z;break;case"defaultValue":p=z;break;case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(u(137,t));break;default:z!==Y&&Ue(e,t,k,z,l,Y)}}Pr(e,f,p,w,M,H,r,n);return;case"select":z=f=p=k=null;for(r in a)if(w=a[r],a.hasOwnProperty(r)&&w!=null)switch(r){case"value":break;case"multiple":z=w;default:l.hasOwnProperty(r)||Ue(e,t,r,null,l,w)}for(n in l)if(r=l[n],w=a[n],l.hasOwnProperty(n)&&(r!=null||w!=null))switch(n){case"value":k=r;break;case"defaultValue":p=r;break;case"multiple":f=r;default:r!==w&&Ue(e,t,n,r,l,w)}t=p,a=f,l=z,k!=null?ts(e,!!a,k,!1):!!l!=!!a&&(t!=null?ts(e,!!a,t,!0):ts(e,!!a,a?[]:"",!1));return;case"textarea":z=k=null;for(p in a)if(n=a[p],a.hasOwnProperty(p)&&n!=null&&!l.hasOwnProperty(p))switch(p){case"value":break;case"children":break;default:Ue(e,t,p,null,l,n)}for(f in l)if(n=l[f],r=a[f],l.hasOwnProperty(f)&&(n!=null||r!=null))switch(f){case"value":k=n;break;case"defaultValue":z=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(u(91));break;default:n!==r&&Ue(e,t,f,n,l,r)}$u(e,k,z);return;case"option":for(var re in a)if(k=a[re],a.hasOwnProperty(re)&&k!=null&&!l.hasOwnProperty(re))switch(re){case"selected":e.selected=!1;break;default:Ue(e,t,re,null,l,k)}for(w in l)if(k=l[w],z=a[w],l.hasOwnProperty(w)&&k!==z&&(k!=null||z!=null))switch(w){case"selected":e.selected=k&&typeof k!="function"&&typeof k!="symbol";break;default:Ue(e,t,w,k,l,z)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var se in a)k=a[se],a.hasOwnProperty(se)&&k!=null&&!l.hasOwnProperty(se)&&Ue(e,t,se,null,l,k);for(M in l)if(k=l[M],z=a[M],l.hasOwnProperty(M)&&k!==z&&(k!=null||z!=null))switch(M){case"children":case"dangerouslySetInnerHTML":if(k!=null)throw Error(u(137,t));break;default:Ue(e,t,M,k,l,z)}return;default:if(Fr(t)){for(var Be in a)k=a[Be],a.hasOwnProperty(Be)&&k!==void 0&&!l.hasOwnProperty(Be)&&Bo(e,t,Be,void 0,l,k);for(H in l)k=l[H],z=a[H],!l.hasOwnProperty(H)||k===z||k===void 0&&z===void 0||Bo(e,t,H,k,l,z);return}}for(var A in a)k=a[A],a.hasOwnProperty(A)&&k!=null&&!l.hasOwnProperty(A)&&Ue(e,t,A,null,l,k);for(Y in l)k=l[Y],z=a[Y],!l.hasOwnProperty(Y)||k===z||k==null&&z==null||Ue(e,t,Y,k,l,z)}var Lo=null,Ho=null;function or(e){return e.nodeType===9?e:e.ownerDocument}function Wm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Fm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function qo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Go=null;function Kp(){var e=window.event;return e&&e.type==="popstate"?e===Go?!1:(Go=e,!0):(Go=null,!1)}var Im=typeof setTimeout=="function"?setTimeout:void 0,$p=typeof clearTimeout=="function"?clearTimeout:void 0,eh=typeof Promise=="function"?Promise:void 0,Jp=typeof queueMicrotask=="function"?queueMicrotask:typeof eh<"u"?function(e){return eh.resolve(null).then(e).catch(Pp)}:Im;function Pp(e){setTimeout(function(){throw e})}function hl(e){return e==="head"}function th(e,t){var a=t,l=0,n=0;do{var r=a.nextSibling;if(e.removeChild(a),r&&r.nodeType===8)if(a=r.data,a==="/$"){if(0<l&&8>l){a=l;var f=e.ownerDocument;if(a&1&&zn(f.documentElement),a&2&&zn(f.body),a&4)for(a=f.head,zn(a),f=a.firstChild;f;){var p=f.nextSibling,w=f.nodeName;f[Ks]||w==="SCRIPT"||w==="STYLE"||w==="LINK"&&f.rel.toLowerCase()==="stylesheet"||a.removeChild(f),f=p}}if(n===0){e.removeChild(r),Gn(t);return}n--}else a==="$"||a==="$?"||a==="$!"?n++:l=a.charCodeAt(0)-48;else l=0;a=r}while(a);Gn(t)}function Yo(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Yo(a),Zr(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function Wp(e,t,a,l){for(;e.nodeType===1;){var n=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[Ks])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(r=e.getAttribute("rel"),r==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(r!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(r=e.getAttribute("src"),(r!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&r&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var r=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===r)return e}else return e;if(e=da(e.nextSibling),e===null)break}return null}function Fp(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=da(e.nextSibling),e===null))return null;return e}function Vo(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Ip(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function da(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Xo=null;function ah(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function lh(e,t,a){switch(t=or(a),e){case"html":if(e=t.documentElement,!e)throw Error(u(452));return e;case"head":if(e=t.head,!e)throw Error(u(453));return e;case"body":if(e=t.body,!e)throw Error(u(454));return e;default:throw Error(u(451))}}function zn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Zr(e)}var sa=new Map,sh=new Set;function ur(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Va=Z.d;Z.d={f:eg,r:tg,D:ag,C:lg,L:sg,m:ng,X:rg,S:ig,M:cg};function eg(){var e=Va.f(),t=tr();return e||t}function tg(e){var t=Wl(e);t!==null&&t.tag===5&&t.type==="form"?Sf(t):Va.r(e)}var ks=typeof document>"u"?null:document;function nh(e,t,a){var l=ks;if(l&&typeof t=="string"&&t){var n=Wt(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof a=="string"&&(n+='[crossorigin="'+a+'"]'),sh.has(n)||(sh.add(n),e={rel:e,crossOrigin:a,href:t},l.querySelector(n)===null&&(t=l.createElement("link"),bt(t,"link",e),dt(t),l.head.appendChild(t)))}}function ag(e){Va.D(e),nh("dns-prefetch",e,null)}function lg(e,t){Va.C(e,t),nh("preconnect",e,t)}function sg(e,t,a){Va.L(e,t,a);var l=ks;if(l&&e&&t){var n='link[rel="preload"][as="'+Wt(t)+'"]';t==="image"&&a&&a.imageSrcSet?(n+='[imagesrcset="'+Wt(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(n+='[imagesizes="'+Wt(a.imageSizes)+'"]')):n+='[href="'+Wt(e)+'"]';var r=n;switch(t){case"style":r=_s(e);break;case"script":r=zs(e)}sa.has(r)||(e=b({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),sa.set(r,e),l.querySelector(n)!==null||t==="style"&&l.querySelector(Dn(r))||t==="script"&&l.querySelector(On(r))||(t=l.createElement("link"),bt(t,"link",e),dt(t),l.head.appendChild(t)))}}function ng(e,t){Va.m(e,t);var a=ks;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+Wt(l)+'"][href="'+Wt(e)+'"]',r=n;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=zs(e)}if(!sa.has(r)&&(e=b({rel:"modulepreload",href:e},t),sa.set(r,e),a.querySelector(n)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(On(r)))return}l=a.createElement("link"),bt(l,"link",e),dt(l),a.head.appendChild(l)}}}function ig(e,t,a){Va.S(e,t,a);var l=ks;if(l&&e){var n=Fl(l).hoistableStyles,r=_s(e);t=t||"default";var f=n.get(r);if(!f){var p={loading:0,preload:null};if(f=l.querySelector(Dn(r)))p.loading=5;else{e=b({rel:"stylesheet",href:e,"data-precedence":t},a),(a=sa.get(r))&&Qo(e,a);var w=f=l.createElement("link");dt(w),bt(w,"link",e),w._p=new Promise(function(M,H){w.onload=M,w.onerror=H}),w.addEventListener("load",function(){p.loading|=1}),w.addEventListener("error",function(){p.loading|=2}),p.loading|=4,dr(f,t,l)}f={type:"stylesheet",instance:f,count:1,state:p},n.set(r,f)}}}function rg(e,t){Va.X(e,t);var a=ks;if(a&&e){var l=Fl(a).hoistableScripts,n=zs(e),r=l.get(n);r||(r=a.querySelector(On(n)),r||(e=b({src:e,async:!0},t),(t=sa.get(n))&&Zo(e,t),r=a.createElement("script"),dt(r),bt(r,"link",e),a.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(n,r))}}function cg(e,t){Va.M(e,t);var a=ks;if(a&&e){var l=Fl(a).hoistableScripts,n=zs(e),r=l.get(n);r||(r=a.querySelector(On(n)),r||(e=b({src:e,async:!0,type:"module"},t),(t=sa.get(n))&&Zo(e,t),r=a.createElement("script"),dt(r),bt(r,"link",e),a.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(n,r))}}function ih(e,t,a,l){var n=(n=F.current)?ur(n):null;if(!n)throw Error(u(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=_s(a.href),a=Fl(n).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=_s(a.href);var r=Fl(n).hoistableStyles,f=r.get(e);if(f||(n=n.ownerDocument||n,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(e,f),(r=n.querySelector(Dn(e)))&&!r._p&&(f.instance=r,f.state.loading=5),sa.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},sa.set(e,a),r||og(n,e,a,f.state))),t&&l===null)throw Error(u(528,""));return f}if(t&&l!==null)throw Error(u(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=zs(a),a=Fl(n).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(u(444,e))}}function _s(e){return'href="'+Wt(e)+'"'}function Dn(e){return'link[rel="stylesheet"]['+e+"]"}function rh(e){return b({},e,{"data-precedence":e.precedence,precedence:null})}function og(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),bt(t,"link",a),dt(t),e.head.appendChild(t))}function zs(e){return'[src="'+Wt(e)+'"]'}function On(e){return"script[async]"+e}function ch(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+Wt(a.href)+'"]');if(l)return t.instance=l,dt(l),l;var n=b({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),dt(l),bt(l,"style",n),dr(l,a.precedence,e),t.instance=l;case"stylesheet":n=_s(a.href);var r=e.querySelector(Dn(n));if(r)return t.state.loading|=4,t.instance=r,dt(r),r;l=rh(a),(n=sa.get(n))&&Qo(l,n),r=(e.ownerDocument||e).createElement("link"),dt(r);var f=r;return f._p=new Promise(function(p,w){f.onload=p,f.onerror=w}),bt(r,"link",l),t.state.loading|=4,dr(r,a.precedence,e),t.instance=r;case"script":return r=zs(a.src),(n=e.querySelector(On(r)))?(t.instance=n,dt(n),n):(l=a,(n=sa.get(r))&&(l=b({},a),Zo(l,n)),e=e.ownerDocument||e,n=e.createElement("script"),dt(n),bt(n,"link",l),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(u(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,dr(l,a.precedence,e));return t.instance}function dr(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=l.length?l[l.length-1]:null,r=n,f=0;f<l.length;f++){var p=l[f];if(p.dataset.precedence===t)r=p;else if(r!==n)break}r?r.parentNode.insertBefore(e,r.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Qo(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Zo(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var fr=null;function oh(e,t,a){if(fr===null){var l=new Map,n=fr=new Map;n.set(a,l)}else n=fr,l=n.get(a),l||(l=new Map,n.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),n=0;n<a.length;n++){var r=a[n];if(!(r[Ks]||r[Pe]||e==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var f=r.getAttribute(t)||"";f=e+f;var p=l.get(f);p?p.push(r):l.set(f,[r])}}return l}function uh(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function ug(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function dh(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Un=null;function dg(){}function fg(e,t,a){if(Un===null)throw Error(u(475));var l=Un;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=_s(a.href),r=e.querySelector(Dn(n));if(r){e=r._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=mr.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=r,dt(r);return}r=e.ownerDocument||e,a=rh(a),(n=sa.get(n))&&Qo(a,n),r=r.createElement("link"),dt(r);var f=r;f._p=new Promise(function(p,w){f.onload=p,f.onerror=w}),bt(r,"link",a),t.instance=r}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=mr.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function mg(){if(Un===null)throw Error(u(475));var e=Un;return e.stylesheets&&e.count===0&&Ko(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&Ko(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function mr(){if(this.count--,this.count===0){if(this.stylesheets)Ko(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var hr=null;function Ko(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,hr=new Map,t.forEach(hg,e),hr=null,mr.call(e))}function hg(e,t){if(!(t.state.loading&4)){var a=hr.get(e);if(a)var l=a.get(null);else{a=new Map,hr.set(e,a);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<n.length;r++){var f=n[r];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(a.set(f.dataset.precedence,f),l=f)}l&&a.set(null,l)}n=t.instance,f=n.getAttribute("data-precedence"),r=a.get(f)||l,r===l&&a.set(null,n),a.set(f,n),this.count++,l=mr.bind(this),n.addEventListener("load",l),n.addEventListener("error",l),r?r.parentNode.insertBefore(n,r.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var Bn={$$typeof:O,Provider:null,Consumer:null,_currentValue:V,_currentValue2:V,_threadCount:0};function xg(e,t,a,l,n,r,f,p){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Zs(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Zs(0),this.hiddenUpdates=Zs(null),this.identifierPrefix=l,this.onUncaughtError=n,this.onCaughtError=r,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=p,this.incompleteTransitions=new Map}function fh(e,t,a,l,n,r,f,p,w,M,H,Y){return e=new xg(e,t,a,f,p,w,M,Y),t=1,r===!0&&(t|=24),r=Yt(3,null,null,t),e.current=r,r.stateNode=e,t=Ac(),t.refCount++,e.pooledCache=t,t.refCount++,r.memoizedState={element:l,isDehydrated:a,cache:t},kc(r),e}function mh(e){return e?(e=us,e):us}function hh(e,t,a,l,n,r){n=mh(n),l.context===null?l.context=n:l.pendingContext=n,l=tl(t),l.payload={element:a},r=r===void 0?null:r,r!==null&&(l.callback=r),a=al(e,l,t),a!==null&&(Kt(a,e,t),mn(a,e,t))}function xh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function $o(e,t){xh(e,t),(e=e.alternate)&&xh(e,t)}function ph(e){if(e.tag===13){var t=os(e,67108864);t!==null&&Kt(t,e,67108864),$o(e,67108864)}}var xr=!0;function pg(e,t,a,l){var n=R.T;R.T=null;var r=Z.p;try{Z.p=2,Jo(e,t,a,l)}finally{Z.p=r,R.T=n}}function gg(e,t,a,l){var n=R.T;R.T=null;var r=Z.p;try{Z.p=8,Jo(e,t,a,l)}finally{Z.p=r,R.T=n}}function Jo(e,t,a,l){if(xr){var n=Po(l);if(n===null)Uo(e,t,l,pr,a),yh(e,l);else if(vg(n,e,t,a,l))l.stopPropagation();else if(yh(e,l),t&4&&-1<yg.indexOf(e)){for(;n!==null;){var r=Wl(n);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var f=ya(r.pendingLanes);if(f!==0){var p=r;for(p.pendingLanes|=2,p.entangledLanes|=2;f;){var w=1<<31-pt(f);p.entanglements[1]|=w,f&=~w}wa(r),(ze&6)===0&&(Ii=wt()+500,Mn(0))}}break;case 13:p=os(r,2),p!==null&&Kt(p,r,2),tr(),$o(r,2)}if(r=Po(l),r===null&&Uo(e,t,l,pr,a),r===n)break;n=r}n!==null&&l.stopPropagation()}else Uo(e,t,l,null,a)}}function Po(e){return e=ec(e),Wo(e)}var pr=null;function Wo(e){if(pr=null,e=Pl(e),e!==null){var t=h(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=y(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return pr=e,null}function gh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ri()){case Jt:return 2;case ci:return 8;case Zl:case Rt:return 32;case Aa:return 268435456;default:return 32}default:return 32}}var Fo=!1,xl=null,pl=null,gl=null,Ln=new Map,Hn=new Map,yl=[],yg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function yh(e,t){switch(e){case"focusin":case"focusout":xl=null;break;case"dragenter":case"dragleave":pl=null;break;case"mouseover":case"mouseout":gl=null;break;case"pointerover":case"pointerout":Ln.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Hn.delete(t.pointerId)}}function qn(e,t,a,l,n,r){return e===null||e.nativeEvent!==r?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:r,targetContainers:[n]},t!==null&&(t=Wl(t),t!==null&&ph(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function vg(e,t,a,l,n){switch(t){case"focusin":return xl=qn(xl,e,t,a,l,n),!0;case"dragenter":return pl=qn(pl,e,t,a,l,n),!0;case"mouseover":return gl=qn(gl,e,t,a,l,n),!0;case"pointerover":var r=n.pointerId;return Ln.set(r,qn(Ln.get(r)||null,e,t,a,l,n)),!0;case"gotpointercapture":return r=n.pointerId,Hn.set(r,qn(Hn.get(r)||null,e,t,a,l,n)),!0}return!1}function vh(e){var t=Pl(e.target);if(t!==null){var a=h(t);if(a!==null){if(t=a.tag,t===13){if(t=y(a),t!==null){e.blockedOn=t,st(e.priority,function(){if(a.tag===13){var l=Zt();l=Ie(l);var n=os(a,l);n!==null&&Kt(n,a,l),$o(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function gr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=Po(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);Ir=l,a.target.dispatchEvent(l),Ir=null}else return t=Wl(a),t!==null&&ph(t),e.blockedOn=a,!1;t.shift()}return!0}function bh(e,t,a){gr(e)&&a.delete(t)}function bg(){Fo=!1,xl!==null&&gr(xl)&&(xl=null),pl!==null&&gr(pl)&&(pl=null),gl!==null&&gr(gl)&&(gl=null),Ln.forEach(bh),Hn.forEach(bh)}function yr(e,t){e.blockedOn===t&&(e.blockedOn=null,Fo||(Fo=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,bg)))}var vr=null;function Nh(e){vr!==e&&(vr=e,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){vr===e&&(vr=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],n=e[t+2];if(typeof l!="function"){if(Wo(l||a)===null)continue;break}var r=Wl(a);r!==null&&(e.splice(t,3),t-=3,Pc(r,{pending:!0,data:n,method:a.method,action:l},l,n))}}))}function Gn(e){function t(w){return yr(w,e)}xl!==null&&yr(xl,e),pl!==null&&yr(pl,e),gl!==null&&yr(gl,e),Ln.forEach(t),Hn.forEach(t);for(var a=0;a<yl.length;a++){var l=yl[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<yl.length&&(a=yl[0],a.blockedOn===null);)vh(a),a.blockedOn===null&&yl.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var n=a[l],r=a[l+1],f=n[He]||null;if(typeof r=="function")f||Nh(a);else if(f){var p=null;if(r&&r.hasAttribute("formAction")){if(n=r,f=r[He]||null)p=f.formAction;else if(Wo(n)!==null)continue}else p=f.action;typeof p=="function"?a[l+1]=p:(a.splice(l,3),l-=3),Nh(a)}}}function Io(e){this._internalRoot=e}br.prototype.render=Io.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(u(409));var a=t.current,l=Zt();hh(a,l,e,t,null,null)},br.prototype.unmount=Io.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;hh(e.current,2,null,e,null,null),tr(),t[ut]=null}};function br(e){this._internalRoot=e}br.prototype.unstable_scheduleHydration=function(e){if(e){var t=Tl();e={blockedOn:null,target:e,priority:t};for(var a=0;a<yl.length&&t!==0&&t<yl[a].priority;a++);yl.splice(a,0,e),a===0&&vh(e)}};var jh=c.version;if(jh!=="19.1.0")throw Error(u(527,jh,"19.1.0"));Z.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(u(188)):(e=Object.keys(e).join(","),Error(u(268,e)));return e=g(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var Ng={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:R,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Nr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Nr.isDisabled&&Nr.supportsFiber)try{ga=Nr.inject(Ng),Ct=Nr}catch{}}return Vn.createRoot=function(e,t){if(!d(e))throw Error(u(299));var a=!1,l="",n=Lf,r=Hf,f=qf,p=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(r=t.onCaughtError),t.onRecoverableError!==void 0&&(f=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(p=t.unstable_transitionCallbacks)),t=fh(e,1,!1,null,null,a,l,n,r,f,p,null),e[ut]=t.current,Oo(e),new Io(t)},Vn.hydrateRoot=function(e,t,a){if(!d(e))throw Error(u(299));var l=!1,n="",r=Lf,f=Hf,p=qf,w=null,M=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(n=a.identifierPrefix),a.onUncaughtError!==void 0&&(r=a.onUncaughtError),a.onCaughtError!==void 0&&(f=a.onCaughtError),a.onRecoverableError!==void 0&&(p=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(w=a.unstable_transitionCallbacks),a.formState!==void 0&&(M=a.formState)),t=fh(e,1,!0,t,a??null,l,n,r,f,p,w,M),t.context=mh(null),a=t.current,l=Zt(),l=Ie(l),n=tl(l),n.callback=null,al(a,n,l),a=l,t.current.lanes=a,Wa(t,a),wa(t),e[ut]=t.current,Oo(e),new br(t)},Vn.version="19.1.0",Vn}var _h;function kg(){if(_h)return au.exports;_h=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(c){console.error(c)}}return i(),au.exports=Mg(),au.exports}var _g=kg(),Xn={},zh;function zg(){if(zh)return Xn;zh=1,Object.defineProperty(Xn,"__esModule",{value:!0}),Xn.parse=y,Xn.serialize=m;const i=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,c=/^[\u0021-\u003A\u003C-\u007E]*$/,o=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,u=/^[\u0020-\u003A\u003D-\u007E]*$/,d=Object.prototype.toString,h=(()=>{const j=function(){};return j.prototype=Object.create(null),j})();function y(j,_){const E=new h,L=j.length;if(L<2)return E;const D=(_==null?void 0:_.decode)||b;let B=0;do{const Q=j.indexOf("=",B);if(Q===-1)break;const O=j.indexOf(";",B),P=O===-1?L:O;if(Q>P){B=j.lastIndexOf(";",Q-1)+1;continue}const J=v(j,B,Q),ie=g(j,Q,J),pe=j.slice(J,ie);if(E[pe]===void 0){let K=v(j,Q+1,P),I=g(j,P,K);const Ee=D(j.slice(K,I));E[pe]=Ee}B=P+1}while(B<L);return E}function v(j,_,E){do{const L=j.charCodeAt(_);if(L!==32&&L!==9)return _}while(++_<E);return E}function g(j,_,E){for(;_>E;){const L=j.charCodeAt(--_);if(L!==32&&L!==9)return _+1}return E}function m(j,_,E){const L=(E==null?void 0:E.encode)||encodeURIComponent;if(!i.test(j))throw new TypeError(`argument name is invalid: ${j}`);const D=L(_);if(!c.test(D))throw new TypeError(`argument val is invalid: ${_}`);let B=j+"="+D;if(!E)return B;if(E.maxAge!==void 0){if(!Number.isInteger(E.maxAge))throw new TypeError(`option maxAge is invalid: ${E.maxAge}`);B+="; Max-Age="+E.maxAge}if(E.domain){if(!o.test(E.domain))throw new TypeError(`option domain is invalid: ${E.domain}`);B+="; Domain="+E.domain}if(E.path){if(!u.test(E.path))throw new TypeError(`option path is invalid: ${E.path}`);B+="; Path="+E.path}if(E.expires){if(!x(E.expires)||!Number.isFinite(E.expires.valueOf()))throw new TypeError(`option expires is invalid: ${E.expires}`);B+="; Expires="+E.expires.toUTCString()}if(E.httpOnly&&(B+="; HttpOnly"),E.secure&&(B+="; Secure"),E.partitioned&&(B+="; Partitioned"),E.priority)switch(typeof E.priority=="string"?E.priority.toLowerCase():void 0){case"low":B+="; Priority=Low";break;case"medium":B+="; Priority=Medium";break;case"high":B+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${E.priority}`)}if(E.sameSite)switch(typeof E.sameSite=="string"?E.sameSite.toLowerCase():E.sameSite){case!0:case"strict":B+="; SameSite=Strict";break;case"lax":B+="; SameSite=Lax";break;case"none":B+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${E.sameSite}`)}return B}function b(j){if(j.indexOf("%")===-1)return j;try{return decodeURIComponent(j)}catch{return j}}function x(j){return d.call(j)==="[object Date]"}return Xn}zg();var Dh="popstate";function Dg(i={}){function c(u,d){let{pathname:h,search:y,hash:v}=u.location;return mu("",{pathname:h,search:y,hash:v},d.state&&d.state.usr||null,d.state&&d.state.key||"default")}function o(u,d){return typeof d=="string"?d:Jn(d)}return Ug(c,o,null,i)}function Xe(i,c){if(i===!1||i===null||typeof i>"u")throw new Error(c)}function ma(i,c){if(!i){typeof console<"u"&&console.warn(c);try{throw new Error(c)}catch{}}}function Og(){return Math.random().toString(36).substring(2,10)}function Oh(i,c){return{usr:i.state,key:i.key,idx:c}}function mu(i,c,o=null,u){return{pathname:typeof i=="string"?i:i.pathname,search:"",hash:"",...typeof c=="string"?Hs(c):c,state:o,key:c&&c.key||u||Og()}}function Jn({pathname:i="/",search:c="",hash:o=""}){return c&&c!=="?"&&(i+=c.charAt(0)==="?"?c:"?"+c),o&&o!=="#"&&(i+=o.charAt(0)==="#"?o:"#"+o),i}function Hs(i){let c={};if(i){let o=i.indexOf("#");o>=0&&(c.hash=i.substring(o),i=i.substring(0,o));let u=i.indexOf("?");u>=0&&(c.search=i.substring(u),i=i.substring(0,u)),i&&(c.pathname=i)}return c}function Ug(i,c,o,u={}){let{window:d=document.defaultView,v5Compat:h=!1}=u,y=d.history,v="POP",g=null,m=b();m==null&&(m=0,y.replaceState({...y.state,idx:m},""));function b(){return(y.state||{idx:null}).idx}function x(){v="POP";let D=b(),B=D==null?null:D-m;m=D,g&&g({action:v,location:L.location,delta:B})}function j(D,B){v="PUSH";let Q=mu(L.location,D,B);m=b()+1;let O=Oh(Q,m),P=L.createHref(Q);try{y.pushState(O,"",P)}catch(J){if(J instanceof DOMException&&J.name==="DataCloneError")throw J;d.location.assign(P)}h&&g&&g({action:v,location:L.location,delta:1})}function _(D,B){v="REPLACE";let Q=mu(L.location,D,B);m=b();let O=Oh(Q,m),P=L.createHref(Q);y.replaceState(O,"",P),h&&g&&g({action:v,location:L.location,delta:0})}function E(D){return Bg(D)}let L={get action(){return v},get location(){return i(d,y)},listen(D){if(g)throw new Error("A history only accepts one active listener");return d.addEventListener(Dh,x),g=D,()=>{d.removeEventListener(Dh,x),g=null}},createHref(D){return c(d,D)},createURL:E,encodeLocation(D){let B=E(D);return{pathname:B.pathname,search:B.search,hash:B.hash}},push:j,replace:_,go(D){return y.go(D)}};return L}function Bg(i,c=!1){let o="http://localhost";typeof window<"u"&&(o=window.location.origin!=="null"?window.location.origin:window.location.href),Xe(o,"No window.location.(origin|href) available to create URL");let u=typeof i=="string"?i:Jn(i);return u=u.replace(/ $/,"%20"),!c&&u.startsWith("//")&&(u=o+u),new URL(u,o)}function l0(i,c,o="/"){return Lg(i,c,o,!1)}function Lg(i,c,o,u){let d=typeof c=="string"?Hs(c):c,h=Ka(d.pathname||"/",o);if(h==null)return null;let y=s0(i);Hg(y);let v=null;for(let g=0;v==null&&g<y.length;++g){let m=Pg(h);v=$g(y[g],m,u)}return v}function s0(i,c=[],o=[],u=""){let d=(h,y,v)=>{let g={relativePath:v===void 0?h.path||"":v,caseSensitive:h.caseSensitive===!0,childrenIndex:y,route:h};g.relativePath.startsWith("/")&&(Xe(g.relativePath.startsWith(u),`Absolute route path "${g.relativePath}" nested under path "${u}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),g.relativePath=g.relativePath.slice(u.length));let m=Qa([u,g.relativePath]),b=o.concat(g);h.children&&h.children.length>0&&(Xe(h.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),s0(h.children,c,b,m)),!(h.path==null&&!h.index)&&c.push({path:m,score:Zg(m,h.index),routesMeta:b})};return i.forEach((h,y)=>{var v;if(h.path===""||!((v=h.path)!=null&&v.includes("?")))d(h,y);else for(let g of n0(h.path))d(h,y,g)}),c}function n0(i){let c=i.split("/");if(c.length===0)return[];let[o,...u]=c,d=o.endsWith("?"),h=o.replace(/\?$/,"");if(u.length===0)return d?[h,""]:[h];let y=n0(u.join("/")),v=[];return v.push(...y.map(g=>g===""?h:[h,g].join("/"))),d&&v.push(...y),v.map(g=>i.startsWith("/")&&g===""?"/":g)}function Hg(i){i.sort((c,o)=>c.score!==o.score?o.score-c.score:Kg(c.routesMeta.map(u=>u.childrenIndex),o.routesMeta.map(u=>u.childrenIndex)))}var qg=/^:[\w-]+$/,Gg=3,Yg=2,Vg=1,Xg=10,Qg=-2,Uh=i=>i==="*";function Zg(i,c){let o=i.split("/"),u=o.length;return o.some(Uh)&&(u+=Qg),c&&(u+=Yg),o.filter(d=>!Uh(d)).reduce((d,h)=>d+(qg.test(h)?Gg:h===""?Vg:Xg),u)}function Kg(i,c){return i.length===c.length&&i.slice(0,-1).every((u,d)=>u===c[d])?i[i.length-1]-c[c.length-1]:0}function $g(i,c,o=!1){let{routesMeta:u}=i,d={},h="/",y=[];for(let v=0;v<u.length;++v){let g=u[v],m=v===u.length-1,b=h==="/"?c:c.slice(h.length)||"/",x=kr({path:g.relativePath,caseSensitive:g.caseSensitive,end:m},b),j=g.route;if(!x&&m&&o&&!u[u.length-1].route.index&&(x=kr({path:g.relativePath,caseSensitive:g.caseSensitive,end:!1},b)),!x)return null;Object.assign(d,x.params),y.push({params:d,pathname:Qa([h,x.pathname]),pathnameBase:ey(Qa([h,x.pathnameBase])),route:j}),x.pathnameBase!=="/"&&(h=Qa([h,x.pathnameBase]))}return y}function kr(i,c){typeof i=="string"&&(i={path:i,caseSensitive:!1,end:!0});let[o,u]=Jg(i.path,i.caseSensitive,i.end),d=c.match(o);if(!d)return null;let h=d[0],y=h.replace(/(.)\/+$/,"$1"),v=d.slice(1);return{params:u.reduce((m,{paramName:b,isOptional:x},j)=>{if(b==="*"){let E=v[j]||"";y=h.slice(0,h.length-E.length).replace(/(.)\/+$/,"$1")}const _=v[j];return x&&!_?m[b]=void 0:m[b]=(_||"").replace(/%2F/g,"/"),m},{}),pathname:h,pathnameBase:y,pattern:i}}function Jg(i,c=!1,o=!0){ma(i==="*"||!i.endsWith("*")||i.endsWith("/*"),`Route path "${i}" will be treated as if it were "${i.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${i.replace(/\*$/,"/*")}".`);let u=[],d="^"+i.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(y,v,g)=>(u.push({paramName:v,isOptional:g!=null}),g?"/?([^\\/]+)?":"/([^\\/]+)"));return i.endsWith("*")?(u.push({paramName:"*"}),d+=i==="*"||i==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):o?d+="\\/*$":i!==""&&i!=="/"&&(d+="(?:(?=\\/|$))"),[new RegExp(d,c?void 0:"i"),u]}function Pg(i){try{return i.split("/").map(c=>decodeURIComponent(c).replace(/\//g,"%2F")).join("/")}catch(c){return ma(!1,`The URL path "${i}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${c}).`),i}}function Ka(i,c){if(c==="/")return i;if(!i.toLowerCase().startsWith(c.toLowerCase()))return null;let o=c.endsWith("/")?c.length-1:c.length,u=i.charAt(o);return u&&u!=="/"?null:i.slice(o)||"/"}function Wg(i,c="/"){let{pathname:o,search:u="",hash:d=""}=typeof i=="string"?Hs(i):i;return{pathname:o?o.startsWith("/")?o:Fg(o,c):c,search:ty(u),hash:ay(d)}}function Fg(i,c){let o=c.replace(/\/+$/,"").split("/");return i.split("/").forEach(d=>{d===".."?o.length>1&&o.pop():d!=="."&&o.push(d)}),o.length>1?o.join("/"):"/"}function iu(i,c,o,u){return`Cannot include a '${i}' character in a manually specified \`to.${c}\` field [${JSON.stringify(u)}].  Please separate it out to the \`to.${o}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Ig(i){return i.filter((c,o)=>o===0||c.route.path&&c.route.path.length>0)}function ju(i){let c=Ig(i);return c.map((o,u)=>u===c.length-1?o.pathname:o.pathnameBase)}function wu(i,c,o,u=!1){let d;typeof i=="string"?d=Hs(i):(d={...i},Xe(!d.pathname||!d.pathname.includes("?"),iu("?","pathname","search",d)),Xe(!d.pathname||!d.pathname.includes("#"),iu("#","pathname","hash",d)),Xe(!d.search||!d.search.includes("#"),iu("#","search","hash",d)));let h=i===""||d.pathname==="",y=h?"/":d.pathname,v;if(y==null)v=o;else{let x=c.length-1;if(!u&&y.startsWith("..")){let j=y.split("/");for(;j[0]==="..";)j.shift(),x-=1;d.pathname=j.join("/")}v=x>=0?c[x]:"/"}let g=Wg(d,v),m=y&&y!=="/"&&y.endsWith("/"),b=(h||y===".")&&o.endsWith("/");return!g.pathname.endsWith("/")&&(m||b)&&(g.pathname+="/"),g}var Qa=i=>i.join("/").replace(/\/\/+/g,"/"),ey=i=>i.replace(/\/+$/,"").replace(/^\/*/,"/"),ty=i=>!i||i==="?"?"":i.startsWith("?")?i:"?"+i,ay=i=>!i||i==="#"?"":i.startsWith("#")?i:"#"+i;function ly(i){return i!=null&&typeof i.status=="number"&&typeof i.statusText=="string"&&typeof i.internal=="boolean"&&"data"in i}var i0=["POST","PUT","PATCH","DELETE"];new Set(i0);var sy=["GET",...i0];new Set(sy);var qs=N.createContext(null);qs.displayName="DataRouter";var Ur=N.createContext(null);Ur.displayName="DataRouterState";var r0=N.createContext({isTransitioning:!1});r0.displayName="ViewTransition";var ny=N.createContext(new Map);ny.displayName="Fetchers";var iy=N.createContext(null);iy.displayName="Await";var ha=N.createContext(null);ha.displayName="Navigation";var Wn=N.createContext(null);Wn.displayName="Location";var Ta=N.createContext({outlet:null,matches:[],isDataRoute:!1});Ta.displayName="Route";var Su=N.createContext(null);Su.displayName="RouteError";function ry(i,{relative:c}={}){Xe(Gs(),"useHref() may be used only in the context of a <Router> component.");let{basename:o,navigator:u}=N.useContext(ha),{hash:d,pathname:h,search:y}=In(i,{relative:c}),v=h;return o!=="/"&&(v=h==="/"?o:Qa([o,h])),u.createHref({pathname:v,search:y,hash:d})}function Gs(){return N.useContext(Wn)!=null}function $a(){return Xe(Gs(),"useLocation() may be used only in the context of a <Router> component."),N.useContext(Wn).location}var c0="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function o0(i){N.useContext(ha).static||N.useLayoutEffect(i)}function Fn(){let{isDataRoute:i}=N.useContext(Ta);return i?by():cy()}function cy(){Xe(Gs(),"useNavigate() may be used only in the context of a <Router> component.");let i=N.useContext(qs),{basename:c,navigator:o}=N.useContext(ha),{matches:u}=N.useContext(Ta),{pathname:d}=$a(),h=JSON.stringify(ju(u)),y=N.useRef(!1);return o0(()=>{y.current=!0}),N.useCallback((g,m={})=>{if(ma(y.current,c0),!y.current)return;if(typeof g=="number"){o.go(g);return}let b=wu(g,JSON.parse(h),d,m.relative==="path");i==null&&c!=="/"&&(b.pathname=b.pathname==="/"?c:Qa([c,b.pathname])),(m.replace?o.replace:o.push)(b,m.state,m)},[c,o,h,d,i])}N.createContext(null);function In(i,{relative:c}={}){let{matches:o}=N.useContext(Ta),{pathname:u}=$a(),d=JSON.stringify(ju(o));return N.useMemo(()=>wu(i,JSON.parse(d),u,c==="path"),[i,d,u,c])}function oy(i,c){return u0(i,c)}function u0(i,c,o,u){var B;Xe(Gs(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:d}=N.useContext(ha),{matches:h}=N.useContext(Ta),y=h[h.length-1],v=y?y.params:{},g=y?y.pathname:"/",m=y?y.pathnameBase:"/",b=y&&y.route;{let Q=b&&b.path||"";d0(g,!b||Q.endsWith("*")||Q.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${g}" (under <Route path="${Q}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${Q}"> to <Route path="${Q==="/"?"*":`${Q}/*`}">.`)}let x=$a(),j;if(c){let Q=typeof c=="string"?Hs(c):c;Xe(m==="/"||((B=Q.pathname)==null?void 0:B.startsWith(m)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${m}" but pathname "${Q.pathname}" was given in the \`location\` prop.`),j=Q}else j=x;let _=j.pathname||"/",E=_;if(m!=="/"){let Q=m.replace(/^\//,"").split("/");E="/"+_.replace(/^\//,"").split("/").slice(Q.length).join("/")}let L=l0(i,{pathname:E});ma(b||L!=null,`No routes matched location "${j.pathname}${j.search}${j.hash}" `),ma(L==null||L[L.length-1].route.element!==void 0||L[L.length-1].route.Component!==void 0||L[L.length-1].route.lazy!==void 0,`Matched leaf route at location "${j.pathname}${j.search}${j.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let D=hy(L&&L.map(Q=>Object.assign({},Q,{params:Object.assign({},v,Q.params),pathname:Qa([m,d.encodeLocation?d.encodeLocation(Q.pathname).pathname:Q.pathname]),pathnameBase:Q.pathnameBase==="/"?m:Qa([m,d.encodeLocation?d.encodeLocation(Q.pathnameBase).pathname:Q.pathnameBase])})),h,o,u);return c&&D?N.createElement(Wn.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...j},navigationType:"POP"}},D):D}function uy(){let i=vy(),c=ly(i)?`${i.status} ${i.statusText}`:i instanceof Error?i.message:JSON.stringify(i),o=i instanceof Error?i.stack:null,u="rgba(200,200,200, 0.5)",d={padding:"0.5rem",backgroundColor:u},h={padding:"2px 4px",backgroundColor:u},y=null;return console.error("Error handled by React Router default ErrorBoundary:",i),y=N.createElement(N.Fragment,null,N.createElement("p",null,"💿 Hey developer 👋"),N.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",N.createElement("code",{style:h},"ErrorBoundary")," or"," ",N.createElement("code",{style:h},"errorElement")," prop on your route.")),N.createElement(N.Fragment,null,N.createElement("h2",null,"Unexpected Application Error!"),N.createElement("h3",{style:{fontStyle:"italic"}},c),o?N.createElement("pre",{style:d},o):null,y)}var dy=N.createElement(uy,null),fy=class extends N.Component{constructor(i){super(i),this.state={location:i.location,revalidation:i.revalidation,error:i.error}}static getDerivedStateFromError(i){return{error:i}}static getDerivedStateFromProps(i,c){return c.location!==i.location||c.revalidation!=="idle"&&i.revalidation==="idle"?{error:i.error,location:i.location,revalidation:i.revalidation}:{error:i.error!==void 0?i.error:c.error,location:c.location,revalidation:i.revalidation||c.revalidation}}componentDidCatch(i,c){console.error("React Router caught the following error during render",i,c)}render(){return this.state.error!==void 0?N.createElement(Ta.Provider,{value:this.props.routeContext},N.createElement(Su.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function my({routeContext:i,match:c,children:o}){let u=N.useContext(qs);return u&&u.static&&u.staticContext&&(c.route.errorElement||c.route.ErrorBoundary)&&(u.staticContext._deepestRenderedBoundaryId=c.route.id),N.createElement(Ta.Provider,{value:i},o)}function hy(i,c=[],o=null,u=null){if(i==null){if(!o)return null;if(o.errors)i=o.matches;else if(c.length===0&&!o.initialized&&o.matches.length>0)i=o.matches;else return null}let d=i,h=o==null?void 0:o.errors;if(h!=null){let g=d.findIndex(m=>m.route.id&&(h==null?void 0:h[m.route.id])!==void 0);Xe(g>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(h).join(",")}`),d=d.slice(0,Math.min(d.length,g+1))}let y=!1,v=-1;if(o)for(let g=0;g<d.length;g++){let m=d[g];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(v=g),m.route.id){let{loaderData:b,errors:x}=o,j=m.route.loader&&!b.hasOwnProperty(m.route.id)&&(!x||x[m.route.id]===void 0);if(m.route.lazy||j){y=!0,v>=0?d=d.slice(0,v+1):d=[d[0]];break}}}return d.reduceRight((g,m,b)=>{let x,j=!1,_=null,E=null;o&&(x=h&&m.route.id?h[m.route.id]:void 0,_=m.route.errorElement||dy,y&&(v<0&&b===0?(d0("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),j=!0,E=null):v===b&&(j=!0,E=m.route.hydrateFallbackElement||null)));let L=c.concat(d.slice(0,b+1)),D=()=>{let B;return x?B=_:j?B=E:m.route.Component?B=N.createElement(m.route.Component,null):m.route.element?B=m.route.element:B=g,N.createElement(my,{match:m,routeContext:{outlet:g,matches:L,isDataRoute:o!=null},children:B})};return o&&(m.route.ErrorBoundary||m.route.errorElement||b===0)?N.createElement(fy,{location:o.location,revalidation:o.revalidation,component:_,error:x,children:D(),routeContext:{outlet:null,matches:L,isDataRoute:!0}}):D()},null)}function Eu(i){return`${i} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function xy(i){let c=N.useContext(qs);return Xe(c,Eu(i)),c}function py(i){let c=N.useContext(Ur);return Xe(c,Eu(i)),c}function gy(i){let c=N.useContext(Ta);return Xe(c,Eu(i)),c}function Tu(i){let c=gy(i),o=c.matches[c.matches.length-1];return Xe(o.route.id,`${i} can only be used on routes that contain a unique "id"`),o.route.id}function yy(){return Tu("useRouteId")}function vy(){var u;let i=N.useContext(Su),c=py("useRouteError"),o=Tu("useRouteError");return i!==void 0?i:(u=c.errors)==null?void 0:u[o]}function by(){let{router:i}=xy("useNavigate"),c=Tu("useNavigate"),o=N.useRef(!1);return o0(()=>{o.current=!0}),N.useCallback(async(d,h={})=>{ma(o.current,c0),o.current&&(typeof d=="number"?i.navigate(d):await i.navigate(d,{fromRouteId:c,...h}))},[i,c])}var Bh={};function d0(i,c,o){!c&&!Bh[i]&&(Bh[i]=!0,ma(!1,o))}N.memo(Ny);function Ny({routes:i,future:c,state:o}){return u0(i,void 0,o,c)}function f0({to:i,replace:c,state:o,relative:u}){Xe(Gs(),"<Navigate> may be used only in the context of a <Router> component.");let{static:d}=N.useContext(ha);ma(!d,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:h}=N.useContext(Ta),{pathname:y}=$a(),v=Fn(),g=wu(i,ju(h),y,u==="path"),m=JSON.stringify(g);return N.useEffect(()=>{v(JSON.parse(m),{replace:c,state:o,relative:u})},[v,m,u,c,o]),null}function fa(i){Xe(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function jy({basename:i="/",children:c=null,location:o,navigationType:u="POP",navigator:d,static:h=!1}){Xe(!Gs(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let y=i.replace(/^\/*/,"/"),v=N.useMemo(()=>({basename:y,navigator:d,static:h,future:{}}),[y,d,h]);typeof o=="string"&&(o=Hs(o));let{pathname:g="/",search:m="",hash:b="",state:x=null,key:j="default"}=o,_=N.useMemo(()=>{let E=Ka(g,y);return E==null?null:{location:{pathname:E,search:m,hash:b,state:x,key:j},navigationType:u}},[y,g,m,b,x,j,u]);return ma(_!=null,`<Router basename="${y}"> is not able to match the URL "${g}${m}${b}" because it does not start with the basename, so the <Router> won't render anything.`),_==null?null:N.createElement(ha.Provider,{value:v},N.createElement(Wn.Provider,{children:c,value:_}))}function wy({children:i,location:c}){return oy(hu(i),c)}function hu(i,c=[]){let o=[];return N.Children.forEach(i,(u,d)=>{if(!N.isValidElement(u))return;let h=[...c,d];if(u.type===N.Fragment){o.push.apply(o,hu(u.props.children,h));return}Xe(u.type===fa,`[${typeof u.type=="string"?u.type:u.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Xe(!u.props.index||!u.props.children,"An index route cannot have child routes.");let y={id:u.props.id||h.join("-"),caseSensitive:u.props.caseSensitive,element:u.props.element,Component:u.props.Component,index:u.props.index,path:u.props.path,loader:u.props.loader,action:u.props.action,hydrateFallbackElement:u.props.hydrateFallbackElement,HydrateFallback:u.props.HydrateFallback,errorElement:u.props.errorElement,ErrorBoundary:u.props.ErrorBoundary,hasErrorBoundary:u.props.hasErrorBoundary===!0||u.props.ErrorBoundary!=null||u.props.errorElement!=null,shouldRevalidate:u.props.shouldRevalidate,handle:u.props.handle,lazy:u.props.lazy};u.props.children&&(y.children=hu(u.props.children,h)),o.push(y)}),o}var Cr="get",Mr="application/x-www-form-urlencoded";function Br(i){return i!=null&&typeof i.tagName=="string"}function Sy(i){return Br(i)&&i.tagName.toLowerCase()==="button"}function Ey(i){return Br(i)&&i.tagName.toLowerCase()==="form"}function Ty(i){return Br(i)&&i.tagName.toLowerCase()==="input"}function Ay(i){return!!(i.metaKey||i.altKey||i.ctrlKey||i.shiftKey)}function Ry(i,c){return i.button===0&&(!c||c==="_self")&&!Ay(i)}var jr=null;function Cy(){if(jr===null)try{new FormData(document.createElement("form"),0),jr=!1}catch{jr=!0}return jr}var My=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function ru(i){return i!=null&&!My.has(i)?(ma(!1,`"${i}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Mr}"`),null):i}function ky(i,c){let o,u,d,h,y;if(Ey(i)){let v=i.getAttribute("action");u=v?Ka(v,c):null,o=i.getAttribute("method")||Cr,d=ru(i.getAttribute("enctype"))||Mr,h=new FormData(i)}else if(Sy(i)||Ty(i)&&(i.type==="submit"||i.type==="image")){let v=i.form;if(v==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let g=i.getAttribute("formaction")||v.getAttribute("action");if(u=g?Ka(g,c):null,o=i.getAttribute("formmethod")||v.getAttribute("method")||Cr,d=ru(i.getAttribute("formenctype"))||ru(v.getAttribute("enctype"))||Mr,h=new FormData(v,i),!Cy()){let{name:m,type:b,value:x}=i;if(b==="image"){let j=m?`${m}.`:"";h.append(`${j}x`,"0"),h.append(`${j}y`,"0")}else m&&h.append(m,x)}}else{if(Br(i))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');o=Cr,u=null,d=Mr,y=i}return h&&d==="text/plain"&&(y=h,h=void 0),{action:u,method:o.toLowerCase(),encType:d,formData:h,body:y}}function Au(i,c){if(i===!1||i===null||typeof i>"u")throw new Error(c)}async function _y(i,c){if(i.id in c)return c[i.id];try{let o=await import(i.module);return c[i.id]=o,o}catch(o){return console.error(`Error loading route module \`${i.module}\`, reloading page...`),console.error(o),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function zy(i){return i==null?!1:i.href==null?i.rel==="preload"&&typeof i.imageSrcSet=="string"&&typeof i.imageSizes=="string":typeof i.rel=="string"&&typeof i.href=="string"}async function Dy(i,c,o){let u=await Promise.all(i.map(async d=>{let h=c.routes[d.route.id];if(h){let y=await _y(h,o);return y.links?y.links():[]}return[]}));return Ly(u.flat(1).filter(zy).filter(d=>d.rel==="stylesheet"||d.rel==="preload").map(d=>d.rel==="stylesheet"?{...d,rel:"prefetch",as:"style"}:{...d,rel:"prefetch"}))}function Lh(i,c,o,u,d,h){let y=(g,m)=>o[m]?g.route.id!==o[m].route.id:!0,v=(g,m)=>{var b;return o[m].pathname!==g.pathname||((b=o[m].route.path)==null?void 0:b.endsWith("*"))&&o[m].params["*"]!==g.params["*"]};return h==="assets"?c.filter((g,m)=>y(g,m)||v(g,m)):h==="data"?c.filter((g,m)=>{var x;let b=u.routes[g.route.id];if(!b||!b.hasLoader)return!1;if(y(g,m)||v(g,m))return!0;if(g.route.shouldRevalidate){let j=g.route.shouldRevalidate({currentUrl:new URL(d.pathname+d.search+d.hash,window.origin),currentParams:((x=o[0])==null?void 0:x.params)||{},nextUrl:new URL(i,window.origin),nextParams:g.params,defaultShouldRevalidate:!0});if(typeof j=="boolean")return j}return!0}):[]}function Oy(i,c,{includeHydrateFallback:o}={}){return Uy(i.map(u=>{let d=c.routes[u.route.id];if(!d)return[];let h=[d.module];return d.clientActionModule&&(h=h.concat(d.clientActionModule)),d.clientLoaderModule&&(h=h.concat(d.clientLoaderModule)),o&&d.hydrateFallbackModule&&(h=h.concat(d.hydrateFallbackModule)),d.imports&&(h=h.concat(d.imports)),h}).flat(1))}function Uy(i){return[...new Set(i)]}function By(i){let c={},o=Object.keys(i).sort();for(let u of o)c[u]=i[u];return c}function Ly(i,c){let o=new Set;return new Set(c),i.reduce((u,d)=>{let h=JSON.stringify(By(d));return o.has(h)||(o.add(h),u.push({key:h,link:d})),u},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Hy=new Set([100,101,204,205]);function qy(i,c){let o=typeof i=="string"?new URL(i,typeof window>"u"?"server://singlefetch/":window.location.origin):i;return o.pathname==="/"?o.pathname="_root.data":c&&Ka(o.pathname,c)==="/"?o.pathname=`${c.replace(/\/$/,"")}/_root.data`:o.pathname=`${o.pathname.replace(/\/$/,"")}.data`,o}function m0(){let i=N.useContext(qs);return Au(i,"You must render this element inside a <DataRouterContext.Provider> element"),i}function Gy(){let i=N.useContext(Ur);return Au(i,"You must render this element inside a <DataRouterStateContext.Provider> element"),i}var Ru=N.createContext(void 0);Ru.displayName="FrameworkContext";function h0(){let i=N.useContext(Ru);return Au(i,"You must render this element inside a <HydratedRouter> element"),i}function Yy(i,c){let o=N.useContext(Ru),[u,d]=N.useState(!1),[h,y]=N.useState(!1),{onFocus:v,onBlur:g,onMouseEnter:m,onMouseLeave:b,onTouchStart:x}=c,j=N.useRef(null);N.useEffect(()=>{if(i==="render"&&y(!0),i==="viewport"){let L=B=>{B.forEach(Q=>{y(Q.isIntersecting)})},D=new IntersectionObserver(L,{threshold:.5});return j.current&&D.observe(j.current),()=>{D.disconnect()}}},[i]),N.useEffect(()=>{if(u){let L=setTimeout(()=>{y(!0)},100);return()=>{clearTimeout(L)}}},[u]);let _=()=>{d(!0)},E=()=>{d(!1),y(!1)};return o?i!=="intent"?[h,j,{}]:[h,j,{onFocus:Qn(v,_),onBlur:Qn(g,E),onMouseEnter:Qn(m,_),onMouseLeave:Qn(b,E),onTouchStart:Qn(x,_)}]:[!1,j,{}]}function Qn(i,c){return o=>{i&&i(o),o.defaultPrevented||c(o)}}function Vy({page:i,...c}){let{router:o}=m0(),u=N.useMemo(()=>l0(o.routes,i,o.basename),[o.routes,i,o.basename]);return u?N.createElement(Qy,{page:i,matches:u,...c}):null}function Xy(i){let{manifest:c,routeModules:o}=h0(),[u,d]=N.useState([]);return N.useEffect(()=>{let h=!1;return Dy(i,c,o).then(y=>{h||d(y)}),()=>{h=!0}},[i,c,o]),u}function Qy({page:i,matches:c,...o}){let u=$a(),{manifest:d,routeModules:h}=h0(),{basename:y}=m0(),{loaderData:v,matches:g}=Gy(),m=N.useMemo(()=>Lh(i,c,g,d,u,"data"),[i,c,g,d,u]),b=N.useMemo(()=>Lh(i,c,g,d,u,"assets"),[i,c,g,d,u]),x=N.useMemo(()=>{if(i===u.pathname+u.search+u.hash)return[];let E=new Set,L=!1;if(c.forEach(B=>{var O;let Q=d.routes[B.route.id];!Q||!Q.hasLoader||(!m.some(P=>P.route.id===B.route.id)&&B.route.id in v&&((O=h[B.route.id])!=null&&O.shouldRevalidate)||Q.hasClientLoader?L=!0:E.add(B.route.id))}),E.size===0)return[];let D=qy(i,y);return L&&E.size>0&&D.searchParams.set("_routes",c.filter(B=>E.has(B.route.id)).map(B=>B.route.id).join(",")),[D.pathname+D.search]},[y,v,u,d,m,c,i,h]),j=N.useMemo(()=>Oy(b,d),[b,d]),_=Xy(b);return N.createElement(N.Fragment,null,x.map(E=>N.createElement("link",{key:E,rel:"prefetch",as:"fetch",href:E,...o})),j.map(E=>N.createElement("link",{key:E,rel:"modulepreload",href:E,...o})),_.map(({key:E,link:L})=>N.createElement("link",{key:E,...L})))}function Zy(...i){return c=>{i.forEach(o=>{typeof o=="function"?o(c):o!=null&&(o.current=c)})}}var x0=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{x0&&(window.__reactRouterVersion="7.6.3")}catch{}function Ky({basename:i,children:c,window:o}){let u=N.useRef();u.current==null&&(u.current=Dg({window:o,v5Compat:!0}));let d=u.current,[h,y]=N.useState({action:d.action,location:d.location}),v=N.useCallback(g=>{N.startTransition(()=>y(g))},[y]);return N.useLayoutEffect(()=>d.listen(v),[d,v]),N.createElement(jy,{basename:i,children:c,location:h.location,navigationType:h.action,navigator:d})}var p0=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Lt=N.forwardRef(function({onClick:c,discover:o="render",prefetch:u="none",relative:d,reloadDocument:h,replace:y,state:v,target:g,to:m,preventScrollReset:b,viewTransition:x,...j},_){let{basename:E}=N.useContext(ha),L=typeof m=="string"&&p0.test(m),D,B=!1;if(typeof m=="string"&&L&&(D=m,x0))try{let I=new URL(window.location.href),Ee=m.startsWith("//")?new URL(I.protocol+m):new URL(m),Te=Ka(Ee.pathname,E);Ee.origin===I.origin&&Te!=null?m=Te+Ee.search+Ee.hash:B=!0}catch{ma(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let Q=ry(m,{relative:d}),[O,P,J]=Yy(u,j),ie=Wy(m,{replace:y,state:v,target:g,preventScrollReset:b,relative:d,viewTransition:x});function pe(I){c&&c(I),I.defaultPrevented||ie(I)}let K=N.createElement("a",{...j,...J,href:D||Q,onClick:B||h?c:pe,ref:Zy(_,P),target:g,"data-discover":!L&&o==="render"?"true":void 0});return O&&!L?N.createElement(N.Fragment,null,K,N.createElement(Vy,{page:Q})):K});Lt.displayName="Link";var $y=N.forwardRef(function({"aria-current":c="page",caseSensitive:o=!1,className:u="",end:d=!1,style:h,to:y,viewTransition:v,children:g,...m},b){let x=In(y,{relative:m.relative}),j=$a(),_=N.useContext(Ur),{navigator:E,basename:L}=N.useContext(ha),D=_!=null&&av(x)&&v===!0,B=E.encodeLocation?E.encodeLocation(x).pathname:x.pathname,Q=j.pathname,O=_&&_.navigation&&_.navigation.location?_.navigation.location.pathname:null;o||(Q=Q.toLowerCase(),O=O?O.toLowerCase():null,B=B.toLowerCase()),O&&L&&(O=Ka(O,L)||O);const P=B!=="/"&&B.endsWith("/")?B.length-1:B.length;let J=Q===B||!d&&Q.startsWith(B)&&Q.charAt(P)==="/",ie=O!=null&&(O===B||!d&&O.startsWith(B)&&O.charAt(B.length)==="/"),pe={isActive:J,isPending:ie,isTransitioning:D},K=J?c:void 0,I;typeof u=="function"?I=u(pe):I=[u,J?"active":null,ie?"pending":null,D?"transitioning":null].filter(Boolean).join(" ");let Ee=typeof h=="function"?h(pe):h;return N.createElement(Lt,{...m,"aria-current":K,className:I,ref:b,style:Ee,to:y,viewTransition:v},typeof g=="function"?g(pe):g)});$y.displayName="NavLink";var Jy=N.forwardRef(({discover:i="render",fetcherKey:c,navigate:o,reloadDocument:u,replace:d,state:h,method:y=Cr,action:v,onSubmit:g,relative:m,preventScrollReset:b,viewTransition:x,...j},_)=>{let E=ev(),L=tv(v,{relative:m}),D=y.toLowerCase()==="get"?"get":"post",B=typeof v=="string"&&p0.test(v),Q=O=>{if(g&&g(O),O.defaultPrevented)return;O.preventDefault();let P=O.nativeEvent.submitter,J=(P==null?void 0:P.getAttribute("formmethod"))||y;E(P||O.currentTarget,{fetcherKey:c,method:J,navigate:o,replace:d,state:h,relative:m,preventScrollReset:b,viewTransition:x})};return N.createElement("form",{ref:_,method:D,action:L,onSubmit:u?g:Q,...j,"data-discover":!B&&i==="render"?"true":void 0})});Jy.displayName="Form";function Py(i){return`${i} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function g0(i){let c=N.useContext(qs);return Xe(c,Py(i)),c}function Wy(i,{target:c,replace:o,state:u,preventScrollReset:d,relative:h,viewTransition:y}={}){let v=Fn(),g=$a(),m=In(i,{relative:h});return N.useCallback(b=>{if(Ry(b,c)){b.preventDefault();let x=o!==void 0?o:Jn(g)===Jn(m);v(i,{replace:x,state:u,preventScrollReset:d,relative:h,viewTransition:y})}},[g,v,m,o,u,c,i,d,h,y])}var Fy=0,Iy=()=>`__${String(++Fy)}__`;function ev(){let{router:i}=g0("useSubmit"),{basename:c}=N.useContext(ha),o=yy();return N.useCallback(async(u,d={})=>{let{action:h,method:y,encType:v,formData:g,body:m}=ky(u,c);if(d.navigate===!1){let b=d.fetcherKey||Iy();await i.fetch(b,o,d.action||h,{preventScrollReset:d.preventScrollReset,formData:g,body:m,formMethod:d.method||y,formEncType:d.encType||v,flushSync:d.flushSync})}else await i.navigate(d.action||h,{preventScrollReset:d.preventScrollReset,formData:g,body:m,formMethod:d.method||y,formEncType:d.encType||v,replace:d.replace,state:d.state,fromRouteId:o,flushSync:d.flushSync,viewTransition:d.viewTransition})},[i,c,o])}function tv(i,{relative:c}={}){let{basename:o}=N.useContext(ha),u=N.useContext(Ta);Xe(u,"useFormAction must be used inside a RouteContext");let[d]=u.matches.slice(-1),h={...In(i||".",{relative:c})},y=$a();if(i==null){h.search=y.search;let v=new URLSearchParams(h.search),g=v.getAll("index");if(g.some(b=>b==="")){v.delete("index"),g.filter(x=>x).forEach(x=>v.append("index",x));let b=v.toString();h.search=b?`?${b}`:""}}return(!i||i===".")&&d.route.index&&(h.search=h.search?h.search.replace(/^\?/,"?index&"):"?index"),o!=="/"&&(h.pathname=h.pathname==="/"?o:Qa([o,h.pathname])),Jn(h)}function av(i,c={}){let o=N.useContext(r0);Xe(o!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:u}=g0("useViewTransitionState"),d=In(i,{relative:c.relative});if(!o.isTransitioning)return!1;let h=Ka(o.currentLocation.pathname,u)||o.currentLocation.pathname,y=Ka(o.nextLocation.pathname,u)||o.nextLocation.pathname;return kr(d.pathname,y)!=null||kr(d.pathname,h)!=null}[...Hy];var lv=a0();const sv=e0(lv),nv=N.createContext({theme:"system",setTheme:()=>null});function iv({children:i,defaultTheme:c="system",storageKey:o="vite-ui-theme",...u}){const[d,h]=N.useState(()=>localStorage.getItem(o)||c);N.useEffect(()=>{const v=window.document.documentElement;if(v.classList.remove("light","dark"),d==="system"){const g=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";v.classList.add(g);return}v.classList.add(d)},[d]);const y={theme:d,setTheme:v=>{localStorage.setItem(o,v),h(v)}};return s.jsx(nv.Provider,{...u,value:y,children:i})}var rv=(i,c,o,u,d,h,y,v)=>{let g=document.documentElement,m=["light","dark"];function b(_){(Array.isArray(i)?i:[i]).forEach(E=>{let L=E==="class",D=L&&h?d.map(B=>h[B]||B):d;L?(g.classList.remove(...D),g.classList.add(h&&h[_]?h[_]:_)):g.setAttribute(E,_)}),x(_)}function x(_){v&&m.includes(_)&&(g.style.colorScheme=_)}function j(){return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}if(u)b(u);else try{let _=localStorage.getItem(c)||o,E=y&&_==="system"?j():_;b(E)}catch{}},cv=N.createContext(void 0),ov={setTheme:i=>{},themes:[]},uv=()=>{var i;return(i=N.useContext(cv))!=null?i:ov};N.memo(({forcedTheme:i,storageKey:c,attribute:o,enableSystem:u,enableColorScheme:d,defaultTheme:h,value:y,themes:v,nonce:g,scriptProps:m})=>{let b=JSON.stringify([o,c,h,i,v,y,u,d]).slice(1,-1);return N.createElement("script",{...m,suppressHydrationWarning:!0,nonce:typeof window>"u"?g:"",dangerouslySetInnerHTML:{__html:`(${rv.toString()})(${b})`}})});function dv(i){if(typeof document>"u")return;let c=document.head||document.getElementsByTagName("head")[0],o=document.createElement("style");o.type="text/css",c.appendChild(o),o.styleSheet?o.styleSheet.cssText=i:o.appendChild(document.createTextNode(i))}const fv=i=>{switch(i){case"success":return xv;case"info":return gv;case"warning":return pv;case"error":return yv;default:return null}},mv=Array(12).fill(0),hv=({visible:i,className:c})=>$.createElement("div",{className:["sonner-loading-wrapper",c].filter(Boolean).join(" "),"data-visible":i},$.createElement("div",{className:"sonner-spinner"},mv.map((o,u)=>$.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${u}`})))),xv=$.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},$.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),pv=$.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},$.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),gv=$.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},$.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),yv=$.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},$.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),vv=$.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},$.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),$.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),bv=()=>{const[i,c]=$.useState(document.hidden);return $.useEffect(()=>{const o=()=>{c(document.hidden)};return document.addEventListener("visibilitychange",o),()=>window.removeEventListener("visibilitychange",o)},[]),i};let xu=1;class Nv{constructor(){this.subscribe=c=>(this.subscribers.push(c),()=>{const o=this.subscribers.indexOf(c);this.subscribers.splice(o,1)}),this.publish=c=>{this.subscribers.forEach(o=>o(c))},this.addToast=c=>{this.publish(c),this.toasts=[...this.toasts,c]},this.create=c=>{var o;const{message:u,...d}=c,h=typeof(c==null?void 0:c.id)=="number"||((o=c.id)==null?void 0:o.length)>0?c.id:xu++,y=this.toasts.find(g=>g.id===h),v=c.dismissible===void 0?!0:c.dismissible;return this.dismissedToasts.has(h)&&this.dismissedToasts.delete(h),y?this.toasts=this.toasts.map(g=>g.id===h?(this.publish({...g,...c,id:h,title:u}),{...g,...c,id:h,dismissible:v,title:u}):g):this.addToast({title:u,...d,dismissible:v,id:h}),h},this.dismiss=c=>(c?(this.dismissedToasts.add(c),requestAnimationFrame(()=>this.subscribers.forEach(o=>o({id:c,dismiss:!0})))):this.toasts.forEach(o=>{this.subscribers.forEach(u=>u({id:o.id,dismiss:!0}))}),c),this.message=(c,o)=>this.create({...o,message:c}),this.error=(c,o)=>this.create({...o,message:c,type:"error"}),this.success=(c,o)=>this.create({...o,type:"success",message:c}),this.info=(c,o)=>this.create({...o,type:"info",message:c}),this.warning=(c,o)=>this.create({...o,type:"warning",message:c}),this.loading=(c,o)=>this.create({...o,type:"loading",message:c}),this.promise=(c,o)=>{if(!o)return;let u;o.loading!==void 0&&(u=this.create({...o,promise:c,type:"loading",message:o.loading,description:typeof o.description!="function"?o.description:void 0}));const d=Promise.resolve(c instanceof Function?c():c);let h=u!==void 0,y;const v=d.then(async m=>{if(y=["resolve",m],$.isValidElement(m))h=!1,this.create({id:u,type:"default",message:m});else if(wv(m)&&!m.ok){h=!1;const x=typeof o.error=="function"?await o.error(`HTTP error! status: ${m.status}`):o.error,j=typeof o.description=="function"?await o.description(`HTTP error! status: ${m.status}`):o.description,E=typeof x=="object"&&!$.isValidElement(x)?x:{message:x};this.create({id:u,type:"error",description:j,...E})}else if(m instanceof Error){h=!1;const x=typeof o.error=="function"?await o.error(m):o.error,j=typeof o.description=="function"?await o.description(m):o.description,E=typeof x=="object"&&!$.isValidElement(x)?x:{message:x};this.create({id:u,type:"error",description:j,...E})}else if(o.success!==void 0){h=!1;const x=typeof o.success=="function"?await o.success(m):o.success,j=typeof o.description=="function"?await o.description(m):o.description,E=typeof x=="object"&&!$.isValidElement(x)?x:{message:x};this.create({id:u,type:"success",description:j,...E})}}).catch(async m=>{if(y=["reject",m],o.error!==void 0){h=!1;const b=typeof o.error=="function"?await o.error(m):o.error,x=typeof o.description=="function"?await o.description(m):o.description,_=typeof b=="object"&&!$.isValidElement(b)?b:{message:b};this.create({id:u,type:"error",description:x,..._})}}).finally(()=>{h&&(this.dismiss(u),u=void 0),o.finally==null||o.finally.call(o)}),g=()=>new Promise((m,b)=>v.then(()=>y[0]==="reject"?b(y[1]):m(y[1])).catch(b));return typeof u!="string"&&typeof u!="number"?{unwrap:g}:Object.assign(u,{unwrap:g})},this.custom=(c,o)=>{const u=(o==null?void 0:o.id)||xu++;return this.create({jsx:c(u),id:u,...o}),u},this.getActiveToasts=()=>this.toasts.filter(c=>!this.dismissedToasts.has(c.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}const Ut=new Nv,jv=(i,c)=>{const o=(c==null?void 0:c.id)||xu++;return Ut.addToast({title:i,...c,id:o}),o},wv=i=>i&&typeof i=="object"&&"ok"in i&&typeof i.ok=="boolean"&&"status"in i&&typeof i.status=="number",Sv=jv,Ev=()=>Ut.toasts,Tv=()=>Ut.getActiveToasts(),Bt=Object.assign(Sv,{success:Ut.success,info:Ut.info,warning:Ut.warning,error:Ut.error,custom:Ut.custom,message:Ut.message,promise:Ut.promise,dismiss:Ut.dismiss,loading:Ut.loading},{getHistory:Ev,getToasts:Tv});dv("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");function wr(i){return i.label!==void 0}const Av=3,Rv="24px",Cv="16px",Hh=4e3,Mv=356,kv=14,_v=45,zv=200;function Sa(...i){return i.filter(Boolean).join(" ")}function Dv(i){const[c,o]=i.split("-"),u=[];return c&&u.push(c),o&&u.push(o),u}const Ov=i=>{var c,o,u,d,h,y,v,g,m;const{invert:b,toast:x,unstyled:j,interacting:_,setHeights:E,visibleToasts:L,heights:D,index:B,toasts:Q,expanded:O,removeToast:P,defaultRichColors:J,closeButton:ie,style:pe,cancelButtonStyle:K,actionButtonStyle:I,className:Ee="",descriptionClassName:Te="",duration:Ae,position:ot,gap:jt,expandByDefault:Re,classNames:R,icons:Z,closeButtonAriaLabel:V="Close toast"}=i,[fe,S]=$.useState(null),[U,X]=$.useState(null),[G,W]=$.useState(!1),[ue,F]=$.useState(!1),[Ne,Ce]=$.useState(!1),[lt,kt]=$.useState(!1),[pa,ia]=$.useState(!1),[Xs,wl]=$.useState(0),[Vr,ii]=$.useState(0),wt=$.useRef(x.duration||Ae||Hh),ri=$.useRef(null),Jt=$.useRef(null),ci=B===0,Zl=B+1<=L,Rt=x.type,Aa=x.dismissible!==!1,Xr=x.className||"",Qr=x.descriptionClassName||"",ga=$.useMemo(()=>D.findIndex(de=>de.toastId===x.id)||0,[D,x.id]),Ct=$.useMemo(()=>{var de;return(de=x.closeButton)!=null?de:ie},[x.closeButton,ie]),ra=$.useMemo(()=>x.duration||Ae||Hh,[x.duration,Ae]),pt=$.useRef(0),Ja=$.useRef(0),oi=$.useRef(0),Pa=$.useRef(null),[Kl,$l]=ot.split("-"),ya=$.useMemo(()=>D.reduce((de,$e,Ie)=>Ie>=ga?de:de+$e.height,0),[D,ga]),Sl=bv(),El=x.invert||b,Qs=Rt==="loading";Ja.current=$.useMemo(()=>ga*jt+ya,[ga,ya]),$.useEffect(()=>{wt.current=ra},[ra]),$.useEffect(()=>{W(!0)},[]),$.useEffect(()=>{const de=Jt.current;if(de){const $e=de.getBoundingClientRect().height;return ii($e),E(Ie=>[{toastId:x.id,height:$e,position:x.position},...Ie]),()=>E(Ie=>Ie.filter(gt=>gt.toastId!==x.id))}},[E,x.id]),$.useLayoutEffect(()=>{if(!G)return;const de=Jt.current,$e=de.style.height;de.style.height="auto";const Ie=de.getBoundingClientRect().height;de.style.height=$e,ii(Ie),E(gt=>gt.find(st=>st.toastId===x.id)?gt.map(st=>st.toastId===x.id?{...st,height:Ie}:st):[{toastId:x.id,height:Ie,position:x.position},...gt])},[G,x.title,x.description,E,x.id,x.jsx,x.action,x.cancel]);const ca=$.useCallback(()=>{F(!0),wl(Ja.current),E(de=>de.filter($e=>$e.toastId!==x.id)),setTimeout(()=>{P(x)},zv)},[x,P,E,Ja]);$.useEffect(()=>{if(x.promise&&Rt==="loading"||x.duration===1/0||x.type==="loading")return;let de;return O||_||Sl?(()=>{if(oi.current<pt.current){const gt=new Date().getTime()-pt.current;wt.current=wt.current-gt}oi.current=new Date().getTime()})():(()=>{wt.current!==1/0&&(pt.current=new Date().getTime(),de=setTimeout(()=>{x.onAutoClose==null||x.onAutoClose.call(x,x),ca()},wt.current))})(),()=>clearTimeout(de)},[O,_,x,Rt,Sl,ca]),$.useEffect(()=>{x.delete&&(ca(),x.onDismiss==null||x.onDismiss.call(x,x))},[ca,x.delete]);function ui(){var de;if(Z!=null&&Z.loading){var $e;return $.createElement("div",{className:Sa(R==null?void 0:R.loader,x==null||($e=x.classNames)==null?void 0:$e.loader,"sonner-loader"),"data-visible":Rt==="loading"},Z.loading)}return $.createElement(hv,{className:Sa(R==null?void 0:R.loader,x==null||(de=x.classNames)==null?void 0:de.loader),visible:Rt==="loading"})}const Zs=x.icon||(Z==null?void 0:Z[Rt])||fv(Rt);var Wa,di;return $.createElement("li",{tabIndex:0,ref:Jt,className:Sa(Ee,Xr,R==null?void 0:R.toast,x==null||(c=x.classNames)==null?void 0:c.toast,R==null?void 0:R.default,R==null?void 0:R[Rt],x==null||(o=x.classNames)==null?void 0:o[Rt]),"data-sonner-toast":"","data-rich-colors":(Wa=x.richColors)!=null?Wa:J,"data-styled":!(x.jsx||x.unstyled||j),"data-mounted":G,"data-promise":!!x.promise,"data-swiped":pa,"data-removed":ue,"data-visible":Zl,"data-y-position":Kl,"data-x-position":$l,"data-index":B,"data-front":ci,"data-swiping":Ne,"data-dismissible":Aa,"data-type":Rt,"data-invert":El,"data-swipe-out":lt,"data-swipe-direction":U,"data-expanded":!!(O||Re&&G),style:{"--index":B,"--toasts-before":B,"--z-index":Q.length-B,"--offset":`${ue?Xs:Ja.current}px`,"--initial-height":Re?"auto":`${Vr}px`,...pe,...x.style},onDragEnd:()=>{Ce(!1),S(null),Pa.current=null},onPointerDown:de=>{de.button!==2&&(Qs||!Aa||(ri.current=new Date,wl(Ja.current),de.target.setPointerCapture(de.pointerId),de.target.tagName!=="BUTTON"&&(Ce(!0),Pa.current={x:de.clientX,y:de.clientY})))},onPointerUp:()=>{var de,$e,Ie;if(lt||!Aa)return;Pa.current=null;const gt=Number(((de=Jt.current)==null?void 0:de.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),Tl=Number((($e=Jt.current)==null?void 0:$e.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),st=new Date().getTime()-((Ie=ri.current)==null?void 0:Ie.getTime()),Je=fe==="x"?gt:Tl,Pe=Math.abs(Je)/st;if(Math.abs(Je)>=_v||Pe>.11){wl(Ja.current),x.onDismiss==null||x.onDismiss.call(x,x),X(fe==="x"?gt>0?"right":"left":Tl>0?"down":"up"),ca(),kt(!0);return}else{var He,ut;(He=Jt.current)==null||He.style.setProperty("--swipe-amount-x","0px"),(ut=Jt.current)==null||ut.style.setProperty("--swipe-amount-y","0px")}ia(!1),Ce(!1),S(null)},onPointerMove:de=>{var $e,Ie,gt;if(!Pa.current||!Aa||(($e=window.getSelection())==null?void 0:$e.toString().length)>0)return;const st=de.clientY-Pa.current.y,Je=de.clientX-Pa.current.x;var Pe;const He=(Pe=i.swipeDirections)!=null?Pe:Dv(ot);!fe&&(Math.abs(Je)>1||Math.abs(st)>1)&&S(Math.abs(Je)>Math.abs(st)?"x":"y");let ut={x:0,y:0};const Jl=Ra=>1/(1.5+Math.abs(Ra)/20);if(fe==="y"){if(He.includes("top")||He.includes("bottom"))if(He.includes("top")&&st<0||He.includes("bottom")&&st>0)ut.y=st;else{const Ra=st*Jl(st);ut.y=Math.abs(Ra)<Math.abs(st)?Ra:st}}else if(fe==="x"&&(He.includes("left")||He.includes("right")))if(He.includes("left")&&Je<0||He.includes("right")&&Je>0)ut.x=Je;else{const Ra=Je*Jl(Je);ut.x=Math.abs(Ra)<Math.abs(Je)?Ra:Je}(Math.abs(ut.x)>0||Math.abs(ut.y)>0)&&ia(!0),(Ie=Jt.current)==null||Ie.style.setProperty("--swipe-amount-x",`${ut.x}px`),(gt=Jt.current)==null||gt.style.setProperty("--swipe-amount-y",`${ut.y}px`)}},Ct&&!x.jsx&&Rt!=="loading"?$.createElement("button",{"aria-label":V,"data-disabled":Qs,"data-close-button":!0,onClick:Qs||!Aa?()=>{}:()=>{ca(),x.onDismiss==null||x.onDismiss.call(x,x)},className:Sa(R==null?void 0:R.closeButton,x==null||(u=x.classNames)==null?void 0:u.closeButton)},(di=Z==null?void 0:Z.close)!=null?di:vv):null,(Rt||x.icon||x.promise)&&x.icon!==null&&((Z==null?void 0:Z[Rt])!==null||x.icon)?$.createElement("div",{"data-icon":"",className:Sa(R==null?void 0:R.icon,x==null||(d=x.classNames)==null?void 0:d.icon)},x.promise||x.type==="loading"&&!x.icon?x.icon||ui():null,x.type!=="loading"?Zs:null):null,$.createElement("div",{"data-content":"",className:Sa(R==null?void 0:R.content,x==null||(h=x.classNames)==null?void 0:h.content)},$.createElement("div",{"data-title":"",className:Sa(R==null?void 0:R.title,x==null||(y=x.classNames)==null?void 0:y.title)},x.jsx?x.jsx:typeof x.title=="function"?x.title():x.title),x.description?$.createElement("div",{"data-description":"",className:Sa(Te,Qr,R==null?void 0:R.description,x==null||(v=x.classNames)==null?void 0:v.description)},typeof x.description=="function"?x.description():x.description):null),$.isValidElement(x.cancel)?x.cancel:x.cancel&&wr(x.cancel)?$.createElement("button",{"data-button":!0,"data-cancel":!0,style:x.cancelButtonStyle||K,onClick:de=>{wr(x.cancel)&&Aa&&(x.cancel.onClick==null||x.cancel.onClick.call(x.cancel,de),ca())},className:Sa(R==null?void 0:R.cancelButton,x==null||(g=x.classNames)==null?void 0:g.cancelButton)},x.cancel.label):null,$.isValidElement(x.action)?x.action:x.action&&wr(x.action)?$.createElement("button",{"data-button":!0,"data-action":!0,style:x.actionButtonStyle||I,onClick:de=>{wr(x.action)&&(x.action.onClick==null||x.action.onClick.call(x.action,de),!de.defaultPrevented&&ca())},className:Sa(R==null?void 0:R.actionButton,x==null||(m=x.classNames)==null?void 0:m.actionButton)},x.action.label):null)};function qh(){if(typeof window>"u"||typeof document>"u")return"ltr";const i=document.documentElement.getAttribute("dir");return i==="auto"||!i?window.getComputedStyle(document.documentElement).direction:i}function Uv(i,c){const o={};return[i,c].forEach((u,d)=>{const h=d===1,y=h?"--mobile-offset":"--offset",v=h?Cv:Rv;function g(m){["top","right","bottom","left"].forEach(b=>{o[`${y}-${b}`]=typeof m=="number"?`${m}px`:m})}typeof u=="number"||typeof u=="string"?g(u):typeof u=="object"?["top","right","bottom","left"].forEach(m=>{u[m]===void 0?o[`${y}-${m}`]=v:o[`${y}-${m}`]=typeof u[m]=="number"?`${u[m]}px`:u[m]}):g(v)}),o}const Bv=$.forwardRef(function(c,o){const{invert:u,position:d="bottom-right",hotkey:h=["altKey","KeyT"],expand:y,closeButton:v,className:g,offset:m,mobileOffset:b,theme:x="light",richColors:j,duration:_,style:E,visibleToasts:L=Av,toastOptions:D,dir:B=qh(),gap:Q=kv,icons:O,containerAriaLabel:P="Notifications"}=c,[J,ie]=$.useState([]),pe=$.useMemo(()=>Array.from(new Set([d].concat(J.filter(U=>U.position).map(U=>U.position)))),[J,d]),[K,I]=$.useState([]),[Ee,Te]=$.useState(!1),[Ae,ot]=$.useState(!1),[jt,Re]=$.useState(x!=="system"?x:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),R=$.useRef(null),Z=h.join("+").replace(/Key/g,"").replace(/Digit/g,""),V=$.useRef(null),fe=$.useRef(!1),S=$.useCallback(U=>{ie(X=>{var G;return(G=X.find(W=>W.id===U.id))!=null&&G.delete||Ut.dismiss(U.id),X.filter(({id:W})=>W!==U.id)})},[]);return $.useEffect(()=>Ut.subscribe(U=>{if(U.dismiss){requestAnimationFrame(()=>{ie(X=>X.map(G=>G.id===U.id?{...G,delete:!0}:G))});return}setTimeout(()=>{sv.flushSync(()=>{ie(X=>{const G=X.findIndex(W=>W.id===U.id);return G!==-1?[...X.slice(0,G),{...X[G],...U},...X.slice(G+1)]:[U,...X]})})})}),[J]),$.useEffect(()=>{if(x!=="system"){Re(x);return}if(x==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?Re("dark"):Re("light")),typeof window>"u")return;const U=window.matchMedia("(prefers-color-scheme: dark)");try{U.addEventListener("change",({matches:X})=>{Re(X?"dark":"light")})}catch{U.addListener(({matches:G})=>{try{Re(G?"dark":"light")}catch(W){console.error(W)}})}},[x]),$.useEffect(()=>{J.length<=1&&Te(!1)},[J]),$.useEffect(()=>{const U=X=>{var G;if(h.every(F=>X[F]||X.code===F)){var ue;Te(!0),(ue=R.current)==null||ue.focus()}X.code==="Escape"&&(document.activeElement===R.current||(G=R.current)!=null&&G.contains(document.activeElement))&&Te(!1)};return document.addEventListener("keydown",U),()=>document.removeEventListener("keydown",U)},[h]),$.useEffect(()=>{if(R.current)return()=>{V.current&&(V.current.focus({preventScroll:!0}),V.current=null,fe.current=!1)}},[R.current]),$.createElement("section",{ref:o,"aria-label":`${P} ${Z}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},pe.map((U,X)=>{var G;const[W,ue]=U.split("-");return J.length?$.createElement("ol",{key:U,dir:B==="auto"?qh():B,tabIndex:-1,ref:R,className:g,"data-sonner-toaster":!0,"data-sonner-theme":jt,"data-y-position":W,"data-x-position":ue,style:{"--front-toast-height":`${((G=K[0])==null?void 0:G.height)||0}px`,"--width":`${Mv}px`,"--gap":`${Q}px`,...E,...Uv(m,b)},onBlur:F=>{fe.current&&!F.currentTarget.contains(F.relatedTarget)&&(fe.current=!1,V.current&&(V.current.focus({preventScroll:!0}),V.current=null))},onFocus:F=>{F.target instanceof HTMLElement&&F.target.dataset.dismissible==="false"||fe.current||(fe.current=!0,V.current=F.relatedTarget)},onMouseEnter:()=>Te(!0),onMouseMove:()=>Te(!0),onMouseLeave:()=>{Ae||Te(!1)},onDragEnd:()=>Te(!1),onPointerDown:F=>{F.target instanceof HTMLElement&&F.target.dataset.dismissible==="false"||ot(!0)},onPointerUp:()=>ot(!1)},J.filter(F=>!F.position&&X===0||F.position===U).map((F,Ne)=>{var Ce,lt;return $.createElement(Ov,{key:F.id,icons:O,index:Ne,toast:F,defaultRichColors:j,duration:(Ce=D==null?void 0:D.duration)!=null?Ce:_,className:D==null?void 0:D.className,descriptionClassName:D==null?void 0:D.descriptionClassName,invert:u,visibleToasts:L,closeButton:(lt=D==null?void 0:D.closeButton)!=null?lt:v,interacting:Ae,position:U,style:D==null?void 0:D.style,unstyled:D==null?void 0:D.unstyled,classNames:D==null?void 0:D.classNames,cancelButtonStyle:D==null?void 0:D.cancelButtonStyle,actionButtonStyle:D==null?void 0:D.actionButtonStyle,closeButtonAriaLabel:D==null?void 0:D.closeButtonAriaLabel,removeToast:S,toasts:J.filter(kt=>kt.position==F.position),heights:K.filter(kt=>kt.position==F.position),setHeights:I,expandByDefault:y,gap:Q,expanded:Ee,swipeDirections:c.swipeDirections})})):null}))}),Lv=({...i})=>{const{theme:c="system"}=uv();return s.jsx(Bv,{theme:c,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...i})},Sr="/api";class Hv{async login(c,o){const u=await fetch(`${Sr}/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:c,password:o})});if(!u.ok){const d=await u.json();throw new Error(d.message||"Login failed")}return u.json()}async register(c){const o=await fetch(`${Sr}/auth/register`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(c)});if(!o.ok){const u=await o.json();throw new Error(u.message||"Registration failed")}return o.json()}async getCurrentUser(){const c=localStorage.getItem("token");if(!c)throw new Error("No token found");const o=await fetch(`${Sr}/auth/me`,{headers:{Authorization:`Bearer ${c}`}});if(!o.ok)throw new Error("Failed to get user data");return o.json()}async refreshToken(){const c=localStorage.getItem("token");if(!c)throw new Error("No token found");const o=await fetch(`${Sr}/auth/refresh`,{method:"POST",headers:{Authorization:`Bearer ${c}`}});if(!o.ok)throw new Error("Token refresh failed");return o.json()}}const cu=new Hv,y0=N.createContext(),Lr=()=>{const i=N.useContext(y0);if(!i)throw new Error("useAuth must be used within an AuthProvider");return i},qv=({children:i})=>{const[c,o]=N.useState(null),[u,d]=N.useState(!0),[h,y]=N.useState(localStorage.getItem("token"));N.useEffect(()=>{(async()=>{if(h)try{const j=await cu.getCurrentUser();o(j)}catch(j){console.error("Auth initialization failed:",j),localStorage.removeItem("token"),y(null)}d(!1)})()},[h]);const b={user:c,token:h,loading:u,login:async(x,j)=>{try{const _=await cu.login(x,j);return y(_.token),o(_.user),localStorage.setItem("token",_.token),_}catch(_){throw _}},register:async x=>{try{const j=await cu.register(x);return y(j.token),o(j.user),localStorage.setItem("token",j.token),j}catch(j){throw j}},logout:()=>{o(null),y(null),localStorage.removeItem("token")},isAuthenticated:!!c};return s.jsx(y0.Provider,{value:b,children:i})};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gv=i=>i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Yv=i=>i.replace(/^([A-Z])|[\s-_]+(\w)/g,(c,o,u)=>u?u.toUpperCase():o.toLowerCase()),Gh=i=>{const c=Yv(i);return c.charAt(0).toUpperCase()+c.slice(1)},v0=(...i)=>i.filter((c,o,u)=>!!c&&c.trim()!==""&&u.indexOf(c)===o).join(" ").trim(),Vv=i=>{for(const c in i)if(c.startsWith("aria-")||c==="role"||c==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Xv={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qv=N.forwardRef(({color:i="currentColor",size:c=24,strokeWidth:o=2,absoluteStrokeWidth:u,className:d="",children:h,iconNode:y,...v},g)=>N.createElement("svg",{ref:g,...Xv,width:c,height:c,stroke:i,strokeWidth:u?Number(o)*24/Number(c):o,className:v0("lucide",d),...!h&&!Vv(v)&&{"aria-hidden":"true"},...v},[...y.map(([m,b])=>N.createElement(m,b)),...Array.isArray(h)?h:[h]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xe=(i,c)=>{const o=N.forwardRef(({className:u,...d},h)=>N.createElement(Qv,{ref:h,iconNode:c,className:v0(`lucide-${Gv(Gh(i))}`,`lucide-${i}`,u),...d}));return o.displayName=Gh(i),o};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zv=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],$n=xe("activity",Zv);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kv=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],b0=xe("arrow-right",Kv);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $v=[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]],Jv=xe("award",$v);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pv=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],_r=xe("bell",Pv);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wv=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],Kn=xe("book-open",Wv);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fv=[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]],Iv=xe("brain",Fv);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eb=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],tb=xe("calendar",eb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ab=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],lb=xe("chart-column",ab);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sb=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],At=xe("circle-check-big",sb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nb=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],Cu=xe("clock",nb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ib=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],Yh=xe("copy",ib);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rb=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]],Mu=xe("download",rb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cb=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],N0=xe("eye-off",cb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ob=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Nt=xe("eye",ob);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ub=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],db=xe("funnel",ub);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fb=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],Za=xe("globe",fb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mb=[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]],ou=xe("heart",mb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hb=[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]],Us=xe("key",hb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xb=[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]],pb=xe("layout-dashboard",xb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gb=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],j0=xe("loader-circle",gb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yb=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]],vb=xe("lock-open",yb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bb=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]],jl=xe("lock",bb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nb=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],jb=xe("log-out",Nb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wb=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],Nl=xe("mail",wb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sb=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Eb=xe("menu",Sb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tb=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],Ab=xe("play",Tb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rb=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],pu=xe("plus",Rb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cb=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],Vh=xe("refresh-cw",Cb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mb=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],Er=xe("save",Mb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kb=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],w0=xe("search",kb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _b=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],S0=xe("settings",_b);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zb=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],Ge=xe("shield",zb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Db=[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]],Ql=xe("smartphone",Db);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ob=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],Ub=xe("star",Ob);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bb=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]],E0=xe("target",Bb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lb=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Hb=xe("trash-2",Lb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qb=[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]],ei=xe("trending-up",qb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gb=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],na=xe("triangle-alert",Gb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yb=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],zr=xe("user",Yb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vb=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],ku=xe("users",Vb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xb=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Qb=xe("x",Xb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zb=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],Bs=xe("zap",Zb),Ds=({children:i})=>{const{isAuthenticated:c,loading:o}=Lr();return o?s.jsx("div",{className:"min-h-screen flex items-center justify-center",children:s.jsx(j0,{className:"h-8 w-8 animate-spin"})}):c?i:s.jsx(f0,{to:"/login",replace:!0})},Kb=()=>{const[i,c]=N.useState(!1),[o,u]=N.useState(0);N.useEffect(()=>{c(!0);const v=setInterval(()=>{u(g=>(g+1)%4)},3e3);return()=>clearInterval(v)},[]);const d=[{number:"50M+",label:"Breaches Monitored",icon:Ge},{number:"99.9%",label:"Uptime Guarantee",icon:ei},{number:"24/7",label:"Real-time Monitoring",icon:Nt},{number:"256-bit",label:"Encryption Standard",icon:jl}],h=[{icon:Nt,title:"Identity Monitoring",description:"Monitor your phone numbers and email addresses for breaches and unauthorized use across the dark web and breach databases.",gradient:"from-blue-500 to-cyan-500"},{icon:jl,title:"Password Security",description:"Analyze password strength, detect compromised passwords, and generate cryptographically secure passwords and passphrases.",gradient:"from-purple-500 to-pink-500"},{icon:Bs,title:"Real-time Alerts",description:"Get instant notifications via email and SMS when your identities are found in new breaches or suspicious activities.",gradient:"from-orange-500 to-red-500"},{icon:Iv,title:"Security Education",description:"Learn cybersecurity best practices with personalized tips, phishing protection guides, and threat awareness content.",gradient:"from-green-500 to-emerald-500"},{icon:Za,title:"Global Coverage",description:"Monitor identities worldwide with support for international phone numbers and comprehensive breach database coverage.",gradient:"from-indigo-500 to-purple-500"},{icon:E0,title:"Advanced Analytics",description:"Get detailed risk assessments, trend analysis, and actionable insights to improve your security posture.",gradient:"from-pink-500 to-rose-500"}],y=["Never reuse passwords across multiple accounts","Enable two-factor authentication on all important accounts","Be suspicious of urgent emails requesting personal information","Keep your software and devices updated with latest security patches","Use a password manager to generate and store strong passwords"];return s.jsxs("div",{className:"min-h-screen animated-bg",children:[s.jsx("nav",{className:"fixed top-0 w-full z-50 glass-card border-0 border-b border-white/20",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"flex justify-between items-center h-16",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center glow-effect",children:s.jsx(Ge,{className:"h-6 w-6 text-white"})}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-xl font-bold text-white",children:"Identity Guardian"}),s.jsx("span",{className:"text-xs bg-gradient-accent text-white px-2 py-1 rounded-full",children:"White Hat"})]})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx(Lt,{to:"/login",className:"nav-link",children:"Sign In"}),s.jsx(Lt,{to:"/register",className:"btn-primary",children:"Get Started"})]})]})})}),s.jsx("section",{className:"pt-32 pb-20 px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsx("div",{className:"text-center",children:s.jsxs("div",{className:`transition-all duration-1000 ${i?"slide-in":"opacity-0"}`,children:[s.jsx("h1",{className:"text-5xl md:text-7xl font-bold mb-6 heading-gradient",children:"Protect Your Digital Identity"}),s.jsx("p",{className:"text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed",children:"Advanced cybersecurity monitoring for your phone numbers, emails, and passwords. Get real-time alerts, security education, and comprehensive protection against identity theft and cyber threats."}),s.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[s.jsxs(Lt,{to:"/register",className:"btn-primary text-lg px-8 py-4 flex items-center space-x-2 glow-effect",children:[s.jsx("span",{children:"Start Free Monitoring"}),s.jsx(b0,{className:"h-5 w-5"})]}),s.jsxs("button",{className:"btn-secondary text-lg px-8 py-4 flex items-center space-x-2",children:[s.jsx("span",{children:"View Demo"}),s.jsx(Nt,{className:"h-5 w-5"})]})]})]})})})}),s.jsx("section",{className:"py-16 px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8",children:d.map((v,g)=>{const m=v.icon;return s.jsxs("div",{className:`glass-card p-6 text-center hover-lift ${o===g?"glow-effect scale-in":""}`,children:[s.jsx(m,{className:"h-8 w-8 mx-auto mb-4 text-blue-400"}),s.jsx("div",{className:"text-3xl font-bold text-white mb-2",children:v.number}),s.jsx("div",{className:"text-gray-300 text-sm",children:v.label})]},g)})})})}),s.jsx("section",{className:"py-20 px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"max-w-7xl mx-auto",children:[s.jsxs("div",{className:"text-center mb-16",children:[s.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Comprehensive Cybersecurity Protection"}),s.jsx("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto",children:"Identity Guardian provides cutting-edge security monitoring and education to keep you safe from cyber threats and identity theft."})]}),s.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:h.map((v,g)=>{const m=v.icon;return s.jsxs("div",{className:"glass-card p-8 hover-lift fade-in group",children:[s.jsx("div",{className:`w-16 h-16 bg-gradient-to-r ${v.gradient} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`,children:s.jsx(m,{className:"h-8 w-8 text-white"})}),s.jsx("h3",{className:"text-2xl font-bold text-white mb-4",children:v.title}),s.jsx("p",{className:"text-gray-300 leading-relaxed",children:v.description})]},g)})})]})}),s.jsx("section",{className:"py-20 px-4 sm:px-6 lg:px-8 bg-black/20",children:s.jsxs("div",{className:"max-w-4xl mx-auto",children:[s.jsxs("div",{className:"text-center mb-12",children:[s.jsx("h2",{className:"text-4xl font-bold text-white mb-6",children:"Essential Security Tips"}),s.jsx("p",{className:"text-xl text-gray-300",children:"Follow these cybersecurity best practices to stay protected online"})]}),s.jsx("div",{className:"space-y-4",children:y.map((v,g)=>s.jsxs("div",{className:"glass-card p-6 flex items-start space-x-4 hover-lift",children:[s.jsx(At,{className:"h-6 w-6 text-green-400 mt-1 flex-shrink-0"}),s.jsx("p",{className:"text-gray-300 text-lg",children:v})]},g))})]})}),s.jsx("section",{className:"py-20 px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"max-w-4xl mx-auto text-center",children:s.jsxs("div",{className:"glass-card p-12 glow-effect",children:[s.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Ready to Secure Your Digital Identity?"}),s.jsx("p",{className:"text-xl text-gray-300 mb-8",children:"Join thousands of users who trust Identity Guardian to protect their digital lives. Start monitoring your identities today."}),s.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[s.jsxs(Lt,{to:"/register",className:"btn-primary text-lg px-8 py-4 flex items-center justify-center space-x-2 glow-effect",children:[s.jsx(Ge,{className:"h-5 w-5"}),s.jsx("span",{children:"Start Free Protection"})]}),s.jsxs(Lt,{to:"/login",className:"btn-secondary text-lg px-8 py-4 flex items-center justify-center space-x-2",children:[s.jsx(ku,{className:"h-5 w-5"}),s.jsx("span",{children:"Sign In to Dashboard"})]})]})]})})}),s.jsx("footer",{className:"py-12 px-4 sm:px-6 lg:px-8 border-t border-white/10",children:s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[s.jsxs("div",{className:"flex items-center space-x-3 mb-4 md:mb-0",children:[s.jsx("div",{className:"w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center",children:s.jsx(Ge,{className:"h-5 w-5 text-white"})}),s.jsx("span",{className:"text-white font-semibold",children:"Identity Guardian"})]}),s.jsx("div",{className:"text-gray-400 text-sm",children:"Protected by 256-bit encryption and advanced security measures"})]})})})]})},$b=()=>{const[i,c]=N.useState(""),[o,u]=N.useState(""),[d,h]=N.useState(!1),[y,v]=N.useState(!1),[g,m]=N.useState(""),{login:b}=Lr(),x=Fn(),j=async E=>{E.preventDefault(),v(!0),m("");try{await new Promise(L=>setTimeout(L,1e3)),i==="<EMAIL>"&&o==="SecureDemo123!"?(b({email:i,name:"Demo User"}),x("/dashboard")):m("Invalid credentials. Use <EMAIL> / SecureDemo123!")}catch{m("Login failed. Please try again.")}finally{v(!1)}},_=[{icon:Ge,title:"Advanced Protection",description:"256-bit encryption and multi-layer security"},{icon:Bs,title:"Real-time Monitoring",description:"Instant alerts for any suspicious activity"},{icon:Za,title:"Global Coverage",description:"Worldwide breach database monitoring"}];return s.jsxs("div",{className:"min-h-screen animated-bg flex",children:[s.jsx("div",{className:"flex-1 flex items-center justify-center p-8",children:s.jsxs("div",{className:"w-full max-w-md",children:[s.jsxs("div",{className:"text-center mb-8 slide-in",children:[s.jsxs(Lt,{to:"/",className:"inline-flex items-center space-x-3 mb-6",children:[s.jsx("div",{className:"w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center glow-effect",children:s.jsx(Ge,{className:"h-7 w-7 text-white"})}),s.jsx("span",{className:"text-2xl font-bold text-white",children:"Identity Guardian"})]}),s.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Secure your digital identity"}),s.jsx("p",{className:"text-gray-300",children:"Welcome Back"}),s.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"Sign in to your Identity Guardian account"})]}),s.jsxs("div",{className:"glass-card p-4 mb-6 border border-blue-500/30",children:[s.jsxs("h3",{className:"text-white font-semibold mb-2 flex items-center space-x-2",children:[s.jsx(At,{className:"h-4 w-4 text-green-400"}),s.jsx("span",{children:"Demo Credentials:"})]}),s.jsxs("div",{className:"space-y-1 text-sm",children:[s.jsxs("p",{className:"text-gray-300",children:[s.jsx("span",{className:"text-blue-400",children:"Email:"})," <EMAIL>"]}),s.jsxs("p",{className:"text-gray-300",children:[s.jsx("span",{className:"text-blue-400",children:"Password:"})," SecureDemo123!"]})]}),s.jsx("p",{className:"text-xs text-gray-400 mt-2",children:"Protected by 256-bit encryption and advanced security measures"})]}),s.jsxs("div",{className:"glass-card p-8 scale-in",children:[s.jsxs("form",{onSubmit:j,className:"space-y-6",children:[g&&s.jsx("div",{className:"p-4 rounded-lg bg-red-500/10 border border-red-500/30 text-red-300 text-sm",children:g}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300 mb-2",children:"Email"}),s.jsxs("div",{className:"relative",children:[s.jsx(Nl,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),s.jsx("input",{id:"email",type:"email",value:i,onChange:E=>c(E.target.value),className:"input-field w-full pl-10",placeholder:"Enter your email",required:!0})]})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-300 mb-2",children:"Password"}),s.jsxs("div",{className:"relative",children:[s.jsx(jl,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),s.jsx("input",{id:"password",type:d?"text":"password",value:o,onChange:E=>u(E.target.value),className:"input-field w-full pl-10 pr-10",placeholder:"Enter your password",required:!0}),s.jsx("button",{type:"button",onClick:()=>h(!d),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors",children:d?s.jsx(N0,{className:"h-5 w-5"}):s.jsx(Nt,{className:"h-5 w-5"})})]})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("label",{className:"flex items-center",children:[s.jsx("input",{type:"checkbox",className:"rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-500"}),s.jsx("span",{className:"ml-2 text-sm text-gray-300",children:"Remember me"})]}),s.jsx(Lt,{to:"/forgot-password",className:"text-sm text-blue-400 hover:text-blue-300 transition-colors",children:"Forgot password?"})]}),s.jsx("button",{type:"submit",disabled:y,className:"btn-primary w-full flex items-center justify-center space-x-2 py-3 text-lg glow-effect",children:y?s.jsx("div",{className:"loading-spinner w-5 h-5"}):s.jsxs(s.Fragment,{children:[s.jsx("span",{children:"Sign In"}),s.jsx(b0,{className:"h-5 w-5"})]})})]}),s.jsx("div",{className:"mt-6 text-center",children:s.jsxs("p",{className:"text-gray-400",children:["Don't have an account?"," ",s.jsx(Lt,{to:"/register",className:"text-blue-400 hover:text-blue-300 transition-colors font-medium",children:"Create one here"})]})})]})]})}),s.jsx("div",{className:"hidden lg:flex flex-1 items-center justify-center p-8",children:s.jsxs("div",{className:"max-w-lg",children:[s.jsxs("div",{className:"text-center mb-12 fade-in",children:[s.jsx("h2",{className:"text-4xl font-bold text-white mb-4 heading-gradient",children:"Comprehensive Cybersecurity Protection"}),s.jsx("p",{className:"text-xl text-gray-300 leading-relaxed",children:"Advanced monitoring and protection for your digital identity, passwords, and personal information."})]}),s.jsx("div",{className:"space-y-8",children:_.map((E,L)=>{const D=E.icon;return s.jsx("div",{className:"glass-card p-6 hover-lift fade-in",style:{animationDelay:`${L*.2}s`},children:s.jsxs("div",{className:"flex items-start space-x-4",children:[s.jsx("div",{className:"w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center flex-shrink-0",children:s.jsx(D,{className:"h-6 w-6 text-white"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:E.title}),s.jsx("p",{className:"text-gray-300 leading-relaxed",children:E.description})]})]})},L)})}),s.jsxs("div",{className:"mt-12 grid grid-cols-3 gap-6 text-center",children:[s.jsxs("div",{className:"glass-card p-4",children:[s.jsx("div",{className:"text-2xl font-bold text-white mb-1",children:"99.9%"}),s.jsx("div",{className:"text-sm text-gray-400",children:"Uptime"})]}),s.jsxs("div",{className:"glass-card p-4",children:[s.jsx("div",{className:"text-2xl font-bold text-white mb-1",children:"50M+"}),s.jsx("div",{className:"text-sm text-gray-400",children:"Protected"})]}),s.jsxs("div",{className:"glass-card p-4",children:[s.jsx("div",{className:"text-2xl font-bold text-white mb-1",children:"24/7"}),s.jsx("div",{className:"text-sm text-gray-400",children:"Monitoring"})]})]})]})})]})};function T0(i){var c,o,u="";if(typeof i=="string"||typeof i=="number")u+=i;else if(typeof i=="object")if(Array.isArray(i)){var d=i.length;for(c=0;c<d;c++)i[c]&&(o=T0(i[c]))&&(u&&(u+=" "),u+=o)}else for(o in i)i[o]&&(u&&(u+=" "),u+=o);return u}function A0(){for(var i,c,o=0,u="",d=arguments.length;o<d;o++)(i=arguments[o])&&(c=T0(i))&&(u&&(u+=" "),u+=c);return u}const _u="-",Jb=i=>{const c=Wb(i),{conflictingClassGroups:o,conflictingClassGroupModifiers:u}=i;return{getClassGroupId:y=>{const v=y.split(_u);return v[0]===""&&v.length!==1&&v.shift(),R0(v,c)||Pb(y)},getConflictingClassGroupIds:(y,v)=>{const g=o[y]||[];return v&&u[y]?[...g,...u[y]]:g}}},R0=(i,c)=>{var y;if(i.length===0)return c.classGroupId;const o=i[0],u=c.nextPart.get(o),d=u?R0(i.slice(1),u):void 0;if(d)return d;if(c.validators.length===0)return;const h=i.join(_u);return(y=c.validators.find(({validator:v})=>v(h)))==null?void 0:y.classGroupId},Xh=/^\[(.+)\]$/,Pb=i=>{if(Xh.test(i)){const c=Xh.exec(i)[1],o=c==null?void 0:c.substring(0,c.indexOf(":"));if(o)return"arbitrary.."+o}},Wb=i=>{const{theme:c,classGroups:o}=i,u={nextPart:new Map,validators:[]};for(const d in o)gu(o[d],u,d,c);return u},gu=(i,c,o,u)=>{i.forEach(d=>{if(typeof d=="string"){const h=d===""?c:Qh(c,d);h.classGroupId=o;return}if(typeof d=="function"){if(Fb(d)){gu(d(u),c,o,u);return}c.validators.push({validator:d,classGroupId:o});return}Object.entries(d).forEach(([h,y])=>{gu(y,Qh(c,h),o,u)})})},Qh=(i,c)=>{let o=i;return c.split(_u).forEach(u=>{o.nextPart.has(u)||o.nextPart.set(u,{nextPart:new Map,validators:[]}),o=o.nextPart.get(u)}),o},Fb=i=>i.isThemeGetter,Ib=i=>{if(i<1)return{get:()=>{},set:()=>{}};let c=0,o=new Map,u=new Map;const d=(h,y)=>{o.set(h,y),c++,c>i&&(c=0,u=o,o=new Map)};return{get(h){let y=o.get(h);if(y!==void 0)return y;if((y=u.get(h))!==void 0)return d(h,y),y},set(h,y){o.has(h)?o.set(h,y):d(h,y)}}},yu="!",vu=":",e1=vu.length,t1=i=>{const{prefix:c,experimentalParseClassName:o}=i;let u=d=>{const h=[];let y=0,v=0,g=0,m;for(let E=0;E<d.length;E++){let L=d[E];if(y===0&&v===0){if(L===vu){h.push(d.slice(g,E)),g=E+e1;continue}if(L==="/"){m=E;continue}}L==="["?y++:L==="]"?y--:L==="("?v++:L===")"&&v--}const b=h.length===0?d:d.substring(g),x=a1(b),j=x!==b,_=m&&m>g?m-g:void 0;return{modifiers:h,hasImportantModifier:j,baseClassName:x,maybePostfixModifierPosition:_}};if(c){const d=c+vu,h=u;u=y=>y.startsWith(d)?h(y.substring(d.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:y,maybePostfixModifierPosition:void 0}}if(o){const d=u;u=h=>o({className:h,parseClassName:d})}return u},a1=i=>i.endsWith(yu)?i.substring(0,i.length-1):i.startsWith(yu)?i.substring(1):i,l1=i=>{const c=Object.fromEntries(i.orderSensitiveModifiers.map(u=>[u,!0]));return u=>{if(u.length<=1)return u;const d=[];let h=[];return u.forEach(y=>{y[0]==="["||c[y]?(d.push(...h.sort(),y),h=[]):h.push(y)}),d.push(...h.sort()),d}},s1=i=>({cache:Ib(i.cacheSize),parseClassName:t1(i),sortModifiers:l1(i),...Jb(i)}),n1=/\s+/,i1=(i,c)=>{const{parseClassName:o,getClassGroupId:u,getConflictingClassGroupIds:d,sortModifiers:h}=c,y=[],v=i.trim().split(n1);let g="";for(let m=v.length-1;m>=0;m-=1){const b=v[m],{isExternal:x,modifiers:j,hasImportantModifier:_,baseClassName:E,maybePostfixModifierPosition:L}=o(b);if(x){g=b+(g.length>0?" "+g:g);continue}let D=!!L,B=u(D?E.substring(0,L):E);if(!B){if(!D){g=b+(g.length>0?" "+g:g);continue}if(B=u(E),!B){g=b+(g.length>0?" "+g:g);continue}D=!1}const Q=h(j).join(":"),O=_?Q+yu:Q,P=O+B;if(y.includes(P))continue;y.push(P);const J=d(B,D);for(let ie=0;ie<J.length;++ie){const pe=J[ie];y.push(O+pe)}g=b+(g.length>0?" "+g:g)}return g};function r1(){let i=0,c,o,u="";for(;i<arguments.length;)(c=arguments[i++])&&(o=C0(c))&&(u&&(u+=" "),u+=o);return u}const C0=i=>{if(typeof i=="string")return i;let c,o="";for(let u=0;u<i.length;u++)i[u]&&(c=C0(i[u]))&&(o&&(o+=" "),o+=c);return o};function c1(i,...c){let o,u,d,h=y;function y(g){const m=c.reduce((b,x)=>x(b),i());return o=s1(m),u=o.cache.get,d=o.cache.set,h=v,v(g)}function v(g){const m=u(g);if(m)return m;const b=i1(g,o);return d(g,b),b}return function(){return h(r1.apply(null,arguments))}}const rt=i=>{const c=o=>o[i]||[];return c.isThemeGetter=!0,c},M0=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k0=/^\((?:(\w[\w-]*):)?(.+)\)$/i,o1=/^\d+\/\d+$/,u1=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,d1=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,f1=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,m1=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,h1=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Os=i=>o1.test(i),be=i=>!!i&&!Number.isNaN(Number(i)),bl=i=>!!i&&Number.isInteger(Number(i)),uu=i=>i.endsWith("%")&&be(i.slice(0,-1)),Xa=i=>u1.test(i),x1=()=>!0,p1=i=>d1.test(i)&&!f1.test(i),_0=()=>!1,g1=i=>m1.test(i),y1=i=>h1.test(i),v1=i=>!te(i)&&!ae(i),b1=i=>Ys(i,O0,_0),te=i=>M0.test(i),Xl=i=>Ys(i,U0,p1),du=i=>Ys(i,E1,be),Zh=i=>Ys(i,z0,_0),N1=i=>Ys(i,D0,y1),Tr=i=>Ys(i,B0,g1),ae=i=>k0.test(i),Zn=i=>Vs(i,U0),j1=i=>Vs(i,T1),Kh=i=>Vs(i,z0),w1=i=>Vs(i,O0),S1=i=>Vs(i,D0),Ar=i=>Vs(i,B0,!0),Ys=(i,c,o)=>{const u=M0.exec(i);return u?u[1]?c(u[1]):o(u[2]):!1},Vs=(i,c,o=!1)=>{const u=k0.exec(i);return u?u[1]?c(u[1]):o:!1},z0=i=>i==="position"||i==="percentage",D0=i=>i==="image"||i==="url",O0=i=>i==="length"||i==="size"||i==="bg-size",U0=i=>i==="length",E1=i=>i==="number",T1=i=>i==="family-name",B0=i=>i==="shadow",A1=()=>{const i=rt("color"),c=rt("font"),o=rt("text"),u=rt("font-weight"),d=rt("tracking"),h=rt("leading"),y=rt("breakpoint"),v=rt("container"),g=rt("spacing"),m=rt("radius"),b=rt("shadow"),x=rt("inset-shadow"),j=rt("text-shadow"),_=rt("drop-shadow"),E=rt("blur"),L=rt("perspective"),D=rt("aspect"),B=rt("ease"),Q=rt("animate"),O=()=>["auto","avoid","all","avoid-page","page","left","right","column"],P=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],J=()=>[...P(),ae,te],ie=()=>["auto","hidden","clip","visible","scroll"],pe=()=>["auto","contain","none"],K=()=>[ae,te,g],I=()=>[Os,"full","auto",...K()],Ee=()=>[bl,"none","subgrid",ae,te],Te=()=>["auto",{span:["full",bl,ae,te]},bl,ae,te],Ae=()=>[bl,"auto",ae,te],ot=()=>["auto","min","max","fr",ae,te],jt=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Re=()=>["start","end","center","stretch","center-safe","end-safe"],R=()=>["auto",...K()],Z=()=>[Os,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...K()],V=()=>[i,ae,te],fe=()=>[...P(),Kh,Zh,{position:[ae,te]}],S=()=>["no-repeat",{repeat:["","x","y","space","round"]}],U=()=>["auto","cover","contain",w1,b1,{size:[ae,te]}],X=()=>[uu,Zn,Xl],G=()=>["","none","full",m,ae,te],W=()=>["",be,Zn,Xl],ue=()=>["solid","dashed","dotted","double"],F=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Ne=()=>[be,uu,Kh,Zh],Ce=()=>["","none",E,ae,te],lt=()=>["none",be,ae,te],kt=()=>["none",be,ae,te],pa=()=>[be,ae,te],ia=()=>[Os,"full",...K()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Xa],breakpoint:[Xa],color:[x1],container:[Xa],"drop-shadow":[Xa],ease:["in","out","in-out"],font:[v1],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Xa],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Xa],shadow:[Xa],spacing:["px",be],text:[Xa],"text-shadow":[Xa],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Os,te,ae,D]}],container:["container"],columns:[{columns:[be,te,ae,v]}],"break-after":[{"break-after":O()}],"break-before":[{"break-before":O()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:J()}],overflow:[{overflow:ie()}],"overflow-x":[{"overflow-x":ie()}],"overflow-y":[{"overflow-y":ie()}],overscroll:[{overscroll:pe()}],"overscroll-x":[{"overscroll-x":pe()}],"overscroll-y":[{"overscroll-y":pe()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:I()}],"inset-x":[{"inset-x":I()}],"inset-y":[{"inset-y":I()}],start:[{start:I()}],end:[{end:I()}],top:[{top:I()}],right:[{right:I()}],bottom:[{bottom:I()}],left:[{left:I()}],visibility:["visible","invisible","collapse"],z:[{z:[bl,"auto",ae,te]}],basis:[{basis:[Os,"full","auto",v,...K()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[be,Os,"auto","initial","none",te]}],grow:[{grow:["",be,ae,te]}],shrink:[{shrink:["",be,ae,te]}],order:[{order:[bl,"first","last","none",ae,te]}],"grid-cols":[{"grid-cols":Ee()}],"col-start-end":[{col:Te()}],"col-start":[{"col-start":Ae()}],"col-end":[{"col-end":Ae()}],"grid-rows":[{"grid-rows":Ee()}],"row-start-end":[{row:Te()}],"row-start":[{"row-start":Ae()}],"row-end":[{"row-end":Ae()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ot()}],"auto-rows":[{"auto-rows":ot()}],gap:[{gap:K()}],"gap-x":[{"gap-x":K()}],"gap-y":[{"gap-y":K()}],"justify-content":[{justify:[...jt(),"normal"]}],"justify-items":[{"justify-items":[...Re(),"normal"]}],"justify-self":[{"justify-self":["auto",...Re()]}],"align-content":[{content:["normal",...jt()]}],"align-items":[{items:[...Re(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Re(),{baseline:["","last"]}]}],"place-content":[{"place-content":jt()}],"place-items":[{"place-items":[...Re(),"baseline"]}],"place-self":[{"place-self":["auto",...Re()]}],p:[{p:K()}],px:[{px:K()}],py:[{py:K()}],ps:[{ps:K()}],pe:[{pe:K()}],pt:[{pt:K()}],pr:[{pr:K()}],pb:[{pb:K()}],pl:[{pl:K()}],m:[{m:R()}],mx:[{mx:R()}],my:[{my:R()}],ms:[{ms:R()}],me:[{me:R()}],mt:[{mt:R()}],mr:[{mr:R()}],mb:[{mb:R()}],ml:[{ml:R()}],"space-x":[{"space-x":K()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":K()}],"space-y-reverse":["space-y-reverse"],size:[{size:Z()}],w:[{w:[v,"screen",...Z()]}],"min-w":[{"min-w":[v,"screen","none",...Z()]}],"max-w":[{"max-w":[v,"screen","none","prose",{screen:[y]},...Z()]}],h:[{h:["screen","lh",...Z()]}],"min-h":[{"min-h":["screen","lh","none",...Z()]}],"max-h":[{"max-h":["screen","lh",...Z()]}],"font-size":[{text:["base",o,Zn,Xl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[u,ae,du]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",uu,te]}],"font-family":[{font:[j1,te,c]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[d,ae,te]}],"line-clamp":[{"line-clamp":[be,"none",ae,du]}],leading:[{leading:[h,...K()]}],"list-image":[{"list-image":["none",ae,te]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ae,te]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:V()}],"text-color":[{text:V()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ue(),"wavy"]}],"text-decoration-thickness":[{decoration:[be,"from-font","auto",ae,Xl]}],"text-decoration-color":[{decoration:V()}],"underline-offset":[{"underline-offset":[be,"auto",ae,te]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:K()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ae,te]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ae,te]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:fe()}],"bg-repeat":[{bg:S()}],"bg-size":[{bg:U()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},bl,ae,te],radial:["",ae,te],conic:[bl,ae,te]},S1,N1]}],"bg-color":[{bg:V()}],"gradient-from-pos":[{from:X()}],"gradient-via-pos":[{via:X()}],"gradient-to-pos":[{to:X()}],"gradient-from":[{from:V()}],"gradient-via":[{via:V()}],"gradient-to":[{to:V()}],rounded:[{rounded:G()}],"rounded-s":[{"rounded-s":G()}],"rounded-e":[{"rounded-e":G()}],"rounded-t":[{"rounded-t":G()}],"rounded-r":[{"rounded-r":G()}],"rounded-b":[{"rounded-b":G()}],"rounded-l":[{"rounded-l":G()}],"rounded-ss":[{"rounded-ss":G()}],"rounded-se":[{"rounded-se":G()}],"rounded-ee":[{"rounded-ee":G()}],"rounded-es":[{"rounded-es":G()}],"rounded-tl":[{"rounded-tl":G()}],"rounded-tr":[{"rounded-tr":G()}],"rounded-br":[{"rounded-br":G()}],"rounded-bl":[{"rounded-bl":G()}],"border-w":[{border:W()}],"border-w-x":[{"border-x":W()}],"border-w-y":[{"border-y":W()}],"border-w-s":[{"border-s":W()}],"border-w-e":[{"border-e":W()}],"border-w-t":[{"border-t":W()}],"border-w-r":[{"border-r":W()}],"border-w-b":[{"border-b":W()}],"border-w-l":[{"border-l":W()}],"divide-x":[{"divide-x":W()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":W()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ue(),"hidden","none"]}],"divide-style":[{divide:[...ue(),"hidden","none"]}],"border-color":[{border:V()}],"border-color-x":[{"border-x":V()}],"border-color-y":[{"border-y":V()}],"border-color-s":[{"border-s":V()}],"border-color-e":[{"border-e":V()}],"border-color-t":[{"border-t":V()}],"border-color-r":[{"border-r":V()}],"border-color-b":[{"border-b":V()}],"border-color-l":[{"border-l":V()}],"divide-color":[{divide:V()}],"outline-style":[{outline:[...ue(),"none","hidden"]}],"outline-offset":[{"outline-offset":[be,ae,te]}],"outline-w":[{outline:["",be,Zn,Xl]}],"outline-color":[{outline:V()}],shadow:[{shadow:["","none",b,Ar,Tr]}],"shadow-color":[{shadow:V()}],"inset-shadow":[{"inset-shadow":["none",x,Ar,Tr]}],"inset-shadow-color":[{"inset-shadow":V()}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:V()}],"ring-offset-w":[{"ring-offset":[be,Xl]}],"ring-offset-color":[{"ring-offset":V()}],"inset-ring-w":[{"inset-ring":W()}],"inset-ring-color":[{"inset-ring":V()}],"text-shadow":[{"text-shadow":["none",j,Ar,Tr]}],"text-shadow-color":[{"text-shadow":V()}],opacity:[{opacity:[be,ae,te]}],"mix-blend":[{"mix-blend":[...F(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":F()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[be]}],"mask-image-linear-from-pos":[{"mask-linear-from":Ne()}],"mask-image-linear-to-pos":[{"mask-linear-to":Ne()}],"mask-image-linear-from-color":[{"mask-linear-from":V()}],"mask-image-linear-to-color":[{"mask-linear-to":V()}],"mask-image-t-from-pos":[{"mask-t-from":Ne()}],"mask-image-t-to-pos":[{"mask-t-to":Ne()}],"mask-image-t-from-color":[{"mask-t-from":V()}],"mask-image-t-to-color":[{"mask-t-to":V()}],"mask-image-r-from-pos":[{"mask-r-from":Ne()}],"mask-image-r-to-pos":[{"mask-r-to":Ne()}],"mask-image-r-from-color":[{"mask-r-from":V()}],"mask-image-r-to-color":[{"mask-r-to":V()}],"mask-image-b-from-pos":[{"mask-b-from":Ne()}],"mask-image-b-to-pos":[{"mask-b-to":Ne()}],"mask-image-b-from-color":[{"mask-b-from":V()}],"mask-image-b-to-color":[{"mask-b-to":V()}],"mask-image-l-from-pos":[{"mask-l-from":Ne()}],"mask-image-l-to-pos":[{"mask-l-to":Ne()}],"mask-image-l-from-color":[{"mask-l-from":V()}],"mask-image-l-to-color":[{"mask-l-to":V()}],"mask-image-x-from-pos":[{"mask-x-from":Ne()}],"mask-image-x-to-pos":[{"mask-x-to":Ne()}],"mask-image-x-from-color":[{"mask-x-from":V()}],"mask-image-x-to-color":[{"mask-x-to":V()}],"mask-image-y-from-pos":[{"mask-y-from":Ne()}],"mask-image-y-to-pos":[{"mask-y-to":Ne()}],"mask-image-y-from-color":[{"mask-y-from":V()}],"mask-image-y-to-color":[{"mask-y-to":V()}],"mask-image-radial":[{"mask-radial":[ae,te]}],"mask-image-radial-from-pos":[{"mask-radial-from":Ne()}],"mask-image-radial-to-pos":[{"mask-radial-to":Ne()}],"mask-image-radial-from-color":[{"mask-radial-from":V()}],"mask-image-radial-to-color":[{"mask-radial-to":V()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":P()}],"mask-image-conic-pos":[{"mask-conic":[be]}],"mask-image-conic-from-pos":[{"mask-conic-from":Ne()}],"mask-image-conic-to-pos":[{"mask-conic-to":Ne()}],"mask-image-conic-from-color":[{"mask-conic-from":V()}],"mask-image-conic-to-color":[{"mask-conic-to":V()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:fe()}],"mask-repeat":[{mask:S()}],"mask-size":[{mask:U()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ae,te]}],filter:[{filter:["","none",ae,te]}],blur:[{blur:Ce()}],brightness:[{brightness:[be,ae,te]}],contrast:[{contrast:[be,ae,te]}],"drop-shadow":[{"drop-shadow":["","none",_,Ar,Tr]}],"drop-shadow-color":[{"drop-shadow":V()}],grayscale:[{grayscale:["",be,ae,te]}],"hue-rotate":[{"hue-rotate":[be,ae,te]}],invert:[{invert:["",be,ae,te]}],saturate:[{saturate:[be,ae,te]}],sepia:[{sepia:["",be,ae,te]}],"backdrop-filter":[{"backdrop-filter":["","none",ae,te]}],"backdrop-blur":[{"backdrop-blur":Ce()}],"backdrop-brightness":[{"backdrop-brightness":[be,ae,te]}],"backdrop-contrast":[{"backdrop-contrast":[be,ae,te]}],"backdrop-grayscale":[{"backdrop-grayscale":["",be,ae,te]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[be,ae,te]}],"backdrop-invert":[{"backdrop-invert":["",be,ae,te]}],"backdrop-opacity":[{"backdrop-opacity":[be,ae,te]}],"backdrop-saturate":[{"backdrop-saturate":[be,ae,te]}],"backdrop-sepia":[{"backdrop-sepia":["",be,ae,te]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":K()}],"border-spacing-x":[{"border-spacing-x":K()}],"border-spacing-y":[{"border-spacing-y":K()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ae,te]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[be,"initial",ae,te]}],ease:[{ease:["linear","initial",B,ae,te]}],delay:[{delay:[be,ae,te]}],animate:[{animate:["none",Q,ae,te]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[L,ae,te]}],"perspective-origin":[{"perspective-origin":J()}],rotate:[{rotate:lt()}],"rotate-x":[{"rotate-x":lt()}],"rotate-y":[{"rotate-y":lt()}],"rotate-z":[{"rotate-z":lt()}],scale:[{scale:kt()}],"scale-x":[{"scale-x":kt()}],"scale-y":[{"scale-y":kt()}],"scale-z":[{"scale-z":kt()}],"scale-3d":["scale-3d"],skew:[{skew:pa()}],"skew-x":[{"skew-x":pa()}],"skew-y":[{"skew-y":pa()}],transform:[{transform:[ae,te,"","none","gpu","cpu"]}],"transform-origin":[{origin:J()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ia()}],"translate-x":[{"translate-x":ia()}],"translate-y":[{"translate-y":ia()}],"translate-z":[{"translate-z":ia()}],"translate-none":["translate-none"],accent:[{accent:V()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:V()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ae,te]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":K()}],"scroll-mx":[{"scroll-mx":K()}],"scroll-my":[{"scroll-my":K()}],"scroll-ms":[{"scroll-ms":K()}],"scroll-me":[{"scroll-me":K()}],"scroll-mt":[{"scroll-mt":K()}],"scroll-mr":[{"scroll-mr":K()}],"scroll-mb":[{"scroll-mb":K()}],"scroll-ml":[{"scroll-ml":K()}],"scroll-p":[{"scroll-p":K()}],"scroll-px":[{"scroll-px":K()}],"scroll-py":[{"scroll-py":K()}],"scroll-ps":[{"scroll-ps":K()}],"scroll-pe":[{"scroll-pe":K()}],"scroll-pt":[{"scroll-pt":K()}],"scroll-pr":[{"scroll-pr":K()}],"scroll-pb":[{"scroll-pb":K()}],"scroll-pl":[{"scroll-pl":K()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ae,te]}],fill:[{fill:["none",...V()]}],"stroke-w":[{stroke:[be,Zn,Xl,du]}],stroke:[{stroke:["none",...V()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},R1=c1(A1);function ct(...i){return R1(A0(i))}const ye=$.forwardRef(({className:i,variant:c="default",size:o="default",...u},d)=>{const h={default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},y={default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"};return s.jsx("button",{className:ct("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",h[c],y[o],i),ref:d,...u})});ye.displayName="Button";function $t({className:i,type:c,...o}){return s.jsx("input",{type:c,"data-slot":"input",className:ct("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",i),...o})}function $h(i,c){if(typeof i=="function")return i(c);i!=null&&(i.current=c)}function L0(...i){return c=>{let o=!1;const u=i.map(d=>{const h=$h(d,c);return!o&&typeof h=="function"&&(o=!0),h});if(o)return()=>{for(let d=0;d<u.length;d++){const h=u[d];typeof h=="function"?h():$h(i[d],null)}}}}function Ls(...i){return N.useCallback(L0(...i),i)}function Dr(i){const c=M1(i),o=N.forwardRef((u,d)=>{const{children:h,...y}=u,v=N.Children.toArray(h),g=v.find(_1);if(g){const m=g.props.children,b=v.map(x=>x===g?N.Children.count(m)>1?N.Children.only(null):N.isValidElement(m)?m.props.children:null:x);return s.jsx(c,{...y,ref:d,children:N.isValidElement(m)?N.cloneElement(m,void 0,b):null})}return s.jsx(c,{...y,ref:d,children:h})});return o.displayName=`${i}.Slot`,o}var C1=Dr("Slot");function M1(i){const c=N.forwardRef((o,u)=>{const{children:d,...h}=o;if(N.isValidElement(d)){const y=D1(d),v=z1(h,d.props);return d.type!==N.Fragment&&(v.ref=u?L0(u,y):y),N.cloneElement(d,v)}return N.Children.count(d)>1?N.Children.only(null):null});return c.displayName=`${i}.SlotClone`,c}var k1=Symbol("radix.slottable");function _1(i){return N.isValidElement(i)&&typeof i.type=="function"&&"__radixId"in i.type&&i.type.__radixId===k1}function z1(i,c){const o={...c};for(const u in c){const d=i[u],h=c[u];/^on[A-Z]/.test(u)?d&&h?o[u]=(...v)=>{const g=h(...v);return d(...v),g}:d&&(o[u]=d):u==="style"?o[u]={...d,...h}:u==="className"&&(o[u]=[d,h].filter(Boolean).join(" "))}return{...i,...o}}function D1(i){var u,d;let c=(u=Object.getOwnPropertyDescriptor(i.props,"ref"))==null?void 0:u.get,o=c&&"isReactWarning"in c&&c.isReactWarning;return o?i.ref:(c=(d=Object.getOwnPropertyDescriptor(i,"ref"))==null?void 0:d.get,o=c&&"isReactWarning"in c&&c.isReactWarning,o?i.props.ref:i.props.ref||i.ref)}var O1=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],xa=O1.reduce((i,c)=>{const o=Dr(`Primitive.${c}`),u=N.forwardRef((d,h)=>{const{asChild:y,...v}=d,g=y?o:c;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),s.jsx(g,{...v,ref:h})});return u.displayName=`Primitive.${c}`,{...i,[c]:u}},{}),U1="Label",H0=N.forwardRef((i,c)=>s.jsx(xa.label,{...i,ref:c,onMouseDown:o=>{var d;o.target.closest("button, input, select, textarea")||((d=i.onMouseDown)==null||d.call(i,o),!o.defaultPrevented&&o.detail>1&&o.preventDefault())}}));H0.displayName=U1;var B1=H0;function at({className:i,...c}){return s.jsx(B1,{"data-slot":"label",className:ct("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",i),...c})}function ce({className:i,...c}){return s.jsx("div",{"data-slot":"card",className:ct("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",i),...c})}function me({className:i,...c}){return s.jsx("div",{"data-slot":"card-header",className:ct("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",i),...c})}function he({className:i,...c}){return s.jsx("div",{"data-slot":"card-title",className:ct("leading-none font-semibold",i),...c})}function xt({className:i,...c}){return s.jsx("div",{"data-slot":"card-description",className:ct("text-muted-foreground text-sm",i),...c})}function oe({className:i,...c}){return s.jsx("div",{"data-slot":"card-content",className:ct("px-6",i),...c})}const Jh=i=>typeof i=="boolean"?`${i}`:i===0?"0":i,Ph=A0,q0=(i,c)=>o=>{var u;if((c==null?void 0:c.variants)==null)return Ph(i,o==null?void 0:o.class,o==null?void 0:o.className);const{variants:d,defaultVariants:h}=c,y=Object.keys(d).map(m=>{const b=o==null?void 0:o[m],x=h==null?void 0:h[m];if(b===null)return null;const j=Jh(b)||Jh(x);return d[m][j]}),v=o&&Object.entries(o).reduce((m,b)=>{let[x,j]=b;return j===void 0||(m[x]=j),m},{}),g=c==null||(u=c.compoundVariants)===null||u===void 0?void 0:u.reduce((m,b)=>{let{class:x,className:j,..._}=b;return Object.entries(_).every(E=>{let[L,D]=E;return Array.isArray(D)?D.includes({...h,...v}[L]):{...h,...v}[L]===D})?[...m,x,j]:m},[]);return Ph(i,y,g,o==null?void 0:o.class,o==null?void 0:o.className)},L1=q0("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function ti({className:i,variant:c,...o}){return s.jsx("div",{"data-slot":"alert",role:"alert",className:ct(L1({variant:c}),i),...o})}function ai({className:i,...c}){return s.jsx("div",{"data-slot":"alert-description",className:ct("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",i),...c})}const H1=()=>{const[i,c]=N.useState({firstName:"",lastName:"",email:"",password:"",confirmPassword:""}),[o,u]=N.useState(!1),[d,h]=N.useState(""),{register:y}=Lr(),v=Fn(),g=b=>{c({...i,[b.target.name]:b.target.value})},m=async b=>{if(b.preventDefault(),u(!0),h(""),i.password!==i.confirmPassword){h("Passwords do not match"),u(!1);return}if(i.password.length<8){h("Password must be at least 8 characters long"),u(!1);return}try{await y({first_name:i.firstName,last_name:i.lastName,email:i.email,password:i.password}),Bt.success("Account created successfully! Welcome to Identity Guardian."),v("/dashboard")}catch(x){h(x.message||"Registration failed. Please try again."),Bt.error("Registration failed. Please check your information.")}finally{u(!1)}};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4",children:s.jsxs("div",{className:"w-full max-w-md",children:[s.jsxs("div",{className:"text-center mb-8",children:[s.jsxs(Lt,{to:"/",className:"inline-flex items-center space-x-2 text-white hover:text-purple-300 transition-colors",children:[s.jsx(Ge,{className:"h-8 w-8 text-purple-400"}),s.jsx("span",{className:"text-2xl font-bold",children:"Identity Guardian"})]}),s.jsx("p",{className:"text-slate-400 mt-2",children:"Protect your digital identity"})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700 backdrop-blur-sm",children:[s.jsxs(me,{className:"space-y-1",children:[s.jsx(he,{className:"text-2xl text-center text-white",children:"Create Account"}),s.jsx(xt,{className:"text-center text-slate-300",children:"Join Identity Guardian and start protecting your digital life"})]}),s.jsxs(oe,{children:[s.jsxs("form",{onSubmit:m,className:"space-y-4",children:[d&&s.jsx(ti,{className:"border-red-500/50 bg-red-500/10",children:s.jsx(ai,{className:"text-red-300",children:d})}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{htmlFor:"firstName",className:"text-slate-300",children:"First Name"}),s.jsxs("div",{className:"relative",children:[s.jsx(zr,{className:"absolute left-3 top-3 h-4 w-4 text-slate-400"}),s.jsx($t,{id:"firstName",name:"firstName",type:"text",placeholder:"John",value:i.firstName,onChange:g,className:"pl-10 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400",required:!0})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{htmlFor:"lastName",className:"text-slate-300",children:"Last Name"}),s.jsxs("div",{className:"relative",children:[s.jsx(zr,{className:"absolute left-3 top-3 h-4 w-4 text-slate-400"}),s.jsx($t,{id:"lastName",name:"lastName",type:"text",placeholder:"Doe",value:i.lastName,onChange:g,className:"pl-10 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400",required:!0})]})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{htmlFor:"email",className:"text-slate-300",children:"Email"}),s.jsxs("div",{className:"relative",children:[s.jsx(Nl,{className:"absolute left-3 top-3 h-4 w-4 text-slate-400"}),s.jsx($t,{id:"email",name:"email",type:"email",placeholder:"<EMAIL>",value:i.email,onChange:g,className:"pl-10 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400",required:!0})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{htmlFor:"password",className:"text-slate-300",children:"Password"}),s.jsxs("div",{className:"relative",children:[s.jsx(jl,{className:"absolute left-3 top-3 h-4 w-4 text-slate-400"}),s.jsx($t,{id:"password",name:"password",type:"password",placeholder:"Create a strong password",value:i.password,onChange:g,className:"pl-10 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400",required:!0})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{htmlFor:"confirmPassword",className:"text-slate-300",children:"Confirm Password"}),s.jsxs("div",{className:"relative",children:[s.jsx(jl,{className:"absolute left-3 top-3 h-4 w-4 text-slate-400"}),s.jsx($t,{id:"confirmPassword",name:"confirmPassword",type:"password",placeholder:"Confirm your password",value:i.confirmPassword,onChange:g,className:"pl-10 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400",required:!0})]})]}),s.jsx(ye,{type:"submit",className:"w-full bg-purple-600 hover:bg-purple-700",disabled:o,children:o?s.jsxs(s.Fragment,{children:[s.jsx(j0,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating Account..."]}):"Create Account"})]}),s.jsx("div",{className:"mt-6 text-center",children:s.jsxs("p",{className:"text-slate-400",children:["Already have an account?"," ",s.jsx(Lt,{to:"/login",className:"text-purple-400 hover:text-purple-300 font-medium",children:"Sign in here"})]})})]})]}),s.jsx("div",{className:"mt-8 text-center",children:s.jsx("p",{className:"text-slate-400 text-sm",children:"By creating an account, you agree to our terms of service and privacy policy"})})]})})},q1=()=>{const[i,c]=N.useState(85),[o,u]=N.useState(!0),[d,h]=N.useState("overview");N.useEffect(()=>{setTimeout(()=>u(!1),1e3)},[]);const y=[{title:"Security Score",value:i,max:100,status:"excellent",icon:Ge,gradient:"from-green-500 to-emerald-500",description:"Overall security health of your digital identity"},{title:"Identities Monitored",value:5,status:"active",icon:Nt,gradient:"from-blue-500 to-cyan-500",description:"Phone numbers & emails"},{title:"Breaches Detected",value:2,status:"warning",icon:na,gradient:"from-orange-500 to-red-500",description:"Requires attention"},{title:"Strong Passwords",value:8,total:10,status:"good",icon:jl,gradient:"from-purple-500 to-pink-500",description:"Password vault status"}],v=[{id:1,type:"breach",title:"New breach detected",description:"Your email found in LinkedIn data breach",time:"2 hours ago",severity:"high",icon:na,color:"text-red-400"},{id:2,type:"password",title:"Weak password detected",description:"Password for social media account needs strengthening",time:"1 day ago",severity:"medium",icon:Us,color:"text-yellow-400"},{id:3,type:"monitoring",title:"New identity added",description:"Started monitoring ******-0123",time:"3 days ago",severity:"info",icon:Ql,color:"text-blue-400"},{id:4,type:"security",title:"Security score improved",description:"Your security score increased to 85/100",time:"1 week ago",severity:"success",icon:ei,color:"text-green-400"}],g=[{title:"Add Identity",description:"Monitor new phone or email",icon:Nt,gradient:"from-blue-500 to-cyan-500",action:()=>console.log("Add identity")},{title:"Check Password",description:"Analyze password strength",icon:jl,gradient:"from-purple-500 to-pink-500",action:()=>console.log("Check password")},{title:"Security Tips",description:"Learn best practices",icon:E0,gradient:"from-green-500 to-emerald-500",action:()=>console.log("Security tips")},{title:"Generate Password",description:"Create secure password",icon:Us,gradient:"from-orange-500 to-red-500",action:()=>console.log("Generate password")}];return o?s.jsx("div",{className:"min-h-screen animated-bg flex items-center justify-center",children:s.jsxs("div",{className:"glass-card p-8 text-center",children:[s.jsx("div",{className:"loading-spinner mx-auto mb-4"}),s.jsx("p",{className:"text-white text-lg",children:"Loading your security dashboard..."})]})}):s.jsx("div",{className:"min-h-screen animated-bg p-6",children:s.jsxs("div",{className:"max-w-7xl mx-auto",children:[s.jsx("div",{className:"mb-8 slide-in",children:s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsxs("div",{children:[s.jsxs("h1",{className:"text-4xl font-bold text-white mb-2",children:["Welcome back, ",s.jsx("span",{className:"text-gradient",children:"Demo!"})]}),s.jsx("p",{className:"text-gray-300 text-lg",children:"Here's your cybersecurity overview and recent activity"})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("button",{className:"btn-secondary flex items-center space-x-2",children:[s.jsx(_r,{className:"h-5 w-5"}),s.jsx("span",{children:"Notifications"})]}),s.jsxs("button",{className:"btn-primary flex items-center space-x-2",children:[s.jsx(S0,{className:"h-5 w-5"}),s.jsx("span",{children:"Settings"})]})]})]})}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:y.map((m,b)=>{const x=m.icon;return s.jsxs("div",{className:"glass-card p-6 hover-lift fade-in",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("div",{className:`w-12 h-12 bg-gradient-to-r ${m.gradient} rounded-xl flex items-center justify-center`,children:s.jsx(x,{className:"h-6 w-6 text-white"})}),m.status==="excellent"&&s.jsx(Jv,{className:"h-5 w-5 text-yellow-400"}),m.status==="warning"&&s.jsx(na,{className:"h-5 w-5 text-red-400"})]}),s.jsxs("div",{className:"mb-3",children:[s.jsxs("div",{className:"flex items-baseline space-x-2",children:[s.jsx("span",{className:"text-3xl font-bold text-white",children:m.value}),m.max&&s.jsxs("span",{className:"text-gray-400",children:["/ ",m.max]}),m.total&&s.jsxs("span",{className:"text-gray-400",children:["/ ",m.total]})]}),s.jsx("h3",{className:"text-gray-300 font-medium",children:m.title})]}),m.max&&s.jsx("div",{className:"progress-bar mb-3",children:s.jsx("div",{className:"progress-fill",style:{width:`${m.value/m.max*100}%`}})}),s.jsx("p",{className:"text-sm text-gray-400",children:m.description})]},b)})}),s.jsxs("div",{className:"grid lg:grid-cols-3 gap-8",children:[s.jsx("div",{className:"lg:col-span-2",children:s.jsxs("div",{className:"glass-card p-6 hover-lift",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsxs("h2",{className:"text-2xl font-bold text-white flex items-center space-x-3",children:[s.jsx($n,{className:"h-6 w-6 text-blue-400"}),s.jsx("span",{children:"Recent Activity"})]}),s.jsx("button",{className:"btn-secondary text-sm",children:"View All"})]}),s.jsx("div",{className:"space-y-4",children:v.map(m=>{const b=m.icon;return s.jsxs("div",{className:"flex items-start space-x-4 p-4 rounded-xl bg-white/5 hover:bg-white/10 transition-all duration-300",children:[s.jsx("div",{className:`w-10 h-10 rounded-lg bg-gradient-to-r ${m.severity==="high"?"from-red-500 to-pink-500":m.severity==="medium"?"from-yellow-500 to-orange-500":m.severity==="success"?"from-green-500 to-emerald-500":"from-blue-500 to-cyan-500"} flex items-center justify-center`,children:s.jsx(b,{className:"h-5 w-5 text-white"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h3",{className:"text-white font-medium mb-1",children:m.title}),s.jsx("p",{className:"text-gray-400 text-sm mb-2",children:m.description}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(Cu,{className:"h-4 w-4 text-gray-500"}),s.jsx("span",{className:"text-gray-500 text-xs",children:m.time})]})]}),s.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-medium ${m.severity==="high"?"bg-red-500/20 text-red-300":m.severity==="medium"?"bg-yellow-500/20 text-yellow-300":m.severity==="success"?"bg-green-500/20 text-green-300":"bg-blue-500/20 text-blue-300"}`,children:m.severity})]},m.id)})})]})}),s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"glass-card p-6 hover-lift",children:[s.jsxs("h2",{className:"text-xl font-bold text-white mb-6 flex items-center space-x-3",children:[s.jsx(Bs,{className:"h-5 w-5 text-yellow-400"}),s.jsx("span",{children:"Quick Actions"})]}),s.jsx("div",{className:"grid grid-cols-2 gap-4",children:g.map((m,b)=>{const x=m.icon;return s.jsxs("button",{onClick:m.action,className:"p-4 rounded-xl bg-white/5 hover:bg-white/10 transition-all duration-300 text-left group",children:[s.jsx("div",{className:`w-8 h-8 bg-gradient-to-r ${m.gradient} rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300`,children:s.jsx(x,{className:"h-4 w-4 text-white"})}),s.jsx("h3",{className:"text-white font-medium text-sm mb-1",children:m.title}),s.jsx("p",{className:"text-gray-400 text-xs",children:m.description})]},b)})})]}),s.jsxs("div",{className:"glass-card p-6 hover-lift",children:[s.jsxs("h2",{className:"text-xl font-bold text-white mb-6 flex items-center space-x-3",children:[s.jsx(Ge,{className:"h-5 w-5 text-green-400"}),s.jsx("span",{children:"Security Status"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between p-3 rounded-lg bg-green-500/10",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(At,{className:"h-5 w-5 text-green-400"}),s.jsx("span",{className:"text-white text-sm",children:"Protected"})]}),s.jsx("span",{className:"badge badge-success",children:"Active"})]}),s.jsxs("div",{className:"flex items-center justify-between p-3 rounded-lg bg-blue-500/10",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Nt,{className:"h-5 w-5 text-blue-400"}),s.jsx("span",{className:"text-white text-sm",children:"Monitoring"})]}),s.jsx("span",{className:"badge badge-success",children:"24/7"})]}),s.jsxs("div",{className:"flex items-center justify-between p-3 rounded-lg bg-yellow-500/10",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(na,{className:"h-5 w-5 text-yellow-400"}),s.jsx("span",{className:"text-white text-sm",children:"Alerts"})]}),s.jsx("span",{className:"badge badge-warning",children:"2 New"})]})]})]}),s.jsxs("div",{className:"glass-card p-6 hover-lift",children:[s.jsxs("h2",{className:"text-xl font-bold text-white mb-6 flex items-center space-x-3",children:[s.jsx(lb,{className:"h-5 w-5 text-purple-400"}),s.jsx("span",{children:"Score Breakdown"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsxs("div",{className:"flex justify-between text-sm mb-2",children:[s.jsx("span",{className:"text-gray-300",children:"Identity Security"}),s.jsx("span",{className:"text-white",children:"90%"})]}),s.jsx("div",{className:"progress-bar",children:s.jsx("div",{className:"progress-fill",style:{width:"90%"}})})]}),s.jsxs("div",{children:[s.jsxs("div",{className:"flex justify-between text-sm mb-2",children:[s.jsx("span",{className:"text-gray-300",children:"Password Strength"}),s.jsx("span",{className:"text-white",children:"85%"})]}),s.jsx("div",{className:"progress-bar",children:s.jsx("div",{className:"progress-fill",style:{width:"85%"}})})]}),s.jsxs("div",{children:[s.jsxs("div",{className:"flex justify-between text-sm mb-2",children:[s.jsx("span",{className:"text-gray-300",children:"Behavior Score"}),s.jsx("span",{className:"text-white",children:"80%"})]}),s.jsx("div",{className:"progress-bar",children:s.jsx("div",{className:"progress-fill",style:{width:"80%"}})})]})]})]})]})]}),s.jsxs("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"glass-card p-6 text-center hover-lift",children:[s.jsx(Za,{className:"h-8 w-8 mx-auto mb-4 text-blue-400"}),s.jsx("div",{className:"text-2xl font-bold text-white mb-2",children:"Global"}),s.jsx("div",{className:"text-gray-300 text-sm",children:"Worldwide monitoring coverage"})]}),s.jsxs("div",{className:"glass-card p-6 text-center hover-lift",children:[s.jsx(ku,{className:"h-8 w-8 mx-auto mb-4 text-green-400"}),s.jsx("div",{className:"text-2xl font-bold text-white mb-2",children:"50M+"}),s.jsx("div",{className:"text-gray-300 text-sm",children:"Identities protected globally"})]}),s.jsxs("div",{className:"glass-card p-6 text-center hover-lift",children:[s.jsx(Bs,{className:"h-8 w-8 mx-auto mb-4 text-yellow-400"}),s.jsx("div",{className:"text-2xl font-bold text-white mb-2",children:"Real-time"}),s.jsx("div",{className:"text-gray-300 text-sm",children:"Instant threat detection"})]})]})]})})},G1=q0("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function Ve({className:i,variant:c,asChild:o=!1,...u}){const d=o?C1:"span";return s.jsx(d,{"data-slot":"badge",className:ct(G1({variant:c}),i),...u})}const li=({children:i})=>{const[c,o]=N.useState(!1),{user:u,logout:d}=Lr(),h=$a(),y=Fn(),v=[{name:"Dashboard",href:"/dashboard",icon:pb,current:h.pathname==="/dashboard"},{name:"Identities",href:"/identities",icon:Nt,current:h.pathname==="/identities"},{name:"Passwords",href:"/passwords",icon:Us,current:h.pathname==="/passwords"},{name:"Monitoring",href:"/monitoring",icon:$n,current:h.pathname==="/monitoring"},{name:"Security Tips",href:"/security-tips",icon:Kn,current:h.pathname==="/security-tips"},{name:"Settings",href:"/settings",icon:S0,current:h.pathname==="/settings"}],g=()=>{d(),Bt.success("Logged out successfully"),y("/")};return s.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900",children:[c&&s.jsx("div",{className:"fixed inset-0 z-40 bg-black/50 lg:hidden",onClick:()=>o(!1)}),s.jsx("div",{className:`
        fixed inset-y-0 left-0 z-50 w-64 bg-slate-900/95 backdrop-blur-sm border-r border-slate-800 transform transition-transform duration-300 ease-in-out lg:translate-x-0
        ${c?"translate-x-0":"-translate-x-full"}
      `,children:s.jsxs("div",{className:"flex flex-col h-full",children:[s.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-slate-800",children:[s.jsxs(Lt,{to:"/dashboard",className:"flex items-center space-x-2",children:[s.jsx(Ge,{className:"h-8 w-8 text-purple-400"}),s.jsx("span",{className:"text-xl font-bold text-white",children:"Identity Guardian"})]}),s.jsx(ye,{variant:"ghost",size:"sm",className:"lg:hidden text-slate-400 hover:text-white",onClick:()=>o(!1),children:s.jsx(Qb,{className:"h-5 w-5"})})]}),s.jsxs("div",{className:"p-6 border-b border-slate-800",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center",children:s.jsx(zr,{className:"h-5 w-5 text-white"})}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:(u==null?void 0:u.full_name)||"User"}),s.jsx("p",{className:"text-slate-400 text-sm",children:u==null?void 0:u.email})]})]}),s.jsxs(Ve,{variant:"outline",className:"mt-3 border-green-500/50 text-green-300",children:[s.jsx(Ge,{className:"w-3 h-3 mr-1"}),"Protected"]})]}),s.jsx("nav",{className:"flex-1 p-6 space-y-2",children:v.map(m=>{const b=m.icon;return s.jsxs(Lt,{to:m.href,className:`
                    flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors
                    ${m.current?"bg-purple-600 text-white":"text-slate-300 hover:text-white hover:bg-slate-800/50"}
                  `,onClick:()=>o(!1),children:[s.jsx(b,{className:"h-5 w-5"}),s.jsx("span",{children:m.name})]},m.name)})}),s.jsx("div",{className:"p-6 border-t border-slate-800",children:s.jsxs(ye,{variant:"ghost",className:"w-full justify-start text-slate-300 hover:text-white hover:bg-slate-800/50",onClick:g,children:[s.jsx(jb,{className:"h-5 w-5 mr-3"}),"Sign Out"]})})]})}),s.jsxs("div",{className:"lg:pl-64",children:[s.jsx("div",{className:"sticky top-0 z-30 bg-slate-900/95 backdrop-blur-sm border-b border-slate-800 px-6 py-4",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(ye,{variant:"ghost",size:"sm",className:"lg:hidden text-slate-400 hover:text-white",onClick:()=>o(!0),children:s.jsx(Eb,{className:"h-5 w-5"})}),s.jsx("div",{className:"flex items-center space-x-4",children:s.jsx(Ve,{variant:"outline",className:"border-purple-500/50 text-purple-300",children:"White Hat Security"})})]})}),s.jsx("main",{className:"p-6",children:i})]})]})};function Ea(i,c,{checkForDefaultPrevented:o=!0}={}){return function(d){if(i==null||i(d),o===!1||!d.defaultPrevented)return c==null?void 0:c(d)}}function si(i,c=[]){let o=[];function u(h,y){const v=N.createContext(y),g=o.length;o=[...o,y];const m=x=>{var B;const{scope:j,children:_,...E}=x,L=((B=j==null?void 0:j[i])==null?void 0:B[g])||v,D=N.useMemo(()=>E,Object.values(E));return s.jsx(L.Provider,{value:D,children:_})};m.displayName=h+"Provider";function b(x,j){var L;const _=((L=j==null?void 0:j[i])==null?void 0:L[g])||v,E=N.useContext(_);if(E)return E;if(y!==void 0)return y;throw new Error(`\`${x}\` must be used within \`${h}\``)}return[m,b]}const d=()=>{const h=o.map(y=>N.createContext(y));return function(v){const g=(v==null?void 0:v[i])||h;return N.useMemo(()=>({[`__scope${i}`]:{...v,[i]:g}}),[v,g])}};return d.scopeName=i,[u,Y1(d,...c)]}function Y1(...i){const c=i[0];if(i.length===1)return c;const o=()=>{const u=i.map(d=>({useScope:d(),scopeName:d.scopeName}));return function(h){const y=u.reduce((v,{useScope:g,scopeName:m})=>{const x=g(h)[`__scope${m}`];return{...v,...x}},{});return N.useMemo(()=>({[`__scope${c.scopeName}`]:y}),[y])}};return o.scopeName=c.scopeName,o}function V1(i){const c=i+"CollectionProvider",[o,u]=si(c),[d,h]=o(c,{collectionRef:{current:null},itemMap:new Map}),y=L=>{const{scope:D,children:B}=L,Q=$.useRef(null),O=$.useRef(new Map).current;return s.jsx(d,{scope:D,itemMap:O,collectionRef:Q,children:B})};y.displayName=c;const v=i+"CollectionSlot",g=Dr(v),m=$.forwardRef((L,D)=>{const{scope:B,children:Q}=L,O=h(v,B),P=Ls(D,O.collectionRef);return s.jsx(g,{ref:P,children:Q})});m.displayName=v;const b=i+"CollectionItemSlot",x="data-radix-collection-item",j=Dr(b),_=$.forwardRef((L,D)=>{const{scope:B,children:Q,...O}=L,P=$.useRef(null),J=Ls(D,P),ie=h(b,B);return $.useEffect(()=>(ie.itemMap.set(P,{ref:P,...O}),()=>void ie.itemMap.delete(P))),s.jsx(j,{[x]:"",ref:J,children:Q})});_.displayName=b;function E(L){const D=h(i+"CollectionConsumer",L);return $.useCallback(()=>{const Q=D.collectionRef.current;if(!Q)return[];const O=Array.from(Q.querySelectorAll(`[${x}]`));return Array.from(D.itemMap.values()).sort((ie,pe)=>O.indexOf(ie.ref.current)-O.indexOf(pe.ref.current))},[D.collectionRef,D.itemMap])}return[{Provider:y,Slot:m,ItemSlot:_},E,u]}var Pn=globalThis!=null&&globalThis.document?N.useLayoutEffect:()=>{},X1=t0[" useId ".trim().toString()]||(()=>{}),Q1=0;function G0(i){const[c,o]=N.useState(X1());return Pn(()=>{o(u=>u??String(Q1++))},[i]),i||(c?`radix-${c}`:"")}function Z1(i){const c=N.useRef(i);return N.useEffect(()=>{c.current=i}),N.useMemo(()=>(...o)=>{var u;return(u=c.current)==null?void 0:u.call(c,...o)},[])}var K1=t0[" useInsertionEffect ".trim().toString()]||Pn;function zu({prop:i,defaultProp:c,onChange:o=()=>{},caller:u}){const[d,h,y]=$1({defaultProp:c,onChange:o}),v=i!==void 0,g=v?i:d;{const b=N.useRef(i!==void 0);N.useEffect(()=>{const x=b.current;x!==v&&console.warn(`${u} is changing from ${x?"controlled":"uncontrolled"} to ${v?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),b.current=v},[v,u])}const m=N.useCallback(b=>{var x;if(v){const j=J1(b)?b(i):b;j!==i&&((x=y.current)==null||x.call(y,j))}else h(b)},[v,i,h,y]);return[g,m]}function $1({defaultProp:i,onChange:c}){const[o,u]=N.useState(i),d=N.useRef(o),h=N.useRef(c);return K1(()=>{h.current=c},[c]),N.useEffect(()=>{var y;d.current!==o&&((y=h.current)==null||y.call(h,o),d.current=o)},[o,d]),[o,u,h]}function J1(i){return typeof i=="function"}var P1=N.createContext(void 0);function Y0(i){const c=N.useContext(P1);return i||c||"ltr"}var fu="rovingFocusGroup.onEntryFocus",W1={bubbles:!1,cancelable:!0},ni="RovingFocusGroup",[bu,V0,F1]=V1(ni),[I1,X0]=si(ni,[F1]),[eN,tN]=I1(ni),Q0=N.forwardRef((i,c)=>s.jsx(bu.Provider,{scope:i.__scopeRovingFocusGroup,children:s.jsx(bu.Slot,{scope:i.__scopeRovingFocusGroup,children:s.jsx(aN,{...i,ref:c})})}));Q0.displayName=ni;var aN=N.forwardRef((i,c)=>{const{__scopeRovingFocusGroup:o,orientation:u,loop:d=!1,dir:h,currentTabStopId:y,defaultCurrentTabStopId:v,onCurrentTabStopIdChange:g,onEntryFocus:m,preventScrollOnEntryFocus:b=!1,...x}=i,j=N.useRef(null),_=Ls(c,j),E=Y0(h),[L,D]=zu({prop:y,defaultProp:v??null,onChange:g,caller:ni}),[B,Q]=N.useState(!1),O=Z1(m),P=V0(o),J=N.useRef(!1),[ie,pe]=N.useState(0);return N.useEffect(()=>{const K=j.current;if(K)return K.addEventListener(fu,O),()=>K.removeEventListener(fu,O)},[O]),s.jsx(eN,{scope:o,orientation:u,dir:E,loop:d,currentTabStopId:L,onItemFocus:N.useCallback(K=>D(K),[D]),onItemShiftTab:N.useCallback(()=>Q(!0),[]),onFocusableItemAdd:N.useCallback(()=>pe(K=>K+1),[]),onFocusableItemRemove:N.useCallback(()=>pe(K=>K-1),[]),children:s.jsx(xa.div,{tabIndex:B||ie===0?-1:0,"data-orientation":u,...x,ref:_,style:{outline:"none",...i.style},onMouseDown:Ea(i.onMouseDown,()=>{J.current=!0}),onFocus:Ea(i.onFocus,K=>{const I=!J.current;if(K.target===K.currentTarget&&I&&!B){const Ee=new CustomEvent(fu,W1);if(K.currentTarget.dispatchEvent(Ee),!Ee.defaultPrevented){const Te=P().filter(R=>R.focusable),Ae=Te.find(R=>R.active),ot=Te.find(R=>R.id===L),Re=[Ae,ot,...Te].filter(Boolean).map(R=>R.ref.current);$0(Re,b)}}J.current=!1}),onBlur:Ea(i.onBlur,()=>Q(!1))})})}),Z0="RovingFocusGroupItem",K0=N.forwardRef((i,c)=>{const{__scopeRovingFocusGroup:o,focusable:u=!0,active:d=!1,tabStopId:h,children:y,...v}=i,g=G0(),m=h||g,b=tN(Z0,o),x=b.currentTabStopId===m,j=V0(o),{onFocusableItemAdd:_,onFocusableItemRemove:E,currentTabStopId:L}=b;return N.useEffect(()=>{if(u)return _(),()=>E()},[u,_,E]),s.jsx(bu.ItemSlot,{scope:o,id:m,focusable:u,active:d,children:s.jsx(xa.span,{tabIndex:x?0:-1,"data-orientation":b.orientation,...v,ref:c,onMouseDown:Ea(i.onMouseDown,D=>{u?b.onItemFocus(m):D.preventDefault()}),onFocus:Ea(i.onFocus,()=>b.onItemFocus(m)),onKeyDown:Ea(i.onKeyDown,D=>{if(D.key==="Tab"&&D.shiftKey){b.onItemShiftTab();return}if(D.target!==D.currentTarget)return;const B=nN(D,b.orientation,b.dir);if(B!==void 0){if(D.metaKey||D.ctrlKey||D.altKey||D.shiftKey)return;D.preventDefault();let O=j().filter(P=>P.focusable).map(P=>P.ref.current);if(B==="last")O.reverse();else if(B==="prev"||B==="next"){B==="prev"&&O.reverse();const P=O.indexOf(D.currentTarget);O=b.loop?iN(O,P+1):O.slice(P+1)}setTimeout(()=>$0(O))}}),children:typeof y=="function"?y({isCurrentTabStop:x,hasTabStop:L!=null}):y})})});K0.displayName=Z0;var lN={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function sN(i,c){return c!=="rtl"?i:i==="ArrowLeft"?"ArrowRight":i==="ArrowRight"?"ArrowLeft":i}function nN(i,c,o){const u=sN(i.key,o);if(!(c==="vertical"&&["ArrowLeft","ArrowRight"].includes(u))&&!(c==="horizontal"&&["ArrowUp","ArrowDown"].includes(u)))return lN[u]}function $0(i,c=!1){const o=document.activeElement;for(const u of i)if(u===o||(u.focus({preventScroll:c}),document.activeElement!==o))return}function iN(i,c){return i.map((o,u)=>i[(c+u)%i.length])}var rN=Q0,cN=K0;function oN(i,c){return N.useReducer((o,u)=>c[o][u]??o,i)}var J0=i=>{const{present:c,children:o}=i,u=uN(c),d=typeof o=="function"?o({present:u.isPresent}):N.Children.only(o),h=Ls(u.ref,dN(d));return typeof o=="function"||u.isPresent?N.cloneElement(d,{ref:h}):null};J0.displayName="Presence";function uN(i){const[c,o]=N.useState(),u=N.useRef(null),d=N.useRef(i),h=N.useRef("none"),y=i?"mounted":"unmounted",[v,g]=oN(y,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return N.useEffect(()=>{const m=Rr(u.current);h.current=v==="mounted"?m:"none"},[v]),Pn(()=>{const m=u.current,b=d.current;if(b!==i){const j=h.current,_=Rr(m);i?g("MOUNT"):_==="none"||(m==null?void 0:m.display)==="none"?g("UNMOUNT"):g(b&&j!==_?"ANIMATION_OUT":"UNMOUNT"),d.current=i}},[i,g]),Pn(()=>{if(c){let m;const b=c.ownerDocument.defaultView??window,x=_=>{const L=Rr(u.current).includes(_.animationName);if(_.target===c&&L&&(g("ANIMATION_END"),!d.current)){const D=c.style.animationFillMode;c.style.animationFillMode="forwards",m=b.setTimeout(()=>{c.style.animationFillMode==="forwards"&&(c.style.animationFillMode=D)})}},j=_=>{_.target===c&&(h.current=Rr(u.current))};return c.addEventListener("animationstart",j),c.addEventListener("animationcancel",x),c.addEventListener("animationend",x),()=>{b.clearTimeout(m),c.removeEventListener("animationstart",j),c.removeEventListener("animationcancel",x),c.removeEventListener("animationend",x)}}else g("ANIMATION_END")},[c,g]),{isPresent:["mounted","unmountSuspended"].includes(v),ref:N.useCallback(m=>{u.current=m?getComputedStyle(m):null,o(m)},[])}}function Rr(i){return(i==null?void 0:i.animationName)||"none"}function dN(i){var u,d;let c=(u=Object.getOwnPropertyDescriptor(i.props,"ref"))==null?void 0:u.get,o=c&&"isReactWarning"in c&&c.isReactWarning;return o?i.ref:(c=(d=Object.getOwnPropertyDescriptor(i,"ref"))==null?void 0:d.get,o=c&&"isReactWarning"in c&&c.isReactWarning,o?i.props.ref:i.props.ref||i.ref)}var Hr="Tabs",[fN,GN]=si(Hr,[X0]),P0=X0(),[mN,Du]=fN(Hr),W0=N.forwardRef((i,c)=>{const{__scopeTabs:o,value:u,onValueChange:d,defaultValue:h,orientation:y="horizontal",dir:v,activationMode:g="automatic",...m}=i,b=Y0(v),[x,j]=zu({prop:u,onChange:d,defaultProp:h??"",caller:Hr});return s.jsx(mN,{scope:o,baseId:G0(),value:x,onValueChange:j,orientation:y,dir:b,activationMode:g,children:s.jsx(xa.div,{dir:b,"data-orientation":y,...m,ref:c})})});W0.displayName=Hr;var F0="TabsList",I0=N.forwardRef((i,c)=>{const{__scopeTabs:o,loop:u=!0,...d}=i,h=Du(F0,o),y=P0(o);return s.jsx(rN,{asChild:!0,...y,orientation:h.orientation,dir:h.dir,loop:u,children:s.jsx(xa.div,{role:"tablist","aria-orientation":h.orientation,...d,ref:c})})});I0.displayName=F0;var ex="TabsTrigger",tx=N.forwardRef((i,c)=>{const{__scopeTabs:o,value:u,disabled:d=!1,...h}=i,y=Du(ex,o),v=P0(o),g=sx(y.baseId,u),m=nx(y.baseId,u),b=u===y.value;return s.jsx(cN,{asChild:!0,...v,focusable:!d,active:b,children:s.jsx(xa.button,{type:"button",role:"tab","aria-selected":b,"aria-controls":m,"data-state":b?"active":"inactive","data-disabled":d?"":void 0,disabled:d,id:g,...h,ref:c,onMouseDown:Ea(i.onMouseDown,x=>{!d&&x.button===0&&x.ctrlKey===!1?y.onValueChange(u):x.preventDefault()}),onKeyDown:Ea(i.onKeyDown,x=>{[" ","Enter"].includes(x.key)&&y.onValueChange(u)}),onFocus:Ea(i.onFocus,()=>{const x=y.activationMode!=="manual";!b&&!d&&x&&y.onValueChange(u)})})})});tx.displayName=ex;var ax="TabsContent",lx=N.forwardRef((i,c)=>{const{__scopeTabs:o,value:u,forceMount:d,children:h,...y}=i,v=Du(ax,o),g=sx(v.baseId,u),m=nx(v.baseId,u),b=u===v.value,x=N.useRef(b);return N.useEffect(()=>{const j=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(j)},[]),s.jsx(J0,{present:d||b,children:({present:j})=>s.jsx(xa.div,{"data-state":b?"active":"inactive","data-orientation":v.orientation,role:"tabpanel","aria-labelledby":g,hidden:!j,id:m,tabIndex:0,...y,ref:c,style:{...i.style,animationDuration:x.current?"0s":void 0},children:j&&h})})});lx.displayName=ax;function sx(i,c){return`${i}-trigger-${c}`}function nx(i,c){return`${i}-content-${c}`}var hN=W0,xN=I0,pN=tx,gN=lx;function qr({className:i,...c}){return s.jsx(hN,{"data-slot":"tabs",className:ct("flex flex-col gap-2",i),...c})}function Gr({className:i,...c}){return s.jsx(xN,{"data-slot":"tabs-list",className:ct("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",i),...c})}function Ht({className:i,...c}){return s.jsx(pN,{"data-slot":"tabs-trigger",className:ct("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",i),...c})}function qt({className:i,...c}){return s.jsx(gN,{"data-slot":"tabs-content",className:ct("flex-1 outline-none",i),...c})}const yN=()=>{const[i,c]=N.useState([{id:1,type:"email",value:"<EMAIL>",status:"monitored",riskLevel:"medium",breachCount:2,lastBreach:"2023-08-15",lastChecked:"2 hours ago",breaches:[{name:"LinkedIn",date:"2021-06-01",severity:"medium"},{name:"Adobe",date:"2013-10-01",severity:"high"}]},{id:2,type:"email",value:"<EMAIL>",status:"monitored",riskLevel:"low",breachCount:0,lastBreach:null,lastChecked:"1 hour ago",breaches:[]},{id:3,type:"phone",value:"+****************",status:"monitored",riskLevel:"high",breachCount:1,lastBreach:"2024-01-10",lastChecked:"30 minutes ago",breaches:[{name:"T-Mobile",date:"2024-01-10",severity:"critical"}]},{id:4,type:"phone",value:"+****************",status:"monitored",riskLevel:"low",breachCount:0,lastBreach:null,lastChecked:"45 minutes ago",breaches:[]}]),[o,u]=N.useState({type:"email",value:""}),[d,h]=N.useState(""),y=x=>{switch(x){case"critical":return"text-red-400 bg-red-500/20 border-red-500/50";case"high":return"text-orange-400 bg-orange-500/20 border-orange-500/50";case"medium":return"text-yellow-400 bg-yellow-500/20 border-yellow-500/50";case"low":return"text-green-400 bg-green-500/20 border-green-500/50";default:return"text-slate-400 bg-slate-500/20 border-slate-500/50"}},v=x=>{switch(x){case"monitored":return"text-green-400 bg-green-500/20 border-green-500/50";case"paused":return"text-yellow-400 bg-yellow-500/20 border-yellow-500/50";case"error":return"text-red-400 bg-red-500/20 border-red-500/50";default:return"text-slate-400 bg-slate-500/20 border-slate-500/50"}},g=()=>{if(!o.value.trim()){Bt.error("Please enter a valid identity");return}const x={id:Date.now(),type:o.type,value:o.value.trim(),status:"monitored",riskLevel:"low",breachCount:0,lastBreach:null,lastChecked:"Just now",breaches:[]};c([...i,x]),u({type:"email",value:""}),Bt.success("Identity added and monitoring started!")},m=i.filter(x=>x.value.toLowerCase().includes(d.toLowerCase())),b={total:i.length,emails:i.filter(x=>x.type==="email").length,phones:i.filter(x=>x.type==="phone").length,breaches:i.reduce((x,j)=>x+j.breachCount,0),highRisk:i.filter(x=>["high","critical"].includes(x.riskLevel)).length};return s.jsx(li,{children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Identity Monitoring"}),s.jsx("p",{className:"text-slate-400",children:"Monitor your phone numbers and email addresses for breaches and unauthorized use"})]}),s.jsxs(ye,{className:"bg-purple-600 hover:bg-purple-700 mt-4 md:mt-0",children:[s.jsx(pu,{className:"w-4 h-4 mr-2"}),"Add Identity"]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Total Identities"}),s.jsx(Nt,{className:"h-4 w-4 text-blue-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:b.total}),s.jsx("p",{className:"text-xs text-slate-400",children:"Being monitored"})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Email Addresses"}),s.jsx(Nl,{className:"h-4 w-4 text-green-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:b.emails}),s.jsx("p",{className:"text-xs text-slate-400",children:"Protected"})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Phone Numbers"}),s.jsx(Ql,{className:"h-4 w-4 text-purple-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:b.phones}),s.jsx("p",{className:"text-xs text-slate-400",children:"Secured"})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Breaches Found"}),s.jsx(na,{className:"h-4 w-4 text-red-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:b.breaches}),s.jsx("p",{className:"text-xs text-slate-400",children:"Total detected"})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"High Risk"}),s.jsx(Ge,{className:"h-4 w-4 text-orange-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:b.highRisk}),s.jsx("p",{className:"text-xs text-slate-400",children:"Need attention"})]})]})]}),s.jsxs(qr,{defaultValue:"monitor",className:"space-y-6",children:[s.jsxs(Gr,{className:"grid w-full grid-cols-3 bg-slate-800/50",children:[s.jsx(Ht,{value:"monitor",className:"data-[state=active]:bg-purple-600",children:"Monitor Identities"}),s.jsx(Ht,{value:"add",className:"data-[state=active]:bg-purple-600",children:"Add New Identity"}),s.jsx(Ht,{value:"reports",className:"data-[state=active]:bg-purple-600",children:"Reports & Analytics"})]}),s.jsxs(qt,{value:"monitor",className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("div",{className:"relative flex-1",children:[s.jsx(w0,{className:"absolute left-3 top-3 h-4 w-4 text-slate-400"}),s.jsx($t,{placeholder:"Search identities...",value:d,onChange:x=>h(x.target.value),className:"pl-10 bg-slate-700/50 border-slate-600 text-white"})]}),s.jsxs(ye,{variant:"outline",className:"border-slate-600 text-slate-300",children:[s.jsx(Mu,{className:"w-4 h-4 mr-2"}),"Export"]})]}),s.jsx("div",{className:"space-y-4",children:m.map(x=>s.jsx(ce,{className:"bg-slate-800/50 border-slate-700",children:s.jsxs(oe,{className:"p-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center",children:x.type==="email"?s.jsx(Nl,{className:"h-6 w-6 text-purple-400"}):s.jsx(Ql,{className:"h-6 w-6 text-purple-400"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-white font-medium",children:x.value}),s.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[s.jsx(Ve,{className:v(x.status),children:x.status}),s.jsxs(Ve,{className:y(x.riskLevel),children:[x.riskLevel," risk"]})]}),s.jsxs("p",{className:"text-slate-400 text-sm mt-1",children:["Last checked: ",x.lastChecked]})]})]}),s.jsxs("div",{className:"text-right",children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[x.breachCount>0?s.jsx(na,{className:"h-5 w-5 text-red-400"}):s.jsx(At,{className:"h-5 w-5 text-green-400"}),s.jsxs("span",{className:"text-white font-medium",children:[x.breachCount," breach",x.breachCount!==1?"es":""]})]}),x.lastBreach&&s.jsxs("p",{className:"text-slate-400 text-sm",children:["Last breach: ",x.lastBreach]})]})]}),x.breaches.length>0&&s.jsxs("div",{className:"mt-4 p-4 bg-slate-700/30 rounded-lg border border-slate-600",children:[s.jsx("h4",{className:"text-white font-medium mb-3",children:"Detected Breaches"}),s.jsx("div",{className:"space-y-2",children:x.breaches.map((j,_)=>s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center",children:s.jsx(na,{className:"h-4 w-4 text-red-400"})}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:j.name}),s.jsx("p",{className:"text-slate-400 text-sm",children:j.date})]})]}),s.jsx(Ve,{className:y(j.severity),children:j.severity})]},_))})]})]})},x.id))})]}),s.jsxs(qt,{value:"add",className:"space-y-6",children:[s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsxs(he,{className:"text-white flex items-center",children:[s.jsx(pu,{className:"w-5 h-5 mr-2 text-purple-400"}),"Add New Identity"]}),s.jsx(xt,{children:"Start monitoring a new email address or phone number for breaches"})]}),s.jsxs(oe,{className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{className:"text-slate-300",children:"Identity Type"}),s.jsxs("div",{className:"flex space-x-4",children:[s.jsxs(ye,{variant:o.type==="email"?"default":"outline",onClick:()=>u({...o,type:"email"}),className:o.type==="email"?"bg-purple-600":"border-slate-600 text-slate-300",children:[s.jsx(Nl,{className:"w-4 h-4 mr-2"}),"Email Address"]}),s.jsxs(ye,{variant:o.type==="phone"?"default":"outline",onClick:()=>u({...o,type:"phone"}),className:o.type==="phone"?"bg-purple-600":"border-slate-600 text-slate-300",children:[s.jsx(Ql,{className:"w-4 h-4 mr-2"}),"Phone Number"]})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{className:"text-slate-300",children:o.type==="email"?"Email Address":"Phone Number"}),s.jsx($t,{placeholder:o.type==="email"?"Enter email address":"Enter phone number",value:o.value,onChange:x=>u({...o,value:x.target.value}),className:"bg-slate-700/50 border-slate-600 text-white"})]}),s.jsxs(ye,{onClick:g,className:"w-full bg-purple-600 hover:bg-purple-700",children:[s.jsx(Nt,{className:"w-4 h-4 mr-2"}),"Start Monitoring"]}),s.jsxs(ti,{className:"border-blue-500/50 bg-blue-500/10",children:[s.jsx(Ge,{className:"h-4 w-4"}),s.jsxs(ai,{className:"text-blue-300",children:[s.jsx("strong",{children:"Privacy Notice:"})," We only store hashed versions of your identities and never share your personal information with third parties."]})]})]})]}),s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsx(me,{children:s.jsx(he,{className:"text-white",children:"What We Monitor"})}),s.jsxs(oe,{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(Za,{className:"h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Global Breach Databases"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Monitor major data breaches worldwide"})]})]}),s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(Nt,{className:"h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Dark Web Monitoring"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Scan underground markets and forums"})]})]}),s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(Cu,{className:"h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Real-time Alerts"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Instant notifications when breaches are detected"})]})]})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsx(me,{children:s.jsx(he,{className:"text-white",children:"Security Features"})}),s.jsxs(oe,{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(Ge,{className:"h-5 w-5 text-green-400 mt-0.5 flex-shrink-0"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Encrypted Storage"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"All data encrypted with AES-256"})]})]}),s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(At,{className:"h-5 w-5 text-green-400 mt-0.5 flex-shrink-0"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Privacy First"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"No data sharing with third parties"})]})]}),s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(ei,{className:"h-5 w-5 text-green-400 mt-0.5 flex-shrink-0"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Risk Assessment"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Advanced algorithms for threat analysis"})]})]})]})]})]})]}),s.jsx(qt,{value:"reports",className:"space-y-6",children:s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsx(me,{children:s.jsx(he,{className:"text-white",children:"Monitoring Summary"})}),s.jsxs(oe,{className:"space-y-4",children:[s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-slate-300",children:"Total Identities"}),s.jsx("span",{className:"text-white font-medium",children:b.total})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-slate-300",children:"Active Monitoring"}),s.jsx("span",{className:"text-green-400 font-medium",children:b.total})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-slate-300",children:"Breaches Detected"}),s.jsx("span",{className:"text-red-400 font-medium",children:b.breaches})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-slate-300",children:"High Risk Items"}),s.jsx("span",{className:"text-orange-400 font-medium",children:b.highRisk})]})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsx(me,{children:s.jsx(he,{className:"text-white",children:"Recent Activity"})}),s.jsxs(oe,{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),s.jsx("span",{className:"text-slate-300 text-sm",children:"All identities checked - 2 hours ago"})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-yellow-400 rounded-full"}),s.jsx("span",{className:"text-slate-300 text-sm",children:"New breach detected - 1 day ago"})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),s.jsx("span",{className:"text-slate-300 text-sm",children:"Identity added - 3 days ago"})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),s.jsx("span",{className:"text-slate-300 text-sm",children:"Weekly scan completed - 1 week ago"})]})]})]})]})})]})]})})};var Ou="Progress",Uu=100,[vN,YN]=si(Ou),[bN,NN]=vN(Ou),ix=N.forwardRef((i,c)=>{const{__scopeProgress:o,value:u=null,max:d,getValueLabel:h=jN,...y}=i;(d||d===0)&&!Wh(d)&&console.error(wN(`${d}`,"Progress"));const v=Wh(d)?d:Uu;u!==null&&!Fh(u,v)&&console.error(SN(`${u}`,"Progress"));const g=Fh(u,v)?u:null,m=Or(g)?h(g,v):void 0;return s.jsx(bN,{scope:o,value:g,max:v,children:s.jsx(xa.div,{"aria-valuemax":v,"aria-valuemin":0,"aria-valuenow":Or(g)?g:void 0,"aria-valuetext":m,role:"progressbar","data-state":ox(g,v),"data-value":g??void 0,"data-max":v,...y,ref:c})})});ix.displayName=Ou;var rx="ProgressIndicator",cx=N.forwardRef((i,c)=>{const{__scopeProgress:o,...u}=i,d=NN(rx,o);return s.jsx(xa.div,{"data-state":ox(d.value,d.max),"data-value":d.value??void 0,"data-max":d.max,...u,ref:c})});cx.displayName=rx;function jN(i,c){return`${Math.round(i/c*100)}%`}function ox(i,c){return i==null?"indeterminate":i===c?"complete":"loading"}function Or(i){return typeof i=="number"}function Wh(i){return Or(i)&&!isNaN(i)&&i>0}function Fh(i,c){return Or(i)&&!isNaN(i)&&i<=c&&i>=0}function wN(i,c){return`Invalid prop \`max\` of value \`${i}\` supplied to \`${c}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Uu}\`.`}function SN(i,c){return`Invalid prop \`value\` of value \`${i}\` supplied to \`${c}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${Uu} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var EN=ix,TN=cx;function Ih({className:i,value:c,...o}){return s.jsx(EN,{"data-slot":"progress",className:ct("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",i),...o,children:s.jsx(TN,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(c||0)}%)`}})})}function AN({className:i,...c}){return s.jsx("textarea",{"data-slot":"textarea",className:ct("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",i),...c})}function RN(i){const c=N.useRef({value:i,previous:i});return N.useMemo(()=>(c.current.value!==i&&(c.current.previous=c.current.value,c.current.value=i),c.current.previous),[i])}function CN(i){const[c,o]=N.useState(void 0);return Pn(()=>{if(i){o({width:i.offsetWidth,height:i.offsetHeight});const u=new ResizeObserver(d=>{if(!Array.isArray(d)||!d.length)return;const h=d[0];let y,v;if("borderBoxSize"in h){const g=h.borderBoxSize,m=Array.isArray(g)?g[0]:g;y=m.inlineSize,v=m.blockSize}else y=i.offsetWidth,v=i.offsetHeight;o({width:y,height:v})});return u.observe(i,{box:"border-box"}),()=>u.unobserve(i)}else o(void 0)},[i]),c}var Yr="Switch",[MN,VN]=si(Yr),[kN,_N]=MN(Yr),ux=N.forwardRef((i,c)=>{const{__scopeSwitch:o,name:u,checked:d,defaultChecked:h,required:y,disabled:v,value:g="on",onCheckedChange:m,form:b,...x}=i,[j,_]=N.useState(null),E=Ls(c,O=>_(O)),L=N.useRef(!1),D=j?b||!!j.closest("form"):!0,[B,Q]=zu({prop:d,defaultProp:h??!1,onChange:m,caller:Yr});return s.jsxs(kN,{scope:o,checked:B,disabled:v,children:[s.jsx(xa.button,{type:"button",role:"switch","aria-checked":B,"aria-required":y,"data-state":hx(B),"data-disabled":v?"":void 0,disabled:v,value:g,...x,ref:E,onClick:Ea(i.onClick,O=>{Q(P=>!P),D&&(L.current=O.isPropagationStopped(),L.current||O.stopPropagation())})}),D&&s.jsx(mx,{control:j,bubbles:!L.current,name:u,value:g,checked:B,required:y,disabled:v,form:b,style:{transform:"translateX(-100%)"}})]})});ux.displayName=Yr;var dx="SwitchThumb",fx=N.forwardRef((i,c)=>{const{__scopeSwitch:o,...u}=i,d=_N(dx,o);return s.jsx(xa.span,{"data-state":hx(d.checked),"data-disabled":d.disabled?"":void 0,...u,ref:c})});fx.displayName=dx;var zN="SwitchBubbleInput",mx=N.forwardRef(({__scopeSwitch:i,control:c,checked:o,bubbles:u=!0,...d},h)=>{const y=N.useRef(null),v=Ls(y,h),g=RN(o),m=CN(c);return N.useEffect(()=>{const b=y.current;if(!b)return;const x=window.HTMLInputElement.prototype,_=Object.getOwnPropertyDescriptor(x,"checked").set;if(g!==o&&_){const E=new Event("click",{bubbles:u});_.call(b,o),b.dispatchEvent(E)}},[g,o,u]),s.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o,...d,tabIndex:-1,ref:v,style:{...d.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});mx.displayName=zN;function hx(i){return i?"checked":"unchecked"}var DN=ux,ON=fx;function Tt({className:i,...c}){return s.jsx(DN,{"data-slot":"switch",className:ct("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",i),...c,children:s.jsx(ON,{"data-slot":"switch-thumb",className:ct("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}const UN=()=>{const[i,c]=N.useState([{id:1,service:"Gmail",username:"<EMAIL>",strength:"strong",score:85,isCompromised:!1,lastChecked:"2 hours ago",recommendations:["Consider using a longer password","Add special characters"]},{id:2,service:"Facebook",username:"<EMAIL>",strength:"weak",score:35,isCompromised:!1,lastChecked:"1 day ago",recommendations:["Use a mix of uppercase and lowercase","Add numbers and symbols","Increase length to at least 12 characters"]},{id:3,service:"LinkedIn",username:"<EMAIL>",strength:"compromised",score:0,isCompromised:!0,lastChecked:"3 hours ago",recommendations:["Change password immediately","Enable two-factor authentication","Use a unique password"]}]),[o,u]=N.useState(""),[d,h]=N.useState(""),[y,v]=N.useState(!1),[g,m]=N.useState(null),[b,x]=N.useState({length:16,includeUppercase:!0,includeLowercase:!0,includeNumbers:!0,includeSymbols:!0,excludeSimilar:!0}),j=O=>{const P=O.length,J=/[A-Z]/.test(O),ie=/[a-z]/.test(O),pe=/\d/.test(O),K=/[!@#$%^&*(),.?":{}|<>]/.test(O);let I=0,Ee="very_weak",Te=[],Ae=[];return P>=12?I+=25:P>=8?I+=15:Te.push("Password is too short"),J?I+=15:Ae.push("Add uppercase letters"),ie?I+=15:Ae.push("Add lowercase letters"),pe?I+=15:Ae.push("Add numbers"),K?I+=20:Ae.push("Add special characters"),/123|abc|qwe/i.test(O)&&(I-=10,Te.push("Contains common patterns")),I>=80?Ee="very_strong":I>=65?Ee="strong":I>=50?Ee="good":I>=35?Ee="fair":I>=20&&(Ee="weak"),{score:Math.max(0,I),strength:Ee,issues:Te,suggestions:Ae,entropy:P*4,timeToCrack:I>70?"Centuries":I>50?"Years":I>30?"Days":"Hours"}},_=()=>{const{length:O,includeUppercase:P,includeLowercase:J,includeNumbers:ie,includeSymbols:pe,excludeSimilar:K}=b;let I="";P&&(I+=K?"ABCDEFGHJKLMNPQRSTUVWXYZ":"ABCDEFGHIJKLMNOPQRSTUVWXYZ"),J&&(I+=K?"abcdefghjkmnpqrstuvwxyz":"abcdefghijklmnopqrstuvwxyz"),ie&&(I+=K?"23456789":"0123456789"),pe&&(I+="!@#$%^&*()_+-=[]{}|;:,.<>?");let Ee="";for(let Te=0;Te<O;Te++)Ee+=I.charAt(Math.floor(Math.random()*I.length));return h(Ee),Ee},E=()=>{const O=["apple","bridge","castle","dragon","eagle","forest","guitar","harbor","island","jungle"],P=[];for(let ie=0;ie<4;ie++)P.push(O[Math.floor(Math.random()*O.length)]);const J=P.join("-")+Math.floor(Math.random()*100);return h(J),J},L=O=>{navigator.clipboard.writeText(O),Bt.success("Copied to clipboard!")},D=O=>{switch(O){case"very_strong":return"text-green-400";case"strong":return"text-green-400";case"good":return"text-yellow-400";case"fair":return"text-yellow-400";case"weak":return"text-red-400";case"very_weak":return"text-red-400";case"compromised":return"text-red-400";default:return"text-slate-400"}},B=(O,P)=>{if(P)return s.jsx(Ve,{variant:"destructive",className:"bg-red-500/20 text-red-300",children:"Compromised"});switch(O){case"very_strong":return s.jsx(Ve,{className:"bg-green-500/20 text-green-300 border-green-500/50",children:"Very Strong"});case"strong":return s.jsx(Ve,{className:"bg-green-500/20 text-green-300 border-green-500/50",children:"Strong"});case"good":return s.jsx(Ve,{className:"bg-yellow-500/20 text-yellow-300 border-yellow-500/50",children:"Good"});case"fair":return s.jsx(Ve,{className:"bg-yellow-500/20 text-yellow-300 border-yellow-500/50",children:"Fair"});case"weak":return s.jsx(Ve,{variant:"destructive",className:"bg-red-500/20 text-red-300",children:"Weak"});default:return s.jsx(Ve,{variant:"destructive",className:"bg-red-500/20 text-red-300",children:"Very Weak"})}};N.useEffect(()=>{if(o){const O=j(o);m(O)}else m(null)},[o]);const Q={totalPasswords:i.length,strongPasswords:i.filter(O=>["strong","very_strong"].includes(O.strength)).length,weakPasswords:i.filter(O=>["weak","very_weak","fair"].includes(O.strength)).length,compromisedPasswords:i.filter(O=>O.isCompromised).length,averageScore:Math.round(i.reduce((O,P)=>O+P.score,0)/i.length)};return s.jsx(li,{children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Password Security"}),s.jsx("p",{className:"text-slate-400",children:"Analyze, generate, and manage secure passwords to protect your accounts"})]}),s.jsxs(ye,{className:"bg-purple-600 hover:bg-purple-700 mt-4 md:mt-0",children:[s.jsx(pu,{className:"w-4 h-4 mr-2"}),"Add Password"]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Total Passwords"}),s.jsx(Us,{className:"h-4 w-4 text-blue-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:Q.totalPasswords}),s.jsx("p",{className:"text-xs text-slate-400",children:"Monitored accounts"})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Strong Passwords"}),s.jsx(Ge,{className:"h-4 w-4 text-green-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:Q.strongPasswords}),s.jsx("p",{className:"text-xs text-slate-400",children:"Well protected"})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Weak Passwords"}),s.jsx(na,{className:"h-4 w-4 text-yellow-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:Q.weakPasswords}),s.jsx("p",{className:"text-xs text-slate-400",children:"Need improvement"})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Compromised"}),s.jsx(vb,{className:"h-4 w-4 text-red-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:Q.compromisedPasswords}),s.jsx("p",{className:"text-xs text-slate-400",children:"Immediate action needed"})]})]})]}),s.jsxs(qr,{defaultValue:"analyzer",className:"space-y-6",children:[s.jsxs(Gr,{className:"grid w-full grid-cols-4 bg-slate-800/50",children:[s.jsx(Ht,{value:"analyzer",className:"data-[state=active]:bg-purple-600",children:"Password Analyzer"}),s.jsx(Ht,{value:"generator",className:"data-[state=active]:bg-purple-600",children:"Password Generator"}),s.jsx(Ht,{value:"vault",className:"data-[state=active]:bg-purple-600",children:"Password Vault"}),s.jsx(Ht,{value:"tips",className:"data-[state=active]:bg-purple-600",children:"Security Tips"})]}),s.jsx(qt,{value:"analyzer",className:"space-y-6",children:s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsxs(he,{className:"text-white flex items-center",children:[s.jsx(Ge,{className:"w-5 h-5 mr-2 text-purple-400"}),"Password Strength Analyzer"]}),s.jsx(xt,{children:"Check if your password is strong and secure against common attacks"})]}),s.jsxs(oe,{className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{htmlFor:"password-input",className:"text-slate-300",children:"Enter Password to Analyze"}),s.jsxs("div",{className:"relative",children:[s.jsx($t,{id:"password-input",type:y?"text":"password",placeholder:"Enter your password here...",value:o,onChange:O=>u(O.target.value),className:"bg-slate-700/50 border-slate-600 text-white pr-10"}),s.jsx(ye,{type:"button",variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white",onClick:()=>v(!y),children:y?s.jsx(N0,{className:"h-4 w-4"}):s.jsx(Nt,{className:"h-4 w-4"})})]})]}),g&&s.jsxs("div",{className:"space-y-4 p-4 bg-slate-700/30 rounded-lg border border-slate-600",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-slate-300",children:"Strength Score"}),s.jsxs("span",{className:`text-2xl font-bold ${D(g.strength)}`,children:[g.score,"/100"]})]}),s.jsx(Ih,{value:g.score,className:"h-3"}),s.jsxs("div",{className:"grid md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("h4",{className:"text-white font-medium mb-2",children:"Analysis Results"}),s.jsxs("div",{className:"space-y-2 text-sm",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-400",children:"Strength Level:"}),s.jsx("span",{className:D(g.strength),children:g.strength.replace("_"," ").toUpperCase()})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-400",children:"Entropy:"}),s.jsxs("span",{className:"text-white",children:[g.entropy," bits"]})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-400",children:"Time to Crack:"}),s.jsx("span",{className:"text-white",children:g.timeToCrack})]})]})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"text-white font-medium mb-2",children:"Recommendations"}),s.jsx("div",{className:"space-y-1",children:g.suggestions.map((O,P)=>s.jsxs("div",{className:"flex items-start space-x-2",children:[s.jsx(At,{className:"h-3 w-3 text-green-400 mt-0.5 flex-shrink-0"}),s.jsx("span",{className:"text-xs text-slate-300",children:O})]},P))})]})]}),g.issues.length>0&&s.jsxs(ti,{className:"border-red-500/50 bg-red-500/10",children:[s.jsx(na,{className:"h-4 w-4"}),s.jsxs(ai,{className:"text-red-300",children:[s.jsx("strong",{children:"Issues found:"})," ",g.issues.join(", ")]})]})]})]})]})}),s.jsx(qt,{value:"generator",className:"space-y-6",children:s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsxs(he,{className:"text-white flex items-center",children:[s.jsx(Bs,{className:"w-5 h-5 mr-2 text-yellow-400"}),"Password Generator"]}),s.jsx(xt,{children:"Generate cryptographically secure passwords"})]}),s.jsx(oe,{className:"space-y-4",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsxs(at,{className:"text-slate-300",children:["Length: ",b.length]}),s.jsx("input",{type:"range",min:"8",max:"64",value:b.length,onChange:O=>x({...b,length:parseInt(O.target.value)}),className:"w-full"})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(Tt,{checked:b.includeUppercase,onCheckedChange:O=>x({...b,includeUppercase:O})}),s.jsx(at,{className:"text-slate-300 text-sm",children:"Uppercase"})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(Tt,{checked:b.includeLowercase,onCheckedChange:O=>x({...b,includeLowercase:O})}),s.jsx(at,{className:"text-slate-300 text-sm",children:"Lowercase"})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(Tt,{checked:b.includeNumbers,onCheckedChange:O=>x({...b,includeNumbers:O})}),s.jsx(at,{className:"text-slate-300 text-sm",children:"Numbers"})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(Tt,{checked:b.includeSymbols,onCheckedChange:O=>x({...b,includeSymbols:O})}),s.jsx(at,{className:"text-slate-300 text-sm",children:"Symbols"})]})]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsxs(ye,{onClick:_,className:"flex-1 bg-purple-600 hover:bg-purple-700",children:[s.jsx(Vh,{className:"w-4 h-4 mr-2"}),"Generate Password"]}),s.jsx(ye,{onClick:E,variant:"outline",className:"border-slate-600 text-slate-300",children:"Passphrase"})]})]})})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsx(he,{className:"text-white",children:"Generated Password"}),s.jsx(xt,{children:"Your secure password is ready to use"})]}),s.jsx(oe,{className:"space-y-4",children:d&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"relative",children:[s.jsx(AN,{value:d,readOnly:!0,className:"bg-slate-700/50 border-slate-600 text-white font-mono text-lg resize-none",rows:3}),s.jsx(ye,{size:"sm",variant:"ghost",className:"absolute top-2 right-2 text-slate-400 hover:text-white",onClick:()=>L(d),children:s.jsx(Yh,{className:"h-4 w-4"})})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-400",children:"Length:"}),s.jsx("span",{className:"text-white",children:d.length})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-400",children:"Strength:"}),s.jsx("span",{className:"text-green-400",children:"Very Strong"})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-400",children:"Entropy:"}),s.jsxs("span",{className:"text-white",children:[d.length*4," bits"]})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-slate-400",children:"Time to Crack:"}),s.jsx("span",{className:"text-white",children:"Centuries"})]})]})]}),s.jsxs(ye,{className:"w-full bg-green-600 hover:bg-green-700",onClick:()=>L(d),children:[s.jsx(Yh,{className:"w-4 h-4 mr-2"}),"Copy to Clipboard"]})]})})]})]})}),s.jsx(qt,{value:"vault",className:"space-y-6",children:s.jsx("div",{className:"space-y-4",children:i.map(O=>s.jsx(ce,{className:"bg-slate-800/50 border-slate-700",children:s.jsxs(oe,{className:"p-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center",children:s.jsx(Us,{className:"h-6 w-6 text-purple-400"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-white font-medium",children:O.service}),s.jsx("p",{className:"text-slate-400 text-sm",children:O.username}),s.jsxs("p",{className:"text-slate-500 text-xs",children:["Last checked: ",O.lastChecked]})]})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("div",{className:"text-right",children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[s.jsx("span",{className:`text-lg font-bold ${D(O.strength)}`,children:O.score}),B(O.strength,O.isCompromised)]}),s.jsx(Ih,{value:O.score,className:"w-24 h-2"})]}),s.jsx(ye,{variant:"ghost",size:"sm",className:"text-slate-400 hover:text-white",children:s.jsx(Vh,{className:"h-4 w-4"})})]})]}),O.recommendations.length>0&&s.jsxs("div",{className:"mt-4 p-3 bg-slate-700/30 rounded-lg",children:[s.jsx("h4",{className:"text-white text-sm font-medium mb-2",children:"Recommendations:"}),s.jsx("ul",{className:"space-y-1",children:O.recommendations.map((P,J)=>s.jsxs("li",{className:"text-slate-300 text-xs flex items-start space-x-2",children:[s.jsx(At,{className:"h-3 w-3 text-yellow-400 mt-0.5 flex-shrink-0"}),s.jsx("span",{children:P})]},J))})]})]})},O.id))})}),s.jsx(qt,{value:"tips",className:"space-y-6",children:s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsx(me,{children:s.jsx(he,{className:"text-white",children:"Password Best Practices"})}),s.jsxs(oe,{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(At,{className:"h-5 w-5 text-green-400 mt-0.5 flex-shrink-0"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Use unique passwords for every account"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Never reuse passwords across multiple services"})]})]}),s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(At,{className:"h-5 w-5 text-green-400 mt-0.5 flex-shrink-0"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Make passwords at least 12 characters long"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Longer passwords are exponentially harder to crack"})]})]}),s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(At,{className:"h-5 w-5 text-green-400 mt-0.5 flex-shrink-0"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Use a mix of character types"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Include uppercase, lowercase, numbers, and symbols"})]})]}),s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(At,{className:"h-5 w-5 text-green-400 mt-0.5 flex-shrink-0"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Avoid personal information"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Don't use names, birthdays, or other personal data"})]})]})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsx(me,{children:s.jsx(he,{className:"text-white",children:"Advanced Security"})}),s.jsxs(oe,{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(Ge,{className:"h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Enable two-factor authentication"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Add an extra layer of security to your accounts"})]})]}),s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(Ge,{className:"h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Use a password manager"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Generate and store unique passwords securely"})]})]}),s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(Ge,{className:"h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Regular security audits"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Check for compromised passwords regularly"})]})]}),s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx(Ge,{className:"h-5 w-5 text-purple-400 mt-0.5 flex-shrink-0"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Monitor for breaches"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Stay informed about data breaches affecting your accounts"})]})]})]})]})]})})]})]})})},BN=()=>{const[i]=N.useState([{id:1,type:"breach",severity:"critical",title:"New Data Breach Detected",description:"<NAME_EMAIL> was found in the recent LinkedIn breach affecting 700M users",identity:"<EMAIL>",identityType:"email",timestamp:"2024-01-15T10:30:00Z",source:"LinkedIn",status:"unread",actionRequired:!0,recommendations:["Change your LinkedIn password immediately","Enable two-factor authentication","Monitor your account for suspicious activity"]},{id:2,type:"suspicious_activity",severity:"high",title:"Suspicious Login Attempt",description:"Multiple failed login attempts detected for your monitored email address",identity:"<EMAIL>",identityType:"email",timestamp:"2024-01-14T15:45:00Z",source:"Security Monitoring",status:"acknowledged",actionRequired:!0,recommendations:["Review recent login activity","Change password if you suspect compromise","Enable account alerts"]},{id:3,type:"identity_exposure",severity:"medium",title:"Phone Number Found on Dark Web",description:"Your phone number +**************** was found in underground forums",identity:"+****************",identityType:"phone",timestamp:"2024-01-13T09:20:00Z",source:"Dark Web Monitoring",status:"read",actionRequired:!1,recommendations:["Be cautious of unexpected calls or texts","Consider changing your phone number if harassment occurs","Report suspicious communications"]},{id:4,type:"password_compromise",severity:"high",title:"Password Found in Breach Database",description:"A password associated with your email was found in a known breach database",identity:"<EMAIL>",identityType:"email",timestamp:"2024-01-12T14:10:00Z",source:"HaveIBeenPwned",status:"resolved",actionRequired:!1,recommendations:["Password has been changed","Continue monitoring for suspicious activity","Use unique passwords for all accounts"]},{id:5,type:"security_tip",severity:"low",title:"Weekly Security Reminder",description:"Time for your weekly security check-up and password review",identity:null,identityType:null,timestamp:"2024-01-11T08:00:00Z",source:"Identity Guardian",status:"read",actionRequired:!1,recommendations:["Review your security dashboard","Check for software updates","Review recent account activity"]}]),[c]=N.useState({totalScans:1247,identitiesMonitored:5,breachesDetected:3,alertsThisWeek:7,lastScanTime:"2 hours ago",uptime:"99.9%",avgResponseTime:"< 1 minute"}),o=y=>{switch(y){case"critical":return"bg-red-500/20 text-red-300 border-red-500/50";case"high":return"bg-orange-500/20 text-orange-300 border-orange-500/50";case"medium":return"bg-yellow-500/20 text-yellow-300 border-yellow-500/50";case"low":return"bg-blue-500/20 text-blue-300 border-blue-500/50";default:return"bg-slate-500/20 text-slate-300 border-slate-500/50"}},u=y=>{switch(y){case"unread":return"bg-red-500/20 text-red-300 border-red-500/50";case"acknowledged":return"bg-yellow-500/20 text-yellow-300 border-yellow-500/50";case"read":return"bg-blue-500/20 text-blue-300 border-blue-500/50";case"resolved":return"bg-green-500/20 text-green-300 border-green-500/50";default:return"bg-slate-500/20 text-slate-300 border-slate-500/50"}},d=y=>{switch(y){case"breach":return s.jsx(na,{className:"h-5 w-5 text-red-400"});case"suspicious_activity":return s.jsx(Nt,{className:"h-5 w-5 text-orange-400"});case"identity_exposure":return s.jsx(Za,{className:"h-5 w-5 text-yellow-400"});case"password_compromise":return s.jsx(Ge,{className:"h-5 w-5 text-red-400"});case"security_tip":return s.jsx(_r,{className:"h-5 w-5 text-blue-400"});default:return s.jsx($n,{className:"h-5 w-5 text-slate-400"})}},h=y=>{const v=new Date(y),m=Math.floor((new Date-v)/(1e3*60*60));return m<1?"Just now":m<24?`${m} hours ago`:m<48?"Yesterday":v.toLocaleDateString()};return s.jsx(li,{children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Security Monitoring"}),s.jsx("p",{className:"text-slate-400",children:"Real-time monitoring and alerts for your digital identities"})]}),s.jsx("div",{className:"flex items-center space-x-2 mt-4 md:mt-0",children:s.jsxs(Ve,{variant:"outline",className:"border-green-500/50 text-green-300",children:[s.jsx($n,{className:"w-3 h-3 mr-1"}),"Active Monitoring"]})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Total Scans"}),s.jsx($n,{className:"h-4 w-4 text-blue-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:c.totalScans.toLocaleString()}),s.jsx("p",{className:"text-xs text-slate-400",children:"Completed successfully"})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Identities Monitored"}),s.jsx(Nt,{className:"h-4 w-4 text-green-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:c.identitiesMonitored}),s.jsx("p",{className:"text-xs text-slate-400",children:"Active protection"})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Breaches Detected"}),s.jsx(na,{className:"h-4 w-4 text-red-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:c.breachesDetected}),s.jsx("p",{className:"text-xs text-slate-400",children:"Requiring attention"})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"System Uptime"}),s.jsx(ei,{className:"h-4 w-4 text-green-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:c.uptime}),s.jsx("p",{className:"text-xs text-slate-400",children:"Last 30 days"})]})]})]}),s.jsxs(qr,{defaultValue:"alerts",className:"space-y-6",children:[s.jsxs(Gr,{className:"grid w-full grid-cols-3 bg-slate-800/50",children:[s.jsx(Ht,{value:"alerts",className:"data-[state=active]:bg-purple-600",children:"Security Alerts"}),s.jsx(Ht,{value:"activity",className:"data-[state=active]:bg-purple-600",children:"Monitoring Activity"}),s.jsx(Ht,{value:"settings",className:"data-[state=active]:bg-purple-600",children:"Alert Settings"})]}),s.jsxs(qt,{value:"alerts",className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs(ye,{variant:"outline",size:"sm",className:"border-slate-600 text-slate-300",children:[s.jsx(db,{className:"w-4 h-4 mr-2"}),"Filter"]}),s.jsxs(ye,{variant:"outline",size:"sm",className:"border-slate-600 text-slate-300",children:[s.jsx(tb,{className:"w-4 h-4 mr-2"}),"Date Range"]})]}),s.jsxs(ye,{variant:"outline",size:"sm",className:"border-slate-600 text-slate-300",children:[s.jsx(Mu,{className:"w-4 h-4 mr-2"}),"Export"]})]}),s.jsx("div",{className:"space-y-4",children:i.map(y=>s.jsx(ce,{className:"bg-slate-800/50 border-slate-700",children:s.jsxs(oe,{className:"p-6",children:[s.jsxs("div",{className:"flex items-start justify-between",children:[s.jsxs("div",{className:"flex items-start space-x-4 flex-1",children:[s.jsx("div",{className:"w-12 h-12 bg-slate-700/50 rounded-lg flex items-center justify-center",children:d(y.type)}),s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[s.jsx(Ve,{className:o(y.severity),children:y.severity}),s.jsx(Ve,{className:u(y.status),children:y.status}),y.actionRequired&&s.jsx(Ve,{variant:"outline",className:"border-orange-500/50 text-orange-300",children:"Action Required"})]}),s.jsx("h3",{className:"text-white font-medium text-lg mb-1",children:y.title}),s.jsx("p",{className:"text-slate-300 mb-2",children:y.description}),s.jsxs("div",{className:"flex items-center space-x-4 text-sm text-slate-400",children:[y.identity&&s.jsxs("div",{className:"flex items-center space-x-1",children:[y.identityType==="email"?s.jsx(Nl,{className:"h-4 w-4"}):s.jsx(Ql,{className:"h-4 w-4"}),s.jsx("span",{children:y.identity})]}),s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx(Cu,{className:"h-4 w-4"}),s.jsx("span",{children:h(y.timestamp)})]}),s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx(Za,{className:"h-4 w-4"}),s.jsx("span",{children:y.source})]})]})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[y.actionRequired&&s.jsx(ye,{size:"sm",className:"bg-orange-600 hover:bg-orange-700",children:"Take Action"}),s.jsx(ye,{variant:"ghost",size:"sm",className:"text-slate-400 hover:text-white",children:"Mark as Read"})]})]}),y.recommendations&&y.recommendations.length>0&&s.jsxs("div",{className:"mt-4 p-4 bg-slate-700/30 rounded-lg border border-slate-600",children:[s.jsx("h4",{className:"text-white font-medium mb-3",children:"Recommended Actions:"}),s.jsx("div",{className:"space-y-2",children:y.recommendations.map((v,g)=>s.jsxs("div",{className:"flex items-start space-x-2",children:[s.jsx(At,{className:"h-4 w-4 text-green-400 mt-0.5 flex-shrink-0"}),s.jsx("span",{className:"text-slate-300 text-sm",children:v})]},g))})]})]})},y.id))})]}),s.jsxs(qt,{value:"activity",className:"space-y-6",children:[s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsx(he,{className:"text-white",children:"Monitoring Status"}),s.jsx(xt,{children:"Current system status and performance"})]}),s.jsxs(oe,{className:"space-y-4",children:[s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-slate-300",children:"System Status"}),s.jsxs(Ve,{className:"bg-green-500/20 text-green-300 border-green-500/50",children:[s.jsx(At,{className:"w-3 h-3 mr-1"}),"Operational"]})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-slate-300",children:"Last Scan"}),s.jsx("span",{className:"text-white",children:c.lastScanTime})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-slate-300",children:"Response Time"}),s.jsx("span",{className:"text-white",children:c.avgResponseTime})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-slate-300",children:"Alerts This Week"}),s.jsx("span",{className:"text-white",children:c.alertsThisWeek})]})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsx(he,{className:"text-white",children:"Recent Activity"}),s.jsx(xt,{children:"Latest monitoring events and scans"})]}),s.jsxs(oe,{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),s.jsx("span",{className:"text-slate-300 text-sm",children:"Identity scan completed - 2 hours ago"})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-red-400 rounded-full"}),s.jsx("span",{className:"text-slate-300 text-sm",children:"New breach detected - 6 hours ago"})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),s.jsx("span",{className:"text-slate-300 text-sm",children:"Dark web scan initiated - 12 hours ago"})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-yellow-400 rounded-full"}),s.jsx("span",{className:"text-slate-300 text-sm",children:"Suspicious activity detected - 1 day ago"})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),s.jsx("span",{className:"text-slate-300 text-sm",children:"Weekly security report generated - 3 days ago"})]})]})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsx(he,{className:"text-white",children:"Monitoring Coverage"}),s.jsx(xt,{children:"What we're actively monitoring for you"})]}),s.jsx(oe,{children:s.jsxs("div",{className:"grid md:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-3",children:s.jsx(Za,{className:"h-8 w-8 text-purple-400"})}),s.jsx("h4",{className:"text-white font-medium mb-2",children:"Data Breaches"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Monitoring 50+ million known breaches and new incidents"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-3",children:s.jsx(Nt,{className:"h-8 w-8 text-purple-400"})}),s.jsx("h4",{className:"text-white font-medium mb-2",children:"Dark Web"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Scanning underground forums and marketplaces 24/7"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-3",children:s.jsx(Ge,{className:"h-8 w-8 text-purple-400"})}),s.jsx("h4",{className:"text-white font-medium mb-2",children:"Threat Intelligence"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Real-time threat feeds from security researchers"})]})]})})]})]}),s.jsxs(qt,{value:"settings",className:"space-y-6",children:[s.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsx(he,{className:"text-white",children:"Notification Preferences"}),s.jsx(xt,{children:"Configure how you receive security alerts"})]}),s.jsxs(oe,{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Email Notifications"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Receive alerts via email"})]}),s.jsx(ye,{variant:"outline",size:"sm",className:"border-green-500/50 text-green-300",children:"Enabled"})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"SMS Alerts"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Critical alerts via text message"})]}),s.jsx(ye,{variant:"outline",size:"sm",className:"border-green-500/50 text-green-300",children:"Enabled"})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Push Notifications"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Browser notifications"})]}),s.jsx(ye,{variant:"outline",size:"sm",className:"border-slate-600 text-slate-300",children:"Disabled"})]})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsx(he,{className:"text-white",children:"Alert Thresholds"}),s.jsx(xt,{children:"Set sensitivity levels for different alert types"})]}),s.jsxs(oe,{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Data Breaches"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"All severity levels"})]}),s.jsx(Ve,{className:"bg-red-500/20 text-red-300 border-red-500/50",children:"High Sensitivity"})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Suspicious Activity"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Medium and above"})]}),s.jsx(Ve,{className:"bg-yellow-500/20 text-yellow-300 border-yellow-500/50",children:"Medium Sensitivity"})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Security Tips"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Weekly digest"})]}),s.jsx(Ve,{className:"bg-blue-500/20 text-blue-300 border-blue-500/50",children:"Low Sensitivity"})]})]})]})]}),s.jsxs(ti,{className:"border-blue-500/50 bg-blue-500/10",children:[s.jsx(_r,{className:"h-4 w-4"}),s.jsxs(ai,{className:"text-blue-300",children:[s.jsx("strong",{children:"Tip:"})," We recommend keeping high sensitivity for data breaches and password compromises to ensure you're notified immediately of critical security events."]})]})]})]})]})})},LN=()=>{const[i,c]=N.useState(""),[o,u]=N.useState("all"),[d,h]=N.useState(new Set),y=[{id:"all",name:"All Tips",icon:Kn},{id:"passwords",name:"Password Security",icon:jl},{id:"phishing",name:"Phishing Protection",icon:Nl},{id:"mobile",name:"Mobile Security",icon:Ql},{id:"browsing",name:"Safe Browsing",icon:Za},{id:"social",name:"Social Engineering",icon:ku},{id:"general",name:"General Awareness",icon:Ge}],v=[{id:1,title:"Use Strong, Unique Passwords",category:"passwords",priority:"critical",readTime:3,likes:245,content:`Creating strong, unique passwords is your first line of defense against cyber attacks. A strong password should be at least 12 characters long and include a mix of uppercase letters, lowercase letters, numbers, and special characters.

Never reuse passwords across multiple accounts. If one account gets compromised, attackers could access all your other accounts using the same password.

Consider using a passphrase - a series of random words that are easy for you to remember but hard for others to guess. For example: "Coffee-Mountain-Purple-42" is much stronger than "P@ssw0rd123".`,actionItems:["Audit all your current passwords","Change any weak or reused passwords","Set up a password manager","Enable two-factor authentication where possible"],tags:["passwords","authentication","security"]},{id:2,title:"Recognize Phishing Attempts",category:"phishing",priority:"critical",readTime:4,likes:189,content:`Phishing is one of the most common ways attackers steal personal information. These attacks often come through email, text messages, or fake websites that look legitimate.

Warning signs of phishing:
- Urgent language ("Act now!" or "Your account will be closed!")
- Requests for personal information via email
- Suspicious sender addresses that don't match the claimed organization
- Links that don't match the legitimate website URL
- Poor grammar or spelling errors

Always verify requests independently. If you receive an email claiming to be from your bank, don't click the link - instead, go directly to your bank's website or call them directly.`,actionItems:["Learn to identify suspicious emails","Verify requests through official channels","Never click suspicious links","Report phishing attempts to your IT department"],tags:["phishing","email","social engineering"]},{id:3,title:"Enable Two-Factor Authentication",category:"passwords",priority:"high",readTime:2,likes:156,content:`Two-factor authentication (2FA) adds an extra layer of security to your accounts by requiring something you know (your password) and something you have (your phone or a security key).

Even if someone steals your password, they won't be able to access your account without the second factor. This makes your accounts significantly more secure.

Types of 2FA:
- SMS codes (better than nothing, but not the most secure)
- Authenticator apps like Google Authenticator or Authy (more secure)
- Hardware security keys (most secure)`,actionItems:["Enable 2FA on your email accounts","Set up 2FA for banking and financial accounts","Install an authenticator app","Consider getting a hardware security key"],tags:["2fa","authentication","security"]},{id:4,title:"Keep Software Updated",category:"general",priority:"high",readTime:3,likes:134,content:`Software updates often include critical security patches that fix vulnerabilities. Cybercriminals actively exploit these vulnerabilities, so keeping your software updated is essential.

What to keep updated:
- Operating system (Windows, macOS, Linux)
- Web browsers (Chrome, Firefox, Safari, Edge)
- Antivirus software
- Mobile apps
- Router firmware

Enable automatic updates whenever possible. This ensures you get security patches as soon as they're available without having to remember to check manually.`,actionItems:["Enable automatic updates on all devices","Check for router firmware updates","Update mobile apps regularly","Keep antivirus software current"],tags:["updates","software","patches"]},{id:5,title:"Be Cautious with Public Wi-Fi",category:"browsing",priority:"medium",readTime:3,likes:98,content:`Public Wi-Fi networks are convenient but can be dangerous. Attackers can easily intercept data transmitted over unsecured networks, potentially stealing passwords, personal information, and other sensitive data.

Safety tips for public Wi-Fi:
- Avoid accessing sensitive accounts (banking, email) on public networks
- Use a VPN (Virtual Private Network) to encrypt your connection
- Turn off automatic Wi-Fi connection on your devices
- Verify the network name with staff before connecting
- Use your phone's hotspot instead when possible`,actionItems:["Install a reputable VPN app","Disable auto-connect to Wi-Fi networks","Use mobile data for sensitive activities","Always verify network names with staff"],tags:["wifi","vpn","public networks"]},{id:6,title:"Secure Your Mobile Devices",category:"mobile",priority:"medium",readTime:4,likes:87,content:`Mobile devices contain vast amounts of personal information and are often targets for thieves and hackers. Securing your phone or tablet is crucial for protecting your data.

Mobile security essentials:
- Use a strong lock screen (PIN, password, fingerprint, or face unlock)
- Keep your device's operating system updated
- Only download apps from official app stores
- Review app permissions carefully
- Enable remote wipe capabilities
- Use encryption if available

Be cautious about what you do on your mobile device in public. Shoulder surfing (people watching you enter passwords) is a real threat in crowded areas.`,actionItems:["Set up a strong lock screen","Enable automatic device locking","Set up remote wipe capabilities","Review and limit app permissions"],tags:["mobile","smartphone","apps"]},{id:7,title:"Think Before You Click",category:"browsing",priority:"high",readTime:2,likes:167,content:`Many cyber attacks succeed because people click on malicious links or download infected files. Developing good clicking habits can prevent most malware infections.

Before clicking any link, ask yourself:
- Do I trust the sender?
- Was I expecting this message?
- Does the link look legitimate?
- Is this too good to be true?

Hover over links to see where they actually lead before clicking. Be especially suspicious of shortened URLs (bit.ly, tinyurl, etc.) that hide the real destination.

Never download software from pop-up ads or unfamiliar websites. Stick to official sources and app stores for downloads.`,actionItems:["Hover over links before clicking","Verify sender identity for unexpected messages","Use official sources for software downloads","Install ad blockers to reduce malicious ads"],tags:["clicking","links","malware"]},{id:8,title:"Protect Your Personal Information",category:"social",priority:"high",readTime:3,likes:123,content:`Identity thieves can use surprisingly little information to steal your identity. Be careful about what personal information you share, both online and offline.

Information to protect:
- Social Security Number
- Date of birth
- Full name and address
- Phone numbers
- Financial account numbers
- Passwords and PINs

Be especially careful on social media. Avoid posting information that could be used to answer security questions, such as your pet's name, where you went to school, or your mother's maiden name.

Shred documents containing personal information before throwing them away. Identity thieves still go through trash looking for useful information.`,actionItems:["Review your social media privacy settings","Limit personal information in online profiles","Shred sensitive documents","Be cautious about sharing personal details"],tags:["privacy","personal information","identity theft"]}],g=v.filter(j=>{const _=j.title.toLowerCase().includes(i.toLowerCase())||j.content.toLowerCase().includes(i.toLowerCase())||j.tags.some(L=>L.toLowerCase().includes(i.toLowerCase())),E=o==="all"||j.category===o;return _&&E}),m=j=>{switch(j){case"critical":return"bg-red-500/20 text-red-300 border-red-500/50";case"high":return"bg-orange-500/20 text-orange-300 border-orange-500/50";case"medium":return"bg-yellow-500/20 text-yellow-300 border-yellow-500/50";case"low":return"bg-blue-500/20 text-blue-300 border-blue-500/50";default:return"bg-slate-500/20 text-slate-300 border-slate-500/50"}},b=j=>{const _=new Set(d);_.has(j)?(_.delete(j),Bt.success("Removed from favorites")):(_.add(j),Bt.success("Added to favorites")),h(_)},x={totalTips:v.length,criticalTips:v.filter(j=>j.priority==="critical").length,completedTips:d.size,avgReadTime:Math.round(v.reduce((j,_)=>j+_.readTime,0)/v.length)};return s.jsx(li,{children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Security Education Center"}),s.jsx("p",{className:"text-slate-400",children:"Learn cybersecurity best practices and stay protected against online threats"})]}),s.jsx("div",{className:"flex items-center space-x-2 mt-4 md:mt-0",children:s.jsxs(Ve,{variant:"outline",className:"border-purple-500/50 text-purple-300",children:[s.jsx(Kn,{className:"w-3 h-3 mr-1"}),"Learning Hub"]})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Total Tips"}),s.jsx(Kn,{className:"h-4 w-4 text-blue-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:x.totalTips}),s.jsx("p",{className:"text-xs text-slate-400",children:"Available to learn"})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Critical Tips"}),s.jsx(na,{className:"h-4 w-4 text-red-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:x.criticalTips}),s.jsx("p",{className:"text-xs text-slate-400",children:"High priority"})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Favorites"}),s.jsx(ou,{className:"h-4 w-4 text-pink-400"})]}),s.jsxs(oe,{children:[s.jsx("div",{className:"text-2xl font-bold text-white",children:x.completedTips}),s.jsx("p",{className:"text-xs text-slate-400",children:"Tips you liked"})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(he,{className:"text-sm font-medium text-slate-300",children:"Avg Read Time"}),s.jsx(ei,{className:"h-4 w-4 text-green-400"})]}),s.jsxs(oe,{children:[s.jsxs("div",{className:"text-2xl font-bold text-white",children:[x.avgReadTime,"m"]}),s.jsx("p",{className:"text-xs text-slate-400",children:"Per tip"})]})]})]}),s.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[s.jsxs("div",{className:"relative flex-1",children:[s.jsx(w0,{className:"absolute left-3 top-3 h-4 w-4 text-slate-400"}),s.jsx($t,{placeholder:"Search security tips...",value:i,onChange:j=>c(j.target.value),className:"pl-10 bg-slate-700/50 border-slate-600 text-white"})]}),s.jsx("div",{className:"flex flex-wrap gap-2",children:y.map(j=>{const _=j.icon;return s.jsxs(ye,{variant:o===j.id?"default":"outline",size:"sm",onClick:()=>u(j.id),className:o===j.id?"bg-purple-600 hover:bg-purple-700":"border-slate-600 text-slate-300 hover:bg-slate-800",children:[s.jsx(_,{className:"w-4 h-4 mr-2"}),j.name]},j.id)})})]}),s.jsx("div",{className:"grid gap-6",children:g.map(j=>s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors",children:[s.jsx(me,{children:s.jsxs("div",{className:"flex items-start justify-between",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[s.jsx(Ve,{className:m(j.priority),children:j.priority}),s.jsxs(Ve,{variant:"outline",className:"border-slate-600 text-slate-300",children:[j.readTime," min read"]})]}),s.jsx(he,{className:"text-white text-xl mb-2",children:j.title}),s.jsxs("div",{className:"flex items-center space-x-4 text-sm text-slate-400",children:[s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx(Nt,{className:"h-4 w-4"}),s.jsxs("span",{children:[j.likes," views"]})]}),s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx(ou,{className:`h-4 w-4 ${d.has(j.id)?"text-pink-400 fill-current":""}`}),s.jsxs("span",{children:[j.likes+(d.has(j.id)?1:0)," likes"]})]})]})]}),s.jsx(ye,{variant:"ghost",size:"sm",onClick:()=>b(j.id),className:`text-slate-400 hover:text-pink-400 ${d.has(j.id)?"text-pink-400":""}`,children:s.jsx(ou,{className:`h-5 w-5 ${d.has(j.id)?"fill-current":""}`})})]})}),s.jsxs(oe,{className:"space-y-4",children:[s.jsxs("div",{className:"text-slate-300 leading-relaxed",children:[j.content.split(`

`)[0],"..."]}),j.actionItems&&j.actionItems.length>0&&s.jsxs("div",{className:"space-y-2",children:[s.jsx("h4",{className:"text-white font-medium",children:"Action Items:"}),s.jsx("div",{className:"grid md:grid-cols-2 gap-2",children:j.actionItems.slice(0,4).map((_,E)=>s.jsxs("div",{className:"flex items-start space-x-2",children:[s.jsx(At,{className:"h-4 w-4 text-green-400 mt-0.5 flex-shrink-0"}),s.jsx("span",{className:"text-slate-300 text-sm",children:_})]},E))})]}),s.jsxs("div",{className:"flex items-center justify-between pt-4 border-t border-slate-700",children:[s.jsx("div",{className:"flex flex-wrap gap-1",children:j.tags.slice(0,3).map(_=>s.jsx(Ve,{variant:"outline",className:"border-slate-600 text-slate-400 text-xs",children:_},_))}),s.jsxs(ye,{size:"sm",className:"bg-purple-600 hover:bg-purple-700",children:[s.jsx(Ab,{className:"w-4 h-4 mr-2"}),"Read More"]})]})]})]},j.id))}),g.length===0&&s.jsx(ce,{className:"bg-slate-800/50 border-slate-700",children:s.jsxs(oe,{className:"text-center py-12",children:[s.jsx(Kn,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),s.jsx("h3",{className:"text-white text-lg font-medium mb-2",children:"No tips found"}),s.jsx("p",{className:"text-slate-400",children:"Try adjusting your search terms or category filters"})]})}),s.jsxs(ce,{className:"bg-gradient-to-r from-purple-900/50 to-blue-900/50 border-purple-500/30",children:[s.jsxs(me,{children:[s.jsxs(he,{className:"text-white flex items-center",children:[s.jsx(Ub,{className:"w-5 h-5 mr-2 text-yellow-400"}),"Featured Security Resources"]}),s.jsx(xt,{className:"text-slate-300",children:"Essential tools and resources for staying secure online"})]}),s.jsx(oe,{children:s.jsxs("div",{className:"grid md:grid-cols-3 gap-4",children:[s.jsxs("div",{className:"p-4 bg-slate-800/50 rounded-lg border border-slate-700",children:[s.jsx(Ge,{className:"h-8 w-8 text-purple-400 mb-3"}),s.jsx("h4",{className:"text-white font-medium mb-2",children:"Password Managers"}),s.jsx("p",{className:"text-slate-300 text-sm",children:"Use tools like Bitwarden, 1Password, or LastPass to generate and store unique passwords"})]}),s.jsxs("div",{className:"p-4 bg-slate-800/50 rounded-lg border border-slate-700",children:[s.jsx(Bs,{className:"h-8 w-8 text-yellow-400 mb-3"}),s.jsx("h4",{className:"text-white font-medium mb-2",children:"VPN Services"}),s.jsx("p",{className:"text-slate-300 text-sm",children:"Protect your internet connection with reputable VPN providers like NordVPN or ExpressVPN"})]}),s.jsxs("div",{className:"p-4 bg-slate-800/50 rounded-lg border border-slate-700",children:[s.jsx(Nt,{className:"h-8 w-8 text-blue-400 mb-3"}),s.jsx("h4",{className:"text-white font-medium mb-2",children:"Security Scanners"}),s.jsx("p",{className:"text-slate-300 text-sm",children:"Regular security scans with tools like Malwarebytes or Windows Defender"})]})]})})]})]})})},HN=()=>{const[i,c]=N.useState({firstName:"John",lastName:"Doe",email:"<EMAIL>",phone:"+****************",timezone:"America/New_York"}),[o,u]=N.useState({emailAlerts:!0,smsAlerts:!0,pushNotifications:!1,weeklyReports:!0,securityTips:!0,breachAlerts:!0,suspiciousActivity:!0}),[d,h]=N.useState({dataRetention:"2-years",shareAnonymousData:!1,marketingEmails:!1,thirdPartySharing:!1}),[y,v]=N.useState({twoFactorEnabled:!0,sessionTimeout:"30-minutes",loginNotifications:!0,deviceTracking:!0}),g=()=>{Bt.success("Profile updated successfully!")},m=()=>{Bt.success("Notification preferences saved!")},b=()=>{Bt.success("Privacy settings updated!")},x=()=>{Bt.success("Security settings updated!")},j=()=>{Bt.success("Data export initiated. You will receive an email when ready.")},_=()=>{Bt.error("Account deletion requires additional verification. Please contact support.")};return s.jsx(li,{children:s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center",children:s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Account Settings"}),s.jsx("p",{className:"text-slate-400",children:"Manage your account preferences, security settings, and privacy controls"})]})}),s.jsxs(qr,{defaultValue:"profile",className:"space-y-6",children:[s.jsxs(Gr,{className:"grid w-full grid-cols-4 bg-slate-800/50",children:[s.jsxs(Ht,{value:"profile",className:"data-[state=active]:bg-purple-600",children:[s.jsx(zr,{className:"w-4 h-4 mr-2"}),"Profile"]}),s.jsxs(Ht,{value:"notifications",className:"data-[state=active]:bg-purple-600",children:[s.jsx(_r,{className:"w-4 h-4 mr-2"}),"Notifications"]}),s.jsxs(Ht,{value:"security",className:"data-[state=active]:bg-purple-600",children:[s.jsx(Ge,{className:"w-4 h-4 mr-2"}),"Security"]}),s.jsxs(Ht,{value:"privacy",className:"data-[state=active]:bg-purple-600",children:[s.jsx(Us,{className:"w-4 h-4 mr-2"}),"Privacy"]})]}),s.jsxs(qt,{value:"profile",className:"space-y-6",children:[s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsx(he,{className:"text-white",children:"Personal Information"}),s.jsx(xt,{children:"Update your personal details and contact information"})]}),s.jsxs(oe,{className:"space-y-4",children:[s.jsxs("div",{className:"grid md:grid-cols-2 gap-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{htmlFor:"firstName",className:"text-slate-300",children:"First Name"}),s.jsx($t,{id:"firstName",value:i.firstName,onChange:E=>c({...i,firstName:E.target.value}),className:"bg-slate-700/50 border-slate-600 text-white"})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{htmlFor:"lastName",className:"text-slate-300",children:"Last Name"}),s.jsx($t,{id:"lastName",value:i.lastName,onChange:E=>c({...i,lastName:E.target.value}),className:"bg-slate-700/50 border-slate-600 text-white"})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{htmlFor:"email",className:"text-slate-300",children:"Email Address"}),s.jsx($t,{id:"email",type:"email",value:i.email,onChange:E=>c({...i,email:E.target.value}),className:"bg-slate-700/50 border-slate-600 text-white"})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{htmlFor:"phone",className:"text-slate-300",children:"Phone Number"}),s.jsx($t,{id:"phone",value:i.phone,onChange:E=>c({...i,phone:E.target.value}),className:"bg-slate-700/50 border-slate-600 text-white"})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{htmlFor:"timezone",className:"text-slate-300",children:"Timezone"}),s.jsxs("select",{id:"timezone",value:i.timezone,onChange:E=>c({...i,timezone:E.target.value}),className:"w-full px-3 py-2 bg-slate-700/50 border border-slate-600 rounded-md text-white",children:[s.jsx("option",{value:"America/New_York",children:"Eastern Time (ET)"}),s.jsx("option",{value:"America/Chicago",children:"Central Time (CT)"}),s.jsx("option",{value:"America/Denver",children:"Mountain Time (MT)"}),s.jsx("option",{value:"America/Los_Angeles",children:"Pacific Time (PT)"}),s.jsx("option",{value:"UTC",children:"UTC"})]})]}),s.jsxs(ye,{onClick:g,className:"bg-purple-600 hover:bg-purple-700",children:[s.jsx(Er,{className:"w-4 h-4 mr-2"}),"Save Changes"]})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsx(he,{className:"text-white",children:"Account Actions"}),s.jsx(xt,{children:"Export your data or delete your account"})]}),s.jsxs(oe,{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-slate-600",children:[s.jsxs("div",{children:[s.jsx("h4",{className:"text-white font-medium",children:"Export Account Data"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Download all your data in a portable format"})]}),s.jsxs(ye,{onClick:j,variant:"outline",className:"border-slate-600 text-slate-300",children:[s.jsx(Mu,{className:"w-4 h-4 mr-2"}),"Export"]})]}),s.jsxs("div",{className:"flex items-center justify-between p-4 bg-red-500/10 rounded-lg border border-red-500/50",children:[s.jsxs("div",{children:[s.jsx("h4",{className:"text-white font-medium",children:"Delete Account"}),s.jsx("p",{className:"text-red-300 text-sm",children:"Permanently delete your account and all data"})]}),s.jsxs(ye,{onClick:_,variant:"destructive",className:"bg-red-600 hover:bg-red-700",children:[s.jsx(Hb,{className:"w-4 h-4 mr-2"}),"Delete"]})]})]})]})]}),s.jsx(qt,{value:"notifications",className:"space-y-6",children:s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsx(he,{className:"text-white",children:"Notification Preferences"}),s.jsx(xt,{children:"Choose how you want to receive alerts and updates"})]}),s.jsxs(oe,{className:"space-y-6",children:[s.jsxs("div",{className:"space-y-4",children:[s.jsx("h4",{className:"text-white font-medium",children:"Delivery Methods"}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Nl,{className:"h-5 w-5 text-blue-400"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Email Alerts"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Receive security alerts via email"})]})]}),s.jsx(Tt,{checked:o.emailAlerts,onCheckedChange:E=>u({...o,emailAlerts:E})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Ql,{className:"h-5 w-5 text-green-400"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"SMS Alerts"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Critical alerts via text message"})]})]}),s.jsx(Tt,{checked:o.smsAlerts,onCheckedChange:E=>u({...o,smsAlerts:E})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Za,{className:"h-5 w-5 text-purple-400"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Push Notifications"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Browser notifications"})]})]}),s.jsx(Tt,{checked:o.pushNotifications,onCheckedChange:E=>u({...o,pushNotifications:E})})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h4",{className:"text-white font-medium",children:"Alert Types"}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Data Breach Alerts"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Immediate notification of identity breaches"})]}),s.jsx(Tt,{checked:o.breachAlerts,onCheckedChange:E=>u({...o,breachAlerts:E})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Suspicious Activity"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Alerts for unusual account activity"})]}),s.jsx(Tt,{checked:o.suspiciousActivity,onCheckedChange:E=>u({...o,suspiciousActivity:E})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Weekly Reports"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Summary of your security status"})]}),s.jsx(Tt,{checked:o.weeklyReports,onCheckedChange:E=>u({...o,weeklyReports:E})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Security Tips"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Educational content and best practices"})]}),s.jsx(Tt,{checked:o.securityTips,onCheckedChange:E=>u({...o,securityTips:E})})]})]}),s.jsxs(ye,{onClick:m,className:"bg-purple-600 hover:bg-purple-700",children:[s.jsx(Er,{className:"w-4 h-4 mr-2"}),"Save Preferences"]})]})]})}),s.jsxs(qt,{value:"security",className:"space-y-6",children:[s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsx(he,{className:"text-white",children:"Security Settings"}),s.jsx(xt,{children:"Manage your account security and authentication"})]}),s.jsxs(oe,{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 bg-green-500/10 rounded-lg border border-green-500/50",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(At,{className:"h-6 w-6 text-green-400"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Two-Factor Authentication"}),s.jsx("p",{className:"text-green-300 text-sm",children:"Enabled with authenticator app"})]})]}),s.jsx(ye,{variant:"outline",className:"border-green-500/50 text-green-300",children:"Manage"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Login Notifications"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Get notified of new device logins"})]}),s.jsx(Tt,{checked:y.loginNotifications,onCheckedChange:E=>v({...y,loginNotifications:E})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Device Tracking"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Monitor devices accessing your account"})]}),s.jsx(Tt,{checked:y.deviceTracking,onCheckedChange:E=>v({...y,deviceTracking:E})})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{className:"text-white font-medium",children:"Session Timeout"}),s.jsxs("select",{value:y.sessionTimeout,onChange:E=>v({...y,sessionTimeout:E.target.value}),className:"w-full px-3 py-2 bg-slate-700/50 border border-slate-600 rounded-md text-white",children:[s.jsx("option",{value:"15-minutes",children:"15 minutes"}),s.jsx("option",{value:"30-minutes",children:"30 minutes"}),s.jsx("option",{value:"1-hour",children:"1 hour"}),s.jsx("option",{value:"4-hours",children:"4 hours"}),s.jsx("option",{value:"never",children:"Never"})]})]})]}),s.jsxs(ye,{onClick:x,className:"bg-purple-600 hover:bg-purple-700",children:[s.jsx(Er,{className:"w-4 h-4 mr-2"}),"Update Security Settings"]})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsx(he,{className:"text-white",children:"Active Sessions"}),s.jsx(xt,{children:"Manage devices that have access to your account"})]}),s.jsxs(oe,{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-slate-600",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Current Session"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Chrome on Windows • New York, NY"}),s.jsx("p",{className:"text-slate-500 text-xs",children:"Active now"})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(At,{className:"h-5 w-5 text-green-400"}),s.jsx("span",{className:"text-green-300 text-sm",children:"Current"})]})]}),s.jsxs("div",{className:"flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-slate-600",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Mobile App"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"iPhone • New York, NY"}),s.jsx("p",{className:"text-slate-500 text-xs",children:"Last active 2 hours ago"})]}),s.jsx(ye,{variant:"outline",size:"sm",className:"border-red-500/50 text-red-300",children:"Revoke"})]})]})]})]}),s.jsxs(qt,{value:"privacy",className:"space-y-6",children:[s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsx(he,{className:"text-white",children:"Privacy Controls"}),s.jsx(xt,{children:"Manage how your data is used and stored"})]}),s.jsxs(oe,{className:"space-y-6",children:[s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx(at,{className:"text-white font-medium",children:"Data Retention Period"}),s.jsxs("select",{value:d.dataRetention,onChange:E=>h({...d,dataRetention:E.target.value}),className:"w-full px-3 py-2 bg-slate-700/50 border border-slate-600 rounded-md text-white",children:[s.jsx("option",{value:"1-year",children:"1 year"}),s.jsx("option",{value:"2-years",children:"2 years"}),s.jsx("option",{value:"5-years",children:"5 years"}),s.jsx("option",{value:"indefinite",children:"Indefinite"})]}),s.jsx("p",{className:"text-slate-400 text-sm",children:"How long we keep your monitoring data"})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Anonymous Analytics"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Help improve our service with anonymous usage data"})]}),s.jsx(Tt,{checked:d.shareAnonymousData,onCheckedChange:E=>h({...d,shareAnonymousData:E})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Marketing Communications"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Receive product updates and security news"})]}),s.jsx(Tt,{checked:d.marketingEmails,onCheckedChange:E=>h({...d,marketingEmails:E})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-white font-medium",children:"Third-Party Data Sharing"}),s.jsx("p",{className:"text-slate-400 text-sm",children:"Share data with security research partners"})]}),s.jsx(Tt,{checked:d.thirdPartySharing,onCheckedChange:E=>h({...d,thirdPartySharing:E})})]})]}),s.jsxs(ti,{className:"border-blue-500/50 bg-blue-500/10",children:[s.jsx(Ge,{className:"h-4 w-4"}),s.jsxs(ai,{className:"text-blue-300",children:[s.jsx("strong",{children:"Privacy Commitment:"})," We never sell your personal data. All data sharing is optional and anonymized for security research purposes only."]})]}),s.jsxs(ye,{onClick:b,className:"bg-purple-600 hover:bg-purple-700",children:[s.jsx(Er,{className:"w-4 h-4 mr-2"}),"Save Privacy Settings"]})]})]}),s.jsxs(ce,{className:"bg-slate-800/50 border-slate-700",children:[s.jsxs(me,{children:[s.jsx(he,{className:"text-white",children:"Data Rights"}),s.jsx(xt,{children:"Exercise your data protection rights"})]}),s.jsx(oe,{className:"space-y-4",children:s.jsxs("div",{className:"grid md:grid-cols-2 gap-4",children:[s.jsxs("div",{className:"p-4 bg-slate-700/30 rounded-lg border border-slate-600",children:[s.jsx("h4",{className:"text-white font-medium mb-2",children:"Right to Access"}),s.jsx("p",{className:"text-slate-400 text-sm mb-3",children:"Request a copy of all data we have about you"}),s.jsx(ye,{variant:"outline",size:"sm",className:"border-slate-600 text-slate-300",children:"Request Data"})]}),s.jsxs("div",{className:"p-4 bg-slate-700/30 rounded-lg border border-slate-600",children:[s.jsx("h4",{className:"text-white font-medium mb-2",children:"Right to Rectification"}),s.jsx("p",{className:"text-slate-400 text-sm mb-3",children:"Correct any inaccurate personal data"}),s.jsx(ye,{variant:"outline",size:"sm",className:"border-slate-600 text-slate-300",children:"Update Data"})]}),s.jsxs("div",{className:"p-4 bg-slate-700/30 rounded-lg border border-slate-600",children:[s.jsx("h4",{className:"text-white font-medium mb-2",children:"Right to Erasure"}),s.jsx("p",{className:"text-slate-400 text-sm mb-3",children:"Request deletion of your personal data"}),s.jsx(ye,{variant:"outline",size:"sm",className:"border-red-500/50 text-red-300",children:"Delete Data"})]}),s.jsxs("div",{className:"p-4 bg-slate-700/30 rounded-lg border border-slate-600",children:[s.jsx("h4",{className:"text-white font-medium mb-2",children:"Right to Portability"}),s.jsx("p",{className:"text-slate-400 text-sm mb-3",children:"Export your data in a machine-readable format"}),s.jsx(ye,{variant:"outline",size:"sm",className:"border-slate-600 text-slate-300",children:"Export Data"})]})]})})]})]})]})]})})};function qN(){return s.jsx(iv,{defaultTheme:"dark",storageKey:"identity-guardian-theme",children:s.jsx(qv,{children:s.jsx(Ky,{children:s.jsxs("div",{className:"min-h-screen bg-background",children:[s.jsxs(wy,{children:[s.jsx(fa,{path:"/",element:s.jsx(Kb,{})}),s.jsx(fa,{path:"/login",element:s.jsx($b,{})}),s.jsx(fa,{path:"/register",element:s.jsx(H1,{})}),s.jsx(fa,{path:"/dashboard",element:s.jsx(Ds,{children:s.jsx(q1,{})})}),s.jsx(fa,{path:"/identities",element:s.jsx(Ds,{children:s.jsx(yN,{})})}),s.jsx(fa,{path:"/passwords",element:s.jsx(Ds,{children:s.jsx(UN,{})})}),s.jsx(fa,{path:"/monitoring",element:s.jsx(Ds,{children:s.jsx(BN,{})})}),s.jsx(fa,{path:"/security-tips",element:s.jsx(Ds,{children:s.jsx(LN,{})})}),s.jsx(fa,{path:"/settings",element:s.jsx(Ds,{children:s.jsx(HN,{})})}),s.jsx(fa,{path:"*",element:s.jsx(f0,{to:"/",replace:!0})})]}),s.jsx(Lv,{})]})})})})}_g.createRoot(document.getElementById("root")).render(s.jsx(N.StrictMode,{children:s.jsx(qN,{})}));
