from datetime import datetime
from enum import Enum
from src import db

class IdentityType(Enum):
    """Enumeration for identity types."""
    PHONE = 'phone'
    EMAIL = 'email'

class IdentityStatus(Enum):
    """Enumeration for identity validation status."""
    PENDING = 'pending'
    VALID = 'valid'
    INVALID = 'invalid'
    ERROR = 'error'

class RiskLevel(Enum):
    """Enumeration for risk levels."""
    LOW = 'low'
    MEDIUM = 'medium'
    HIGH = 'high'
    CRITICAL = 'critical'

class Identity(db.Model):
    """Identity model for storing phone numbers and email addresses."""
    
    __tablename__ = 'identities'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    
    # Identity information
    identity_type = db.Column(db.Enum(IdentityType), nullable=False, index=True)
    original_value = db.Column(db.String(255), nullable=False)
    normalized_value = db.Column(db.String(255), nullable=False, index=True)
    
    # Validation information
    status = db.Column(db.Enum(IdentityStatus), default=IdentityStatus.PENDING, nullable=False)
    validation_details = db.Column(db.JSON)
    last_validated = db.Column(db.DateTime)
    
    # Risk assessment
    risk_level = db.Column(db.Enum(RiskLevel), default=RiskLevel.LOW, nullable=False)
    risk_score = db.Column(db.Integer, default=0, nullable=False)  # 0-100
    risk_factors = db.Column(db.JSON)
    
    # Monitoring configuration
    is_monitored = db.Column(db.Boolean, default=True, nullable=False)
    monitoring_frequency = db.Column(db.Integer, default=24)  # hours
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    breaches = db.relationship('Breach', backref='identity', lazy=True, cascade='all, delete-orphan')
    monitoring_records = db.relationship('Monitoring', backref='identity', lazy=True, cascade='all, delete-orphan')
    
    def __init__(self, user_id, identity_type, original_value, normalized_value=None):
        self.user_id = user_id
        self.identity_type = identity_type
        self.original_value = original_value.strip()
        self.normalized_value = normalized_value or self.original_value.strip()
    
    def update_risk_assessment(self, risk_level, risk_score, risk_factors=None):
        """Update risk assessment for this identity."""
        self.risk_level = risk_level
        self.risk_score = max(0, min(100, risk_score))  # Ensure 0-100 range
        self.risk_factors = risk_factors or {}
        self.updated_at = datetime.utcnow()
    
    def update_validation_status(self, status, details=None):
        """Update validation status and details."""
        self.status = status
        self.validation_details = details or {}
        self.last_validated = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def get_breach_count(self):
        """Get total number of breaches for this identity."""
        return len(self.breaches)
    
    def get_latest_breach(self):
        """Get the most recent breach for this identity."""
        if not self.breaches:
            return None
        return max(self.breaches, key=lambda b: b.detected_at)
    
    @property
    def is_phone(self):
        """Check if this is a phone number identity."""
        return self.identity_type == IdentityType.PHONE
    
    @property
    def is_email(self):
        """Check if this is an email identity."""
        return self.identity_type == IdentityType.EMAIL
    
    def to_dict(self, include_breaches=False):
        """Convert identity to dictionary."""
        data = {
            'id': self.id,
            'user_id': self.user_id,
            'identity_type': self.identity_type.value,
            'original_value': self.original_value,
            'normalized_value': self.normalized_value,
            'status': self.status.value,
            'validation_details': self.validation_details,
            'last_validated': self.last_validated.isoformat() if self.last_validated else None,
            'risk_level': self.risk_level.value,
            'risk_score': self.risk_score,
            'risk_factors': self.risk_factors,
            'is_monitored': self.is_monitored,
            'monitoring_frequency': self.monitoring_frequency,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'breach_count': self.get_breach_count()
        }
        
        if include_breaches:
            data['breaches'] = [breach.to_dict() for breach in self.breaches]
            data['latest_breach'] = self.get_latest_breach().to_dict() if self.get_latest_breach() else None
        
        return data
    
    def __repr__(self):
        return f'<Identity {self.identity_type.value}: {self.normalized_value}>'

