# Identity Guardian - Complete Package

## 🛡️ Overview

**Identity Guardian** is a comprehensive cybersecurity application designed to protect your digital identity through advanced monitoring, password security, and cybersecurity education. This package contains everything you need to set up and run the application on your local machine.

## 📦 Package Contents

```
identity-guardian-complete/
├── README.md                           # This file
├── installation_guide.md               # Complete installation instructions
├── identity_guardian_demo_guide.md     # Demo guide and features
├── theguardian_database_schema.sql     # PostgreSQL database schema
├── requirements.txt                    # Python dependencies
├── package.json                        # Node.js dependencies summary
├── .vscode/                           # VSCode configuration
│   ├── settings.json
│   ├── launch.json
│   └── tasks.json
├── identity-guardian-backend/          # Flask backend application
│   ├── src/
│   │   ├── main.py                    # Main Flask application
│   │   ├── config.py                  # Configuration settings
│   │   ├── models/                    # Database models
│   │   ├── api/                       # API endpoints
│   │   ├── services/                  # Business logic services
│   │   └── static/                    # Static files (built frontend)
│   ├── venv/                          # Python virtual environment
│   ├── requirements.txt               # Python dependencies
│   └── .env.example                   # Environment variables template
└── identity-guardian-frontend/         # React frontend application
    ├── src/
    │   ├── App.jsx                    # Main React component
    │   ├── index.css                  # Beautiful CSS with animations
    │   ├── pages/                     # React pages
    │   ├── components/                # React components
    │   ├── contexts/                  # React contexts
    │   └── services/                  # API services
    ├── public/                        # Public assets
    ├── package.json                   # Node.js dependencies
    └── dist/                          # Built frontend files
```

## 🚀 Quick Start

### 1. Prerequisites
- **Node.js** (v18 or higher)
- **Python** (v3.9 or higher)
- **PostgreSQL** (v13 or higher)
- **Visual Studio Code** (recommended)

### 2. Database Setup
```bash
# Create PostgreSQL database
createdb theguardian

# Import schema
psql theguardian < theguardian_database_schema.sql
```

### 3. Backend Setup
```bash
cd identity-guardian-backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
source venv/bin/activate  # macOS/Linux
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your database credentials
```

### 4. Frontend Setup
```bash
cd identity-guardian-frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

### 5. Run Application
```bash
# Terminal 1 - Backend
cd identity-guardian-backend
source venv/bin/activate
python src/main.py

# Terminal 2 - Frontend (if running separately)
cd identity-guardian-frontend
npm run dev
```

## 🎨 Features

### ✨ Beautiful, Elegant Design
- **Animated gradient backgrounds** with smooth color transitions
- **Glass morphism UI** with professional blur effects
- **Glowing buttons and hover effects** with smooth animations
- **Modern cybersecurity aesthetic** with purple/blue gradients
- **Responsive design** for all devices

### 🔒 Comprehensive Security Features
- **Password strength analyzer** with real-time feedback
- **Compromised password detection** using HaveIBeenPwned API
- **Secure password generator** with customizable options
- **Identity monitoring** for phone numbers and emails
- **Breach detection** and real-time alerts
- **Cybersecurity education center** with best practices

### 📊 Advanced Dashboard
- **Real-time security metrics** with animated progress bars
- **Activity timeline** with color-coded severity levels
- **Quick actions** for common security tasks
- **Security score breakdown** with detailed analytics

## 🔧 VSCode Setup

### Recommended Extensions
- Python
- JavaScript (ES6) code snippets
- Tailwind CSS IntelliSense
- PostgreSQL
- GitLens
- Prettier - Code formatter
- ES7+ React/Redux/React-Native snippets

### Configuration Files Included
- `.vscode/settings.json` - VSCode workspace settings
- `.vscode/launch.json` - Debug configurations
- `.vscode/tasks.json` - Build and run tasks

## 🗄️ Database

The application uses PostgreSQL with the database name **"theguardian"**. The complete schema includes:

- **User management** and authentication
- **Identity monitoring** and breach tracking
- **Password security** and analysis
- **Security alerts** and notifications
- **Educational content** and tips
- **Audit logging** and analytics

## 🌐 Demo

**Live Demo**: Available in demo guide
**Credentials**: <EMAIL> / SecureDemo123!

## 📚 Documentation

- `installation_guide.md` - Complete setup instructions for all platforms
- `identity_guardian_demo_guide.md` - Feature overview and demo guide
- `theguardian_database_schema.sql` - Database structure and relationships

## 🔐 Security

- **256-bit encryption** for sensitive data
- **JWT authentication** with secure token handling
- **Password hashing** using bcrypt
- **SQL injection protection** with parameterized queries
- **CORS configuration** for secure API access
- **Environment variable** protection for secrets

## 🚀 Deployment

### Development
```bash
# Backend
python src/main.py

# Frontend
npm run dev
```

### Production
```bash
# Build frontend
npm run build

# Copy to Flask static directory
cp -r dist/* ../identity-guardian-backend/src/static/

# Run Flask in production mode
export FLASK_ENV=production
python src/main.py
```

## 🛠️ Troubleshooting

### Common Issues
1. **Database connection errors** - Check PostgreSQL service and credentials
2. **Node.js dependency issues** - Clear npm cache and reinstall
3. **Python virtual environment** - Recreate venv if needed
4. **Port conflicts** - Ensure ports 5000 and 5173 are available

### Support
- Check `installation_guide.md` for detailed troubleshooting
- Review application logs for error details
- Verify all environment variables are configured

## 📄 License

This project is created for educational and security purposes. Use responsibly and in accordance with applicable laws and regulations.

## 🎯 Next Steps

1. **Set up development environment** following installation guide
2. **Configure database** with provided schema
3. **Customize branding** and styling as needed
4. **Add additional features** using the extensible architecture
5. **Deploy to production** using provided deployment guide

---

**Identity Guardian** - Your comprehensive cybersecurity defense center, ready for development and deployment!

For detailed installation instructions, see `installation_guide.md`
For feature overview and demo, see `identity_guardian_demo_guide.md`

