import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  Eye, 
  Lock, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp,
  Users,
  Globe,
  Smartphone,
  Mail,
  Key,
  Activity,
  Bell,
  Settings,
  BarChart3,
  PieChart,
  Zap,
  Target,
  Award,
  Clock
} from 'lucide-react';

const DashboardPage = () => {
  const [securityScore, setSecurityScore] = useState(85);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    // Simulate loading
    setTimeout(() => setIsLoading(false), 1000);
  }, []);

  const securityMetrics = [
    {
      title: 'Security Score',
      value: securityScore,
      max: 100,
      status: 'excellent',
      icon: Shield,
      gradient: 'from-green-500 to-emerald-500',
      description: 'Overall security health of your digital identity'
    },
    {
      title: 'Identities Monitored',
      value: 5,
      status: 'active',
      icon: Eye,
      gradient: 'from-blue-500 to-cyan-500',
      description: 'Phone numbers & emails'
    },
    {
      title: 'Breaches Detected',
      value: 2,
      status: 'warning',
      icon: AlertTriangle,
      gradient: 'from-orange-500 to-red-500',
      description: 'Requires attention'
    },
    {
      title: 'Strong Passwords',
      value: 8,
      total: 10,
      status: 'good',
      icon: Lock,
      gradient: 'from-purple-500 to-pink-500',
      description: 'Password vault status'
    }
  ];

  const recentActivity = [
    {
      id: 1,
      type: 'breach',
      title: 'New breach detected',
      description: 'Your email found in LinkedIn data breach',
      time: '2 hours ago',
      severity: 'high',
      icon: AlertTriangle,
      color: 'text-red-400'
    },
    {
      id: 2,
      type: 'password',
      title: 'Weak password detected',
      description: 'Password for social media account needs strengthening',
      time: '1 day ago',
      severity: 'medium',
      icon: Key,
      color: 'text-yellow-400'
    },
    {
      id: 3,
      type: 'monitoring',
      title: 'New identity added',
      description: 'Started monitoring ******-0123',
      time: '3 days ago',
      severity: 'info',
      icon: Smartphone,
      color: 'text-blue-400'
    },
    {
      id: 4,
      type: 'security',
      title: 'Security score improved',
      description: 'Your security score increased to 85/100',
      time: '1 week ago',
      severity: 'success',
      icon: TrendingUp,
      color: 'text-green-400'
    }
  ];

  const quickActions = [
    {
      title: 'Add Identity',
      description: 'Monitor new phone or email',
      icon: Eye,
      gradient: 'from-blue-500 to-cyan-500',
      action: () => console.log('Add identity')
    },
    {
      title: 'Check Password',
      description: 'Analyze password strength',
      icon: Lock,
      gradient: 'from-purple-500 to-pink-500',
      action: () => console.log('Check password')
    },
    {
      title: 'Security Tips',
      description: 'Learn best practices',
      icon: Target,
      gradient: 'from-green-500 to-emerald-500',
      action: () => console.log('Security tips')
    },
    {
      title: 'Generate Password',
      description: 'Create secure password',
      icon: Key,
      gradient: 'from-orange-500 to-red-500',
      action: () => console.log('Generate password')
    }
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen animated-bg flex items-center justify-center">
        <div className="glass-card p-8 text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading your security dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen animated-bg p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8 slide-in">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">
                Welcome back, <span className="text-gradient">Demo!</span>
              </h1>
              <p className="text-gray-300 text-lg">Here's your cybersecurity overview and recent activity</p>
            </div>
            <div className="flex items-center space-x-4">
              <button className="btn-secondary flex items-center space-x-2">
                <Bell className="h-5 w-5" />
                <span>Notifications</span>
              </button>
              <button className="btn-primary flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Settings</span>
              </button>
            </div>
          </div>
        </div>

        {/* Security Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {securityMetrics.map((metric, index) => {
            const Icon = metric.icon;
            return (
              <div key={index} className="glass-card p-6 hover-lift fade-in">
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 bg-gradient-to-r ${metric.gradient} rounded-xl flex items-center justify-center`}>
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  {metric.status === 'excellent' && <Award className="h-5 w-5 text-yellow-400" />}
                  {metric.status === 'warning' && <AlertTriangle className="h-5 w-5 text-red-400" />}
                </div>
                
                <div className="mb-3">
                  <div className="flex items-baseline space-x-2">
                    <span className="text-3xl font-bold text-white">
                      {metric.value}
                    </span>
                    {metric.max && (
                      <span className="text-gray-400">/ {metric.max}</span>
                    )}
                    {metric.total && (
                      <span className="text-gray-400">/ {metric.total}</span>
                    )}
                  </div>
                  <h3 className="text-gray-300 font-medium">{metric.title}</h3>
                </div>

                {metric.max && (
                  <div className="progress-bar mb-3">
                    <div 
                      className="progress-fill" 
                      style={{ width: `${(metric.value / metric.max) * 100}%` }}
                    ></div>
                  </div>
                )}

                <p className="text-sm text-gray-400">{metric.description}</p>
              </div>
            );
          })}
        </div>

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <div className="glass-card p-6 hover-lift">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white flex items-center space-x-3">
                  <Activity className="h-6 w-6 text-blue-400" />
                  <span>Recent Activity</span>
                </h2>
                <button className="btn-secondary text-sm">View All</button>
              </div>

              <div className="space-y-4">
                {recentActivity.map((activity) => {
                  const Icon = activity.icon;
                  return (
                    <div key={activity.id} className="flex items-start space-x-4 p-4 rounded-xl bg-white/5 hover:bg-white/10 transition-all duration-300">
                      <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${
                        activity.severity === 'high' ? 'from-red-500 to-pink-500' :
                        activity.severity === 'medium' ? 'from-yellow-500 to-orange-500' :
                        activity.severity === 'success' ? 'from-green-500 to-emerald-500' :
                        'from-blue-500 to-cyan-500'
                      } flex items-center justify-center`}>
                        <Icon className="h-5 w-5 text-white" />
                      </div>
                      
                      <div className="flex-1">
                        <h3 className="text-white font-medium mb-1">{activity.title}</h3>
                        <p className="text-gray-400 text-sm mb-2">{activity.description}</p>
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-gray-500" />
                          <span className="text-gray-500 text-xs">{activity.time}</span>
                        </div>
                      </div>

                      <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                        activity.severity === 'high' ? 'bg-red-500/20 text-red-300' :
                        activity.severity === 'medium' ? 'bg-yellow-500/20 text-yellow-300' :
                        activity.severity === 'success' ? 'bg-green-500/20 text-green-300' :
                        'bg-blue-500/20 text-blue-300'
                      }`}>
                        {activity.severity}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-6">
            {/* Quick Actions Card */}
            <div className="glass-card p-6 hover-lift">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-3">
                <Zap className="h-5 w-5 text-yellow-400" />
                <span>Quick Actions</span>
              </h2>

              <div className="grid grid-cols-2 gap-4">
                {quickActions.map((action, index) => {
                  const Icon = action.icon;
                  return (
                    <button
                      key={index}
                      onClick={action.action}
                      className="p-4 rounded-xl bg-white/5 hover:bg-white/10 transition-all duration-300 text-left group"
                    >
                      <div className={`w-8 h-8 bg-gradient-to-r ${action.gradient} rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300`}>
                        <Icon className="h-4 w-4 text-white" />
                      </div>
                      <h3 className="text-white font-medium text-sm mb-1">{action.title}</h3>
                      <p className="text-gray-400 text-xs">{action.description}</p>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Security Status */}
            <div className="glass-card p-6 hover-lift">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-3">
                <Shield className="h-5 w-5 text-green-400" />
                <span>Security Status</span>
              </h2>

              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 rounded-lg bg-green-500/10">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-400" />
                    <span className="text-white text-sm">Protected</span>
                  </div>
                  <span className="badge badge-success">Active</span>
                </div>

                <div className="flex items-center justify-between p-3 rounded-lg bg-blue-500/10">
                  <div className="flex items-center space-x-3">
                    <Eye className="h-5 w-5 text-blue-400" />
                    <span className="text-white text-sm">Monitoring</span>
                  </div>
                  <span className="badge badge-success">24/7</span>
                </div>

                <div className="flex items-center justify-between p-3 rounded-lg bg-yellow-500/10">
                  <div className="flex items-center space-x-3">
                    <AlertTriangle className="h-5 w-5 text-yellow-400" />
                    <span className="text-white text-sm">Alerts</span>
                  </div>
                  <span className="badge badge-warning">2 New</span>
                </div>
              </div>
            </div>

            {/* Security Score Chart */}
            <div className="glass-card p-6 hover-lift">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center space-x-3">
                <BarChart3 className="h-5 w-5 text-purple-400" />
                <span>Score Breakdown</span>
              </h2>

              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-300">Identity Security</span>
                    <span className="text-white">90%</span>
                  </div>
                  <div className="progress-bar">
                    <div className="progress-fill" style={{ width: '90%' }}></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-300">Password Strength</span>
                    <span className="text-white">85%</span>
                  </div>
                  <div className="progress-bar">
                    <div className="progress-fill" style={{ width: '85%' }}></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-300">Behavior Score</span>
                    <span className="text-white">80%</span>
                  </div>
                  <div className="progress-bar">
                    <div className="progress-fill" style={{ width: '80%' }}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Stats */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="glass-card p-6 text-center hover-lift">
            <Globe className="h-8 w-8 mx-auto mb-4 text-blue-400" />
            <div className="text-2xl font-bold text-white mb-2">Global</div>
            <div className="text-gray-300 text-sm">Worldwide monitoring coverage</div>
          </div>

          <div className="glass-card p-6 text-center hover-lift">
            <Users className="h-8 w-8 mx-auto mb-4 text-green-400" />
            <div className="text-2xl font-bold text-white mb-2">50M+</div>
            <div className="text-gray-300 text-sm">Identities protected globally</div>
          </div>

          <div className="glass-card p-6 text-center hover-lift">
            <Zap className="h-8 w-8 mx-auto mb-4 text-yellow-400" />
            <div className="text-2xl font-bold text-white mb-2">Real-time</div>
            <div className="text-gray-300 text-sm">Instant threat detection</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;

