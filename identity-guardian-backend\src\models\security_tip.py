from datetime import datetime
from enum import Enum
from src import db

class TipCategory(Enum):
    """Enumeration for security tip categories."""
    PASSWORD_SECURITY = 'password_security'
    PHISHING_PROTECTION = 'phishing_protection'
    SOCIAL_ENGINEERING = 'social_engineering'
    MALWARE_PROTECTION = 'malware_protection'
    PRIVACY_SETTINGS = 'privacy_settings'
    TWO_FACTOR_AUTH = 'two_factor_auth'
    SAFE_BROWSING = 'safe_browsing'
    EMAIL_SECURITY = 'email_security'
    MOBILE_SECURITY = 'mobile_security'
    FINANCIAL_SECURITY = 'financial_security'
    IDENTITY_PROTECTION = 'identity_protection'
    GENERAL_AWARENESS = 'general_awareness'

class TipPriority(Enum):
    """Enumeration for tip priority levels."""
    LOW = 'low'
    MEDIUM = 'medium'
    HIGH = 'high'
    CRITICAL = 'critical'

class SecurityTip(db.Model):
    """Security tip model for cybersecurity awareness content."""
    
    __tablename__ = 'security_tips'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Tip content
    title = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text, nullable=False)
    summary = db.Column(db.String(500))  # Short summary for cards/previews
    
    # Categorization
    category = db.Column(db.Enum(TipCategory), nullable=False, index=True)
    priority = db.Column(db.Enum(TipPriority), default=TipPriority.MEDIUM, nullable=False)
    tags = db.Column(db.JSON)  # Additional tags for filtering
    
    # Content details
    difficulty_level = db.Column(db.String(20), default='beginner')  # beginner, intermediate, advanced
    estimated_read_time = db.Column(db.Integer, default=2)  # minutes
    action_items = db.Column(db.JSON)  # List of actionable steps
    
    # Engagement
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    view_count = db.Column(db.Integer, default=0, nullable=False)
    helpful_count = db.Column(db.Integer, default=0, nullable=False)
    
    # External resources
    external_links = db.Column(db.JSON)  # Links to additional resources
    video_url = db.Column(db.String(500))  # Optional video content
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __init__(self, title, content, category, priority=TipPriority.MEDIUM):
        self.title = title.strip()
        self.content = content.strip()
        self.category = category
        self.priority = priority
    
    def increment_view_count(self):
        """Increment view count for analytics."""
        self.view_count += 1
        db.session.commit()
    
    def increment_helpful_count(self):
        """Increment helpful count when user finds tip useful."""
        self.helpful_count += 1
        db.session.commit()
    
    def add_action_items(self, items):
        """Add actionable items to the tip."""
        if not isinstance(items, list):
            items = [items]
        self.action_items = items
        self.updated_at = datetime.utcnow()
    
    def add_external_links(self, links):
        """Add external resource links."""
        if not isinstance(links, list):
            links = [links]
        self.external_links = links
        self.updated_at = datetime.utcnow()
    
    @property
    def engagement_score(self):
        """Calculate engagement score based on views and helpful votes."""
        if self.view_count == 0:
            return 0
        return (self.helpful_count / self.view_count) * 100
    
    @property
    def priority_score(self):
        """Get numeric priority score (1-4)."""
        priority_scores = {
            TipPriority.LOW: 1,
            TipPriority.MEDIUM: 2,
            TipPriority.HIGH: 3,
            TipPriority.CRITICAL: 4
        }
        return priority_scores.get(self.priority, 2)
    
    def to_dict(self, include_analytics=False):
        """Convert security tip to dictionary."""
        data = {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'summary': self.summary,
            'category': self.category.value,
            'priority': self.priority.value,
            'priority_score': self.priority_score,
            'tags': self.tags or [],
            'difficulty_level': self.difficulty_level,
            'estimated_read_time': self.estimated_read_time,
            'action_items': self.action_items or [],
            'external_links': self.external_links or [],
            'video_url': self.video_url,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_analytics:
            data.update({
                'view_count': self.view_count,
                'helpful_count': self.helpful_count,
                'engagement_score': self.engagement_score
            })
        
        return data
    
    def __repr__(self):
        return f'<SecurityTip {self.title[:50]}...>'

class UserTipInteraction(db.Model):
    """Track user interactions with security tips."""
    
    __tablename__ = 'user_tip_interactions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    tip_id = db.Column(db.Integer, db.ForeignKey('security_tips.id'), nullable=False, index=True)
    
    # Interaction details
    viewed_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    marked_helpful = db.Column(db.Boolean, default=False)
    completed_actions = db.Column(db.JSON)  # List of completed action items
    notes = db.Column(db.Text)  # User's personal notes
    
    # Relationships
    user = db.relationship('User', backref='tip_interactions')
    tip = db.relationship('SecurityTip', backref='user_interactions')
    
    def __init__(self, user_id, tip_id):
        self.user_id = user_id
        self.tip_id = tip_id
    
    def mark_helpful(self):
        """Mark tip as helpful."""
        if not self.marked_helpful:
            self.marked_helpful = True
            self.tip.increment_helpful_count()
    
    def complete_action(self, action_index):
        """Mark an action item as completed."""
        if self.completed_actions is None:
            self.completed_actions = []
        
        if action_index not in self.completed_actions:
            self.completed_actions.append(action_index)
    
    def to_dict(self):
        """Convert interaction to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'tip_id': self.tip_id,
            'viewed_at': self.viewed_at.isoformat() if self.viewed_at else None,
            'marked_helpful': self.marked_helpful,
            'completed_actions': self.completed_actions or [],
            'notes': self.notes
        }
    
    def __repr__(self):
        return f'<UserTipInteraction User:{self.user_id} Tip:{self.tip_id}>'

