import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Shield, 
  Eye, 
  Lock, 
  Globe, 
  Zap, 
  CheckCircle, 
  ArrowRight,
  Star,
  Users,
  Award,
  TrendingUp,
  Smartphone,
  Mail,
  Key,
  AlertTriangle,
  Brain,
  Target
} from 'lucide-react';

const LandingPage = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentStat, setCurrentStat] = useState(0);

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setCurrentStat(prev => (prev + 1) % 4);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const stats = [
    { number: '50M+', label: 'Breaches Monitored', icon: Shield },
    { number: '99.9%', label: 'Uptime Guarantee', icon: TrendingUp },
    { number: '24/7', label: 'Real-time Monitoring', icon: Eye },
    { number: '256-bit', label: 'Encryption Standard', icon: Lock }
  ];

  const features = [
    {
      icon: Eye,
      title: 'Identity Monitoring',
      description: 'Monitor your phone numbers and email addresses for breaches and unauthorized use across the dark web and breach databases.',
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Lock,
      title: 'Password Security',
      description: 'Analyze password strength, detect compromised passwords, and generate cryptographically secure passwords and passphrases.',
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      icon: Zap,
      title: 'Real-time Alerts',
      description: 'Get instant notifications via email and SMS when your identities are found in new breaches or suspicious activities.',
      gradient: 'from-orange-500 to-red-500'
    },
    {
      icon: Brain,
      title: 'Security Education',
      description: 'Learn cybersecurity best practices with personalized tips, phishing protection guides, and threat awareness content.',
      gradient: 'from-green-500 to-emerald-500'
    },
    {
      icon: Globe,
      title: 'Global Coverage',
      description: 'Monitor identities worldwide with support for international phone numbers and comprehensive breach database coverage.',
      gradient: 'from-indigo-500 to-purple-500'
    },
    {
      icon: Target,
      title: 'Advanced Analytics',
      description: 'Get detailed risk assessments, trend analysis, and actionable insights to improve your security posture.',
      gradient: 'from-pink-500 to-rose-500'
    }
  ];

  const securityTips = [
    'Never reuse passwords across multiple accounts',
    'Enable two-factor authentication on all important accounts',
    'Be suspicious of urgent emails requesting personal information',
    'Keep your software and devices updated with latest security patches',
    'Use a password manager to generate and store strong passwords'
  ];

  return (
    <div className="min-h-screen animated-bg">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 glass-card border-0 border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center glow-effect">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">Identity Guardian</h1>
                <span className="text-xs bg-gradient-accent text-white px-2 py-1 rounded-full">White Hat</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/login" className="nav-link">
                Sign In
              </Link>
              <Link to="/register" className="btn-primary">
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className={`transition-all duration-1000 ${isVisible ? 'slide-in' : 'opacity-0'}`}>
              <h1 className="text-5xl md:text-7xl font-bold mb-6 heading-gradient">
                Protect Your Digital Identity
              </h1>
              <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed">
                Advanced cybersecurity monitoring for your phone numbers, emails, and passwords. 
                Get real-time alerts, security education, and comprehensive protection against 
                identity theft and cyber threats.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Link to="/register" className="btn-primary text-lg px-8 py-4 flex items-center space-x-2 glow-effect">
                  <span>Start Free Monitoring</span>
                  <ArrowRight className="h-5 w-5" />
                </Link>
                <button className="btn-secondary text-lg px-8 py-4 flex items-center space-x-2">
                  <span>View Demo</span>
                  <Eye className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Animated Stats Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div 
                  key={index} 
                  className={`glass-card p-6 text-center hover-lift ${
                    currentStat === index ? 'glow-effect scale-in' : ''
                  }`}
                >
                  <Icon className="h-8 w-8 mx-auto mb-4 text-blue-400" />
                  <div className="text-3xl font-bold text-white mb-2">{stat.number}</div>
                  <div className="text-gray-300 text-sm">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Comprehensive Cybersecurity Protection
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Identity Guardian provides cutting-edge security monitoring and education to keep you safe from cyber threats and identity theft.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="glass-card p-8 hover-lift fade-in group">
                  <div className={`w-16 h-16 bg-gradient-to-r ${feature.gradient} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">{feature.title}</h3>
                  <p className="text-gray-300 leading-relaxed">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Security Tips Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-white mb-6">Essential Security Tips</h2>
            <p className="text-xl text-gray-300">Follow these cybersecurity best practices to stay protected online</p>
          </div>
          
          <div className="space-y-4">
            {securityTips.map((tip, index) => (
              <div key={index} className="glass-card p-6 flex items-start space-x-4 hover-lift">
                <CheckCircle className="h-6 w-6 text-green-400 mt-1 flex-shrink-0" />
                <p className="text-gray-300 text-lg">{tip}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="glass-card p-12 glow-effect">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to Secure Your Digital Identity?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of users who trust Identity Guardian to protect their digital lives. 
              Start monitoring your identities today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/register" className="btn-primary text-lg px-8 py-4 flex items-center justify-center space-x-2 glow-effect">
                <Shield className="h-5 w-5" />
                <span>Start Free Protection</span>
              </Link>
              <Link to="/login" className="btn-secondary text-lg px-8 py-4 flex items-center justify-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Sign In to Dashboard</span>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 sm:px-6 lg:px-8 border-t border-white/10">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-3 mb-4 md:mb-0">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Shield className="h-5 w-5 text-white" />
              </div>
              <span className="text-white font-semibold">Identity Guardian</span>
            </div>
            <div className="text-gray-400 text-sm">
              Protected by 256-bit encryption and advanced security measures
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;

