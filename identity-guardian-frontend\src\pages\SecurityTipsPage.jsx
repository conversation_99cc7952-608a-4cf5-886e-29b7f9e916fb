import React, { useState } from 'react';
import Layout from '../components/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { 
  BookOpen, 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Search,
  Heart,
  Eye,
  Lock,
  Mail,
  Smartphone,
  Globe,
  Users,
  Zap,
  Star,
  TrendingUp,
  Play
} from 'lucide-react';
import { toast } from 'sonner';

const SecurityTipsPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [likedTips, setLikedTips] = useState(new Set());

  const categories = [
    { id: 'all', name: 'All Tips', icon: BookOpen },
    { id: 'passwords', name: 'Password Security', icon: Lock },
    { id: 'phishing', name: 'Phishing Protection', icon: Mail },
    { id: 'mobile', name: 'Mobile Security', icon: Smartphone },
    { id: 'browsing', name: 'Safe Browsing', icon: Globe },
    { id: 'social', name: 'Social Engineering', icon: Users },
    { id: 'general', name: 'General Awareness', icon: Shield }
  ];

  const securityTips = [
    {
      id: 1,
      title: 'Use Strong, Unique Passwords',
      category: 'passwords',
      priority: 'critical',
      readTime: 3,
      likes: 245,
      content: `Creating strong, unique passwords is your first line of defense against cyber attacks. A strong password should be at least 12 characters long and include a mix of uppercase letters, lowercase letters, numbers, and special characters.

Never reuse passwords across multiple accounts. If one account gets compromised, attackers could access all your other accounts using the same password.

Consider using a passphrase - a series of random words that are easy for you to remember but hard for others to guess. For example: "Coffee-Mountain-Purple-42" is much stronger than "P@ssw0rd123".`,
      actionItems: [
        'Audit all your current passwords',
        'Change any weak or reused passwords',
        'Set up a password manager',
        'Enable two-factor authentication where possible'
      ],
      tags: ['passwords', 'authentication', 'security']
    },
    {
      id: 2,
      title: 'Recognize Phishing Attempts',
      category: 'phishing',
      priority: 'critical',
      readTime: 4,
      likes: 189,
      content: `Phishing is one of the most common ways attackers steal personal information. These attacks often come through email, text messages, or fake websites that look legitimate.

Warning signs of phishing:
- Urgent language ("Act now!" or "Your account will be closed!")
- Requests for personal information via email
- Suspicious sender addresses that don't match the claimed organization
- Links that don't match the legitimate website URL
- Poor grammar or spelling errors

Always verify requests independently. If you receive an email claiming to be from your bank, don't click the link - instead, go directly to your bank's website or call them directly.`,
      actionItems: [
        'Learn to identify suspicious emails',
        'Verify requests through official channels',
        'Never click suspicious links',
        'Report phishing attempts to your IT department'
      ],
      tags: ['phishing', 'email', 'social engineering']
    },
    {
      id: 3,
      title: 'Enable Two-Factor Authentication',
      category: 'passwords',
      priority: 'high',
      readTime: 2,
      likes: 156,
      content: `Two-factor authentication (2FA) adds an extra layer of security to your accounts by requiring something you know (your password) and something you have (your phone or a security key).

Even if someone steals your password, they won't be able to access your account without the second factor. This makes your accounts significantly more secure.

Types of 2FA:
- SMS codes (better than nothing, but not the most secure)
- Authenticator apps like Google Authenticator or Authy (more secure)
- Hardware security keys (most secure)`,
      actionItems: [
        'Enable 2FA on your email accounts',
        'Set up 2FA for banking and financial accounts',
        'Install an authenticator app',
        'Consider getting a hardware security key'
      ],
      tags: ['2fa', 'authentication', 'security']
    },
    {
      id: 4,
      title: 'Keep Software Updated',
      category: 'general',
      priority: 'high',
      readTime: 3,
      likes: 134,
      content: `Software updates often include critical security patches that fix vulnerabilities. Cybercriminals actively exploit these vulnerabilities, so keeping your software updated is essential.

What to keep updated:
- Operating system (Windows, macOS, Linux)
- Web browsers (Chrome, Firefox, Safari, Edge)
- Antivirus software
- Mobile apps
- Router firmware

Enable automatic updates whenever possible. This ensures you get security patches as soon as they're available without having to remember to check manually.`,
      actionItems: [
        'Enable automatic updates on all devices',
        'Check for router firmware updates',
        'Update mobile apps regularly',
        'Keep antivirus software current'
      ],
      tags: ['updates', 'software', 'patches']
    },
    {
      id: 5,
      title: 'Be Cautious with Public Wi-Fi',
      category: 'browsing',
      priority: 'medium',
      readTime: 3,
      likes: 98,
      content: `Public Wi-Fi networks are convenient but can be dangerous. Attackers can easily intercept data transmitted over unsecured networks, potentially stealing passwords, personal information, and other sensitive data.

Safety tips for public Wi-Fi:
- Avoid accessing sensitive accounts (banking, email) on public networks
- Use a VPN (Virtual Private Network) to encrypt your connection
- Turn off automatic Wi-Fi connection on your devices
- Verify the network name with staff before connecting
- Use your phone's hotspot instead when possible`,
      actionItems: [
        'Install a reputable VPN app',
        'Disable auto-connect to Wi-Fi networks',
        'Use mobile data for sensitive activities',
        'Always verify network names with staff'
      ],
      tags: ['wifi', 'vpn', 'public networks']
    },
    {
      id: 6,
      title: 'Secure Your Mobile Devices',
      category: 'mobile',
      priority: 'medium',
      readTime: 4,
      likes: 87,
      content: `Mobile devices contain vast amounts of personal information and are often targets for thieves and hackers. Securing your phone or tablet is crucial for protecting your data.

Mobile security essentials:
- Use a strong lock screen (PIN, password, fingerprint, or face unlock)
- Keep your device's operating system updated
- Only download apps from official app stores
- Review app permissions carefully
- Enable remote wipe capabilities
- Use encryption if available

Be cautious about what you do on your mobile device in public. Shoulder surfing (people watching you enter passwords) is a real threat in crowded areas.`,
      actionItems: [
        'Set up a strong lock screen',
        'Enable automatic device locking',
        'Set up remote wipe capabilities',
        'Review and limit app permissions'
      ],
      tags: ['mobile', 'smartphone', 'apps']
    },
    {
      id: 7,
      title: 'Think Before You Click',
      category: 'browsing',
      priority: 'high',
      readTime: 2,
      likes: 167,
      content: `Many cyber attacks succeed because people click on malicious links or download infected files. Developing good clicking habits can prevent most malware infections.

Before clicking any link, ask yourself:
- Do I trust the sender?
- Was I expecting this message?
- Does the link look legitimate?
- Is this too good to be true?

Hover over links to see where they actually lead before clicking. Be especially suspicious of shortened URLs (bit.ly, tinyurl, etc.) that hide the real destination.

Never download software from pop-up ads or unfamiliar websites. Stick to official sources and app stores for downloads.`,
      actionItems: [
        'Hover over links before clicking',
        'Verify sender identity for unexpected messages',
        'Use official sources for software downloads',
        'Install ad blockers to reduce malicious ads'
      ],
      tags: ['clicking', 'links', 'malware']
    },
    {
      id: 8,
      title: 'Protect Your Personal Information',
      category: 'social',
      priority: 'high',
      readTime: 3,
      likes: 123,
      content: `Identity thieves can use surprisingly little information to steal your identity. Be careful about what personal information you share, both online and offline.

Information to protect:
- Social Security Number
- Date of birth
- Full name and address
- Phone numbers
- Financial account numbers
- Passwords and PINs

Be especially careful on social media. Avoid posting information that could be used to answer security questions, such as your pet's name, where you went to school, or your mother's maiden name.

Shred documents containing personal information before throwing them away. Identity thieves still go through trash looking for useful information.`,
      actionItems: [
        'Review your social media privacy settings',
        'Limit personal information in online profiles',
        'Shred sensitive documents',
        'Be cautious about sharing personal details'
      ],
      tags: ['privacy', 'personal information', 'identity theft']
    }
  ];

  const filteredTips = securityTips.filter(tip => {
    const matchesSearch = tip.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tip.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tip.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || tip.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'critical': return 'bg-red-500/20 text-red-300 border-red-500/50';
      case 'high': return 'bg-orange-500/20 text-orange-300 border-orange-500/50';
      case 'medium': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/50';
      case 'low': return 'bg-blue-500/20 text-blue-300 border-blue-500/50';
      default: return 'bg-slate-500/20 text-slate-300 border-slate-500/50';
    }
  };

  const toggleLike = (tipId) => {
    const newLikedTips = new Set(likedTips);
    if (newLikedTips.has(tipId)) {
      newLikedTips.delete(tipId);
      toast.success('Removed from favorites');
    } else {
      newLikedTips.add(tipId);
      toast.success('Added to favorites');
    }
    setLikedTips(newLikedTips);
  };

  const stats = {
    totalTips: securityTips.length,
    criticalTips: securityTips.filter(tip => tip.priority === 'critical').length,
    completedTips: likedTips.size,
    avgReadTime: Math.round(securityTips.reduce((sum, tip) => sum + tip.readTime, 0) / securityTips.length)
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Security Education Center</h1>
            <p className="text-slate-400">
              Learn cybersecurity best practices and stay protected against online threats
            </p>
          </div>
          <div className="flex items-center space-x-2 mt-4 md:mt-0">
            <Badge variant="outline" className="border-purple-500/50 text-purple-300">
              <BookOpen className="w-3 h-3 mr-1" />
              Learning Hub
            </Badge>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Total Tips</CardTitle>
              <BookOpen className="h-4 w-4 text-blue-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.totalTips}</div>
              <p className="text-xs text-slate-400">Available to learn</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Critical Tips</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.criticalTips}</div>
              <p className="text-xs text-slate-400">High priority</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Favorites</CardTitle>
              <Heart className="h-4 w-4 text-pink-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.completedTips}</div>
              <p className="text-xs text-slate-400">Tips you liked</p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-300">Avg Read Time</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{stats.avgReadTime}m</div>
              <p className="text-xs text-slate-400">Per tip</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search security tips..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-slate-700/50 border-slate-600 text-white"
            />
          </div>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => {
              const Icon = category.icon;
              return (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className={selectedCategory === category.id 
                    ? 'bg-purple-600 hover:bg-purple-700' 
                    : 'border-slate-600 text-slate-300 hover:bg-slate-800'
                  }
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {category.name}
                </Button>
              );
            })}
          </div>
        </div>

        {/* Tips Grid */}
        <div className="grid gap-6">
          {filteredTips.map((tip) => (
            <Card key={tip.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge className={getPriorityColor(tip.priority)}>
                        {tip.priority}
                      </Badge>
                      <Badge variant="outline" className="border-slate-600 text-slate-300">
                        {tip.readTime} min read
                      </Badge>
                    </div>
                    <CardTitle className="text-white text-xl mb-2">{tip.title}</CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-slate-400">
                      <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4" />
                        <span>{tip.likes} views</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Heart className={`h-4 w-4 ${likedTips.has(tip.id) ? 'text-pink-400 fill-current' : ''}`} />
                        <span>{tip.likes + (likedTips.has(tip.id) ? 1 : 0)} likes</span>
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleLike(tip.id)}
                    className={`text-slate-400 hover:text-pink-400 ${likedTips.has(tip.id) ? 'text-pink-400' : ''}`}
                  >
                    <Heart className={`h-5 w-5 ${likedTips.has(tip.id) ? 'fill-current' : ''}`} />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-slate-300 leading-relaxed">
                  {tip.content.split('\n\n')[0]}...
                </div>
                
                {tip.actionItems && tip.actionItems.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-white font-medium">Action Items:</h4>
                    <div className="grid md:grid-cols-2 gap-2">
                      {tip.actionItems.slice(0, 4).map((item, index) => (
                        <div key={index} className="flex items-start space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                          <span className="text-slate-300 text-sm">{item}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-between pt-4 border-t border-slate-700">
                  <div className="flex flex-wrap gap-1">
                    {tip.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="outline" className="border-slate-600 text-slate-400 text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                    <Play className="w-4 h-4 mr-2" />
                    Read More
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredTips.length === 0 && (
          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="text-center py-12">
              <BookOpen className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <h3 className="text-white text-lg font-medium mb-2">No tips found</h3>
              <p className="text-slate-400">
                Try adjusting your search terms or category filters
              </p>
            </CardContent>
          </Card>
        )}

        {/* Featured Security Resources */}
        <Card className="bg-gradient-to-r from-purple-900/50 to-blue-900/50 border-purple-500/30">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Star className="w-5 h-5 mr-2 text-yellow-400" />
              Featured Security Resources
            </CardTitle>
            <CardDescription className="text-slate-300">
              Essential tools and resources for staying secure online
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="p-4 bg-slate-800/50 rounded-lg border border-slate-700">
                <Shield className="h-8 w-8 text-purple-400 mb-3" />
                <h4 className="text-white font-medium mb-2">Password Managers</h4>
                <p className="text-slate-300 text-sm">
                  Use tools like Bitwarden, 1Password, or LastPass to generate and store unique passwords
                </p>
              </div>
              <div className="p-4 bg-slate-800/50 rounded-lg border border-slate-700">
                <Zap className="h-8 w-8 text-yellow-400 mb-3" />
                <h4 className="text-white font-medium mb-2">VPN Services</h4>
                <p className="text-slate-300 text-sm">
                  Protect your internet connection with reputable VPN providers like NordVPN or ExpressVPN
                </p>
              </div>
              <div className="p-4 bg-slate-800/50 rounded-lg border border-slate-700">
                <Eye className="h-8 w-8 text-blue-400 mb-3" />
                <h4 className="text-white font-medium mb-2">Security Scanners</h4>
                <p className="text-slate-300 text-sm">
                  Regular security scans with tools like Malwarebytes or Windows Defender
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default SecurityTipsPage;

