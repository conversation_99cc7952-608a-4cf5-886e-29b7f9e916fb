from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from src.models.password import Password, PasswordStrength, PasswordStatus
from src.services.password_analyzer import Password<PERSON><PERSON>yzer
from src import db

passwords_bp = Blueprint('passwords', __name__)
password_analyzer = PasswordAnalyzer()

@passwords_bp.route('/analyze', methods=['POST'])
@jwt_required()
def analyze_password():
    """Analyze password strength and security."""
    try:
        data = request.get_json()
        password = data.get('password')
        
        if not password:
            return jsonify({'error': 'Password is required'}), 400
        
        # Analyze password strength
        analysis = password_analyzer.analyze_password(password)
        
        # Check if password is compromised
        status, compromise_details = password_analyzer.check_compromised_status(password)
        
        return jsonify({
            'strength_analysis': analysis,
            'compromise_status': status.value,
            'compromise_details': compromise_details,
            'recommendations': analysis['suggestions']
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@passwords_bp.route('/generate', methods=['POST'])
@jwt_required()
def generate_password():
    """Generate a strong password or passphrase."""
    try:
        data = request.get_json() or {}
        password_type = data.get('type', 'password')  # 'password' or 'passphrase'
        length = data.get('length', 16)
        include_symbols = data.get('include_symbols', True)
        
        if password_type == 'passphrase':
            word_count = data.get('word_count', 4)
            separator = data.get('separator', '-')
            generated = password_analyzer.generate_passphrase(word_count, separator)
        else:
            generated = password_analyzer.generate_strong_password(length, include_symbols)
        
        # Analyze the generated password
        analysis = password_analyzer.analyze_password(generated)
        
        return jsonify({
            'password': generated,
            'type': password_type,
            'strength': analysis['strength'].value,
            'score': analysis['score'],
            'entropy': analysis['entropy']
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@passwords_bp.route('/', methods=['GET'])
@jwt_required()
def get_user_passwords():
    """Get all passwords for the current user."""
    try:
        user_id = get_jwt_identity()
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        passwords = Password.query.filter_by(user_id=user_id).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'passwords': [password.to_dict() for password in passwords.items],
            'total': passwords.total,
            'pages': passwords.pages,
            'current_page': page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@passwords_bp.route('/', methods=['POST'])
@jwt_required()
def add_password():
    """Add a new password for monitoring."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        password_plain = data.get('password')
        service_name = data.get('service_name')
        username = data.get('username')
        
        if not password_plain or not service_name:
            return jsonify({'error': 'Password and service name are required'}), 400
        
        # Create password record
        password = Password(
            user_id=user_id,
            password_plain=password_plain,
            service_name=service_name,
            username=username
        )
        
        # Analyze password
        analysis = password_analyzer.analyze_password(password_plain)
        password.update_strength_analysis(
            strength=PasswordStrength(analysis['strength'].value),
            score=analysis['score'],
            analysis=analysis
        )
        
        # Check compromise status
        status, details = password_analyzer.check_compromised_status(password_plain)
        password.update_compromise_status(status, details)
        
        # Add recommendations
        password.add_recommendations(analysis['suggestions'])
        
        db.session.add(password)
        db.session.commit()
        
        return jsonify({
            'message': 'Password added successfully',
            'password': password.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@passwords_bp.route('/<int:password_id>', methods=['PUT'])
@jwt_required()
def update_password(password_id):
    """Update a password record."""
    try:
        user_id = get_jwt_identity()
        password = Password.query.filter_by(id=password_id, user_id=user_id).first()
        
        if not password:
            return jsonify({'error': 'Password not found'}), 404
        
        data = request.get_json()
        
        # Update basic information
        if 'service_name' in data:
            password.service_name = data['service_name']
        if 'username' in data:
            password.username = data['username']
        if 'is_monitored' in data:
            password.is_monitored = data['is_monitored']
        if 'check_frequency' in data:
            password.check_frequency = data['check_frequency']
        
        # If new password provided, re-analyze
        if 'password' in data:
            new_password = data['password']
            password.set_password_hash(new_password)
            
            # Re-analyze
            analysis = password_analyzer.analyze_password(new_password)
            password.update_strength_analysis(
                strength=PasswordStrength(analysis['strength'].value),
                score=analysis['score'],
                analysis=analysis
            )
            
            # Re-check compromise status
            status, details = password_analyzer.check_compromised_status(new_password)
            password.update_compromise_status(status, details)
            
            password.add_recommendations(analysis['suggestions'])
        
        db.session.commit()
        
        return jsonify({
            'message': 'Password updated successfully',
            'password': password.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@passwords_bp.route('/<int:password_id>', methods=['DELETE'])
@jwt_required()
def delete_password(password_id):
    """Delete a password record."""
    try:
        user_id = get_jwt_identity()
        password = Password.query.filter_by(id=password_id, user_id=user_id).first()
        
        if not password:
            return jsonify({'error': 'Password not found'}), 404
        
        db.session.delete(password)
        db.session.commit()
        
        return jsonify({'message': 'Password deleted successfully'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@passwords_bp.route('/<int:password_id>/recheck', methods=['POST'])
@jwt_required()
def recheck_password(password_id):
    """Re-check a password for compromise status."""
    try:
        user_id = get_jwt_identity()
        password = Password.query.filter_by(id=password_id, user_id=user_id).first()
        
        if not password:
            return jsonify({'error': 'Password not found'}), 404
        
        # Note: We can't re-check the actual password since we only store the hash
        # This endpoint would be used to update monitoring settings or force a check
        # In a real implementation, you might store encrypted passwords or use a different approach
        
        return jsonify({
            'message': 'Password recheck initiated',
            'password': password.to_dict()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@passwords_bp.route('/dashboard', methods=['GET'])
@jwt_required()
def password_dashboard():
    """Get password security dashboard data."""
    try:
        user_id = get_jwt_identity()
        
        # Get all user passwords
        passwords = Password.query.filter_by(user_id=user_id).all()
        
        # Calculate statistics
        total_passwords = len(passwords)
        compromised_count = sum(1 for p in passwords if p.is_compromised)
        weak_count = sum(1 for p in passwords if p.is_weak)
        needs_attention = sum(1 for p in passwords if p.needs_attention)
        
        # Average strength score
        avg_strength = sum(p.strength_score for p in passwords) / total_passwords if total_passwords > 0 else 0
        
        # Strength distribution
        strength_distribution = {}
        for strength in PasswordStrength:
            strength_distribution[strength.value] = sum(1 for p in passwords if p.strength == strength)
        
        # Recent additions
        recent_passwords = sorted(passwords, key=lambda p: p.created_at, reverse=True)[:5]
        
        return jsonify({
            'total_passwords': total_passwords,
            'compromised_count': compromised_count,
            'weak_count': weak_count,
            'needs_attention': needs_attention,
            'average_strength_score': round(avg_strength, 1),
            'strength_distribution': strength_distribution,
            'recent_passwords': [p.to_dict() for p in recent_passwords],
            'security_score': max(0, 100 - (compromised_count * 20) - (weak_count * 10))
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

